'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/use-toast';
import {
  Calendar,
  Search,
  Loader2,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  Store,
  Filter,
  CalendarDays
} from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';

interface Event {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  venue: string;
  city: string;
  status: string;
  capacity: number;
  attendeeCount: number;
  category: string;
  image?: string;
}

interface VendorProfile {
  id: string;
  businessName: string;
  verificationStatus: string;
}

export default function VendorEventApplicationsPage() {
  const router = useRouter();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [vendorProfile, setVendorProfile] = useState<VendorProfile | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [applicationNote, setApplicationNote] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [appliedEventIds, setAppliedEventIds] = useState<string[]>([]);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Fetch vendor profile
        const profileResponse = await fetch('/api/vendors/profile');

        if (!profileResponse.ok) {
          if (profileResponse.status === 404) {
            router.push('/dashboard/vendor/create-profile');
            return;
          }
          throw new Error('Failed to fetch vendor profile');
        }

        const profileData = await profileResponse.json();
        setVendorProfile(profileData);

        // Check if vendor is verified
        if (profileData.verificationStatus !== 'APPROVED') {
          setError('Your vendor account is not verified. Please complete the verification process to apply for events.');
          setLoading(false);
          return;
        }

        // Fetch events the vendor has already applied to
        const appliedResponse = await fetch('/api/vendors/events/applied');

        if (!appliedResponse.ok) {
          throw new Error('Failed to fetch applied events');
        }

        const appliedData = await appliedResponse.json();
        setAppliedEventIds(appliedData.appliedEventIds || []);

        // Fetch available events
        const eventsResponse = await fetch('/api/vendors/events/available');

        if (!eventsResponse.ok) {
          throw new Error('Failed to fetch available events');
        }

        const eventsData = await eventsResponse.json();
        setEvents(eventsData);

      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load available events',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [router]);

  const filteredEvents = events.filter(event => {
    // First, exclude events the vendor has already applied to
    if (appliedEventIds.includes(event.id)) {
      return false;
    }

    // Then apply search filter if there's a query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        event.title.toLowerCase().includes(query) ||
        event.venue.toLowerCase().includes(query) ||
        event.city.toLowerCase().includes(query) ||
        event.category.toLowerCase().includes(query)
      );
    }

    return true;
  });

  const handleApply = async () => {
    if (!selectedEvent || !vendorProfile) return;

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/vendors/events/apply', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventId: selectedEvent.id,
          notes: applicationNote,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit application');
      }

      toast({
        title: 'Application submitted',
        description: `Your application for ${selectedEvent.title} has been submitted successfully.`,
        variant: 'default',
      });

      // Add the event ID to the applied list
      setAppliedEventIds([...appliedEventIds, selectedEvent.id]);

      // Remove the event from the filtered list
      setEvents(events.filter(event => event.id !== selectedEvent.id));
      setSelectedEvent(null);
      setApplicationNote('');

    } catch (error) {
      console.error('Application error:', error);
      toast({
        title: 'Application failed',
        description: error instanceof Error ? error.message : 'An error occurred while submitting your application',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading available events...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center">
            <XCircle className="mr-2 h-5 w-5" />
            Error Loading Events
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
          {vendorProfile && vendorProfile.verificationStatus !== 'APPROVED' && (
            <div className="mt-4 bg-yellow-50 p-4 rounded-md">
              <div className="flex items-start">
                <Clock className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                <div>
                  <h4 className="font-medium text-yellow-700">Verification Required</h4>
                  <p className="text-yellow-600 text-sm mt-1">
                    Your vendor account needs to be verified before you can apply for events.
                  </p>
                  <Button
                    variant="link"
                    className="p-0 h-auto text-blue-600 mt-2"
                    onClick={() => router.push('/dashboard/vendor/verification')}
                  >
                    Complete verification
                  </Button>
                </div>
              </div>
            </div>
          )}
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/dashboard/vendor')}
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Apply for Events</h1>
          <p className="text-gray-600 mt-1">
            Find and apply to participate as a vendor in upcoming events
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Available Events</CardTitle>
          <CardDescription>
            Browse events that are accepting vendor applications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
            <div className="relative w-full md:w-1/3">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                placeholder="Search events..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>

            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/vendor/events/my-applications')}
                size="sm"
              >
                <Store className="mr-1 h-4 w-4" />
                My Applications
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard/vendor/events')}
                size="sm"
              >
                <CalendarDays className="mr-1 h-4 w-4" />
                My Events
              </Button>
            </div>
          </div>

          {filteredEvents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CalendarDays className="h-12 w-12 mx-auto text-gray-300 mb-4" />
              <h3 className="text-lg font-medium mb-2">No Available Events</h3>
              <p className="mb-4">There are no events currently accepting vendor applications</p>
              {searchQuery && (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery('')}
                >
                  Clear Search
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredEvents.map((event) => (
                    <TableRow key={event.id}>
                      <TableCell className="font-medium">{event.title}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-gray-500" />
                          <span>
                            {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1 text-gray-500" />
                          <span>{event.venue}, {event.city}</span>
                        </div>
                      </TableCell>
                      <TableCell>{event.category}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {event.attendeeCount}/{event.capacity}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              size="sm"
                              onClick={() => setSelectedEvent(event)}
                            >
                              Apply
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[525px]">
                            <DialogHeader>
                              <DialogTitle>Apply as Vendor</DialogTitle>
                              <DialogDescription>
                                Submit your application to participate as a vendor at this event.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                              <div className="mb-4">
                                <h3 className="font-medium text-lg">{event.title}</h3>
                                <div className="flex items-center text-sm text-gray-500 mt-1">
                                  <Calendar className="h-4 w-4 mr-1" />
                                  <span>
                                    {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="flex items-center text-sm text-gray-500 mt-1">
                                  <MapPin className="h-4 w-4 mr-1" />
                                  <span>{event.venue}, {event.city}</span>
                                </div>
                              </div>

                              <div className="space-y-4 mt-4">
                                <div className="space-y-2">
                                  <Label htmlFor="applicationNote">Application Note (Optional)</Label>
                                  <Textarea
                                    id="applicationNote"
                                    placeholder="Describe what you'll be selling, any special requirements, etc."
                                    value={applicationNote}
                                    onChange={(e) => setApplicationNote(e.target.value)}
                                    rows={4}
                                  />
                                </div>
                              </div>
                            </div>
                            <DialogFooter>
                              <Button
                                variant="outline"
                                onClick={() => {
                                  setSelectedEvent(null);
                                  setApplicationNote('');
                                }}
                              >
                                Cancel
                              </Button>
                              <Button
                                onClick={handleApply}
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? (
                                  <>
                                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                    Submitting...
                                  </>
                                ) : (
                                  'Submit Application'
                                )}
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
