

'use server'

import { generateVerificationToken } from "@/data/tokens";
import { getUserByEmail, getUserById } from "@/data/user";
import { currentUser } from "@/lib/auth";
import { sendVerificationEmail } from "@/lib/mail";
import { db } from "@/lib/prisma";
import { SettingsSchema } from "@/schemas";
import { compare, hash } from "bcryptjs";
import fs from "fs/promises";
import crypto from 'crypto';

export async function settings(values: SettingsSchema) {
  try {
    const user = await currentUser();

    if (!user || !user.id) {
      return { error: 'Unauthorized' }
    }

    const dbUser = await getUserById(user.id);

    if (!dbUser) {
      return { error: 'Unauthorized' }
    }

    if (user.isOAuth) {
      values.email = undefined;
      values.password = undefined;
      values.newPassword = undefined;
      values.isTwoFactorEnabled = undefined;
    }

    // Restrict role changes to allowed roles
    if (values.role) {
      // Check if user is trying to set a restricted role
      const restrictedRoles = ['ADMIN', 'SUPERADMIN'];

      // If current role is restricted, don't allow changing it
      if (restrictedRoles.includes(dbUser.role)) {
        values.role = dbUser.role; // Keep the current role
      }

      // Don't allow changing to a restricted role
      if (restrictedRoles.includes(values.role)) {
        return { error: 'You cannot set your role to ADMIN or SUPERADMIN' };
      }
    }

    if (values.email && values.email !== user.email) {
      const existingUser = await getUserByEmail(values.email);

      if (existingUser && existingUser.id !== user.id) {
        return { error: 'Email already in use!' }
      }

      const verificationToken = await generateVerificationToken(values.email);
      await sendVerificationEmail(verificationToken.email, verificationToken.token);

      return { success: 'Verification email sent!' }
    }

    if (values.password && values.newPassword && dbUser.password) {
      const passwordsMatch = await compare(values.password, dbUser.password);

      if (!passwordsMatch) {
        return { error: 'Incorrect password!' }
      }

      const hashedPassword = await hash(values.newPassword, 10);
      values.password = hashedPassword;
      values.newPassword = undefined;
    }

    // Handle image upload
    let imageUrl: string | undefined;
    if (values.image instanceof File) {
      if (!values.image.type.startsWith("image/")) {
        return { error: 'File must be an image' };
      }

      await fs.mkdir("public/profiles", { recursive: true });
      const imagePath = `/profiles/${crypto.randomUUID()}-${values.image.name}`;
      await fs.writeFile(
        `public${imagePath}`,
        Buffer.from(await values.image.arrayBuffer())
      );

      imageUrl = imagePath; // Store the uploaded image path
    }

    // Prepare data for update, excluding the File object
    const { image, ...updateData } = values;

    await db.user.update({
      where: { id: dbUser.id },
      data: {
        ...updateData,
        ...(imageUrl && { image: imageUrl }), // Only update image if we have a new one
      }
    });

    return { success: 'Settings updated!' }
  } catch (error) {
    console.error('Error updating settings:', error);
    return { error: 'An unexpected error occurred!' }
  }
}

