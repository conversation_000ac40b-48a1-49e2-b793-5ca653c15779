import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

/**
 * GET /api/admin/finance/reports
 * Get comprehensive financial reports for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    
    // Build date filter for orders
    const orderDateFilter: Prisma.OrderWhereInput = {};
    if (startDate || endDate) {
      orderDateFilter.createdAt = {};
      if (startDate) {
        orderDateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        orderDateFilter.createdAt.lte = endDate;
      }
    }

    // Build date filter for vendor orders
    const vendorOrderDateFilter: Prisma.VendorOrderWhereInput = {};
    if (startDate || endDate) {
      vendorOrderDateFilter.createdAt = {};
      if (startDate) {
        vendorOrderDateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        vendorOrderDateFilter.createdAt.lte = endDate;
      }
    }

    // Build date filter for NFC transactions
    const nfcDateFilter: Prisma.VendorNFCTransactionWhereInput = {};
    if (startDate || endDate) {
      nfcDateFilter.createdAt = {};
      if (startDate) {
        nfcDateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        nfcDateFilter.createdAt.lte = endDate;
      }
    }

    // Get ticket sales data
    const ticketSalesData = await db.order.aggregate({
      where: {
        status: 'Completed',
        ...orderDateFilter
      },
      _sum: {
        totalPrice: true
      },
      _count: {
        id: true
      }
    });

    // Get vendor sales data
    const vendorSalesData = await db.vendorOrder.aggregate({
      where: {
        status: 'Completed',
        ...vendorOrderDateFilter
      },
      _sum: {
        totalAmount: true
      },
      _count: {
        id: true
      }
    });

    // Get NFC transaction data
    const nfcTransactionData = await db.vendorNFCTransaction.aggregate({
      where: {
        status: 'COMPLETED',
        ...nfcDateFilter
      },
      _sum: {
        amount: true
      },
      _count: {
        id: true
      }
    });

    // Calculate platform fees (6% of ticket sales)
    const platformFees = (ticketSalesData._sum.totalPrice || 0) * 0.06;
    
    // Calculate processing fees (3.5% of all transactions)
    const processingFees = (
      (ticketSalesData._sum.totalPrice || 0) + 
      (vendorSalesData._sum.totalAmount || 0) + 
      (nfcTransactionData._sum.amount || 0)
    ) * 0.035;

    // Calculate total revenue
    const totalRevenue = 
      (ticketSalesData._sum.totalPrice || 0) + 
      (vendorSalesData._sum.totalAmount || 0) + 
      (nfcTransactionData._sum.amount || 0);

    // Return the revenue summary
    return NextResponse.json({
      totalRevenue,
      ticketSales: ticketSalesData._sum.totalPrice || 0,
      vendorSales: vendorSalesData._sum.totalAmount || 0,
      nfcTransactions: nfcTransactionData._sum.amount || 0,
      platformFees,
      processingFees,
      ticketCount: ticketSalesData._count.id || 0,
      vendorOrderCount: vendorSalesData._count.id || 0,
      nfcTransactionCount: nfcTransactionData._count.id || 0
    });
  } catch (error) {
    console.error('Error generating financial reports:', error);
    return NextResponse.json(
      { error: 'Failed to generate financial reports' },
      { status: 500 }
    );
  }
}
