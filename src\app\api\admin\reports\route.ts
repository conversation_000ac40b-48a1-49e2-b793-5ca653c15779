import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const reportType = searchParams.get('type') || 'sales';
    const period = searchParams.get('period') || 'month';
    const startDate = searchParams.get('startDate') || null;
    const endDate = searchParams.get('endDate') || null;

    // Define date ranges
    let dateRange: { gte: Date; lte: Date };
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    if (startDate && endDate) {
      // Custom date range
      dateRange = {
        gte: new Date(startDate),
        lte: new Date(endDate)
      };
    } else {
      // Predefined periods
      switch (period) {
        case 'week':
          const lastWeek = new Date(today);
          lastWeek.setDate(lastWeek.getDate() - 7);
          dateRange = { gte: lastWeek, lte: now };
          break;
        case 'month':
          const lastMonth = new Date(today);
          lastMonth.setMonth(lastMonth.getMonth() - 1);
          dateRange = { gte: lastMonth, lte: now };
          break;
        case 'quarter':
          const lastQuarter = new Date(today);
          lastQuarter.setMonth(lastQuarter.getMonth() - 3);
          dateRange = { gte: lastQuarter, lte: now };
          break;
        case 'year':
          const lastYear = new Date(today);
          lastYear.setFullYear(lastYear.getFullYear() - 1);
          dateRange = { gte: lastYear, lte: now };
          break;
        default:
          const defaultMonth = new Date(today);
          defaultMonth.setMonth(defaultMonth.getMonth() - 1);
          dateRange = { gte: defaultMonth, lte: now };
      }
    }

    // Generate report based on type
    let reportData;

    switch (reportType) {
      case 'sales':
        reportData = await generateSalesReport(dateRange);
        break;
      case 'users':
        reportData = await generateUsersReport(dateRange);
        break;
      case 'events':
        reportData = await generateEventsReport(dateRange);
        break;
      case 'tickets':
        reportData = await generateTicketsReport(dateRange);
        break;
      default:
        reportData = await generateSalesReport(dateRange);
    }

    return NextResponse.json(reportData);
  } catch (error) {
    console.error('Error generating report:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Generate sales report with mock data for now
async function generateSalesReport(dateRange: { gte: Date; lte: Date }) {
  // For now, generate mock data
  // In a real implementation, you would query the database

  const startDate = dateRange.gte;
  const endDate = dateRange.lte;
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Generate daily sales data
  const dailySales = Array.from({ length: daysDiff }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    return {
      date: dateStr,
      revenue: Math.floor(Math.random() * 5000) + 1000,
      orders: Math.floor(Math.random() * 50) + 10,
      tickets: Math.floor(Math.random() * 150) + 30
    };
  });

  // Generate category sales data
  const categories = ['MUSIC', 'CONFERENCE', 'SPORTS', 'WEDDING', 'CORPORATE', 'FESTIVAL', 'OTHER'];
  const categorySales = categories.map(category => ({
    category,
    value: Math.floor(Math.random() * 50000) + 10000,
    revenue: Math.floor(Math.random() * 50000) + 10000,
    orders: Math.floor(Math.random() * 500) + 100,
    tickets: Math.floor(Math.random() * 1500) + 300
  }));

  // Generate top events data
  const topEvents = Array.from({ length: 10 }, (_, i) => ({
    eventId: `event-${i + 1}`,
    eventTitle: `Event ${i + 1}`,
    revenue: Math.floor(Math.random() * 10000) + 2000,
    orders: Math.floor(Math.random() * 100) + 20,
    tickets: Math.floor(Math.random() * 300) + 60
  })).sort((a, b) => b.revenue - a.revenue);

  // Calculate summary
  const totalRevenue = dailySales.reduce((sum, day) => sum + day.revenue, 0);
  const totalOrders = dailySales.reduce((sum, day) => sum + day.orders, 0);
  const totalTickets = dailySales.reduce((sum, day) => sum + day.tickets, 0);

  return {
    reportType: 'sales',
    dateRange: {
      start: dateRange.gte.toISOString(),
      end: dateRange.lte.toISOString()
    },
    summary: {
      totalRevenue,
      totalOrders,
      totalTickets,
      averageOrderValue: totalOrders > 0 ? totalRevenue / totalOrders : 0
    },
    dailySales,
    categorySales,
    topEvents
  };
}

// Generate users report with mock data for now
async function generateUsersReport(dateRange: { gte: Date; lte: Date }) {
  // For now, generate mock data
  // In a real implementation, you would query the database

  const startDate = dateRange.gte;
  const endDate = dateRange.lte;
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Generate daily users data
  const dailyUsers = Array.from({ length: daysDiff }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    const count = Math.floor(Math.random() * 20) + 5;
    const verified = Math.floor(count * (Math.random() * 0.5 + 0.3)); // 30-80% verification rate

    return {
      date: dateStr,
      count,
      verified
    };
  });

  // Generate users by role
  const roles = ['USER', 'ORGANIZER', 'VENDOR', 'ADMIN', 'SUPERADMIN','DEVELOPER'];
  const usersByRole = roles.reduce((acc, role) => {
    acc[role] = Math.floor(Math.random() * 100) + (role === 'USER' ? 500 : 20);
    return acc;
  }, {} as Record<string, number>);

  // Generate recent users
  const recentUsers = Array.from({ length: 10 }, (_, i) => {
    const date = new Date(endDate);
    date.setDate(date.getDate() - Math.floor(Math.random() * 10));

    return {
      id: `user-${i + 1}`,
      name: `User ${i + 1}`,
      email: `user${i + 1}@example.com`,
      role: roles[Math.floor(Math.random() * roles.length)],
      createdAt: date.toISOString(),
      isVerified: Math.random() > 0.3 // 70% chance of being verified
    };
  }).sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

  // Calculate summary
  const totalUsers = Object.values(usersByRole).reduce((sum, count) => sum + count, 0);
  const verifiedUsers = Math.floor(totalUsers * 0.7); // Assume 70% verification rate

  return {
    reportType: 'users',
    dateRange: {
      start: dateRange.gte.toISOString(),
      end: dateRange.lte.toISOString()
    },
    summary: {
      totalUsers,
      verifiedUsers,
      verificationRate: verifiedUsers / totalUsers
    },
    usersByRole,
    dailyUsers,
    recentUsers
  };
}

// Generate events report with mock data for now
async function generateEventsReport(dateRange: { gte: Date; lte: Date }) {
  // For now, generate mock data
  // In a real implementation, you would query the database

  const startDate = dateRange.gte;
  const endDate = dateRange.lte;
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Generate daily events data
  const dailyEvents = Array.from({ length: daysDiff }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    const count = Math.floor(Math.random() * 5) + 1;
    const published = Math.floor(count * (Math.random() * 0.6 + 0.2)); // 20-80% published rate

    return {
      date: dateStr,
      count,
      published
    };
  });

  // Generate events by status
  const statuses = ['Draft', 'Published', 'Cancelled', 'Completed'];
  const eventsByStatus = statuses.reduce((acc, status) => {
    acc[status] = Math.floor(Math.random() * 50) + (status === 'Published' ? 100 : 20);
    return acc;
  }, {} as Record<string, number>);

  // Generate events by category
  const categories = ['MUSIC', 'CONFERENCE', 'SPORTS', 'WEDDING', 'CORPORATE', 'FESTIVAL', 'OTHER'];
  const eventsByCategory = categories.reduce((acc, category) => {
    acc[category] = Math.floor(Math.random() * 30) + 10;
    return acc;
  }, {} as Record<string, number>);

  // Generate top events
  const topEvents = Array.from({ length: 10 }, (_, i) => ({
    id: `event-${i + 1}`,
    title: `Event ${i + 1}`,
    category: categories[Math.floor(Math.random() * categories.length)],
    status: statuses[Math.floor(Math.random() * statuses.length)],
    ticketsTotal: Math.floor(Math.random() * 500) + 100,
    ticketsSold: Math.floor(Math.random() * 300) + 50,
    revenue: Math.floor(Math.random() * 10000) + 2000
  })).sort((a, b) => b.revenue - a.revenue);

  // Calculate summary
  const totalEvents = Object.values(eventsByStatus).reduce((sum, count) => sum + count, 0);
  const publishedEvents = eventsByStatus['Published'] || 0;
  const totalRevenue = topEvents.reduce((sum, event) => sum + event.revenue, 0);

  return {
    reportType: 'events',
    dateRange: {
      start: dateRange.gte.toISOString(),
      end: dateRange.lte.toISOString()
    },
    summary: {
      totalEvents,
      publishedEvents,
      totalRevenue
    },
    eventsByStatus,
    eventsByCategory,
    dailyEvents,
    topEvents
  };
}

// Generate tickets report with mock data for now
async function generateTicketsReport(dateRange: { gte: Date; lte: Date }) {
  // For now, generate mock data
  // In a real implementation, you would query the database

  const startDate = dateRange.gte;
  const endDate = dateRange.lte;
  const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

  // Generate daily tickets data
  const dailyTickets = Array.from({ length: daysDiff }, (_, i) => {
    const date = new Date(startDate);
    date.setDate(date.getDate() + i);
    const dateStr = date.toISOString().split('T')[0];

    const total = Math.floor(Math.random() * 100) + 20;
    const sold = Math.floor(total * (Math.random() * 0.7 + 0.1)); // 10-80% sold rate
    const revenue = sold * (Math.floor(Math.random() * 50) + 20); // $20-70 per ticket

    return {
      date: dateStr,
      total,
      sold,
      revenue
    };
  });

  // Generate tickets by category
  const categories = ['MUSIC', 'CONFERENCE', 'SPORTS', 'WEDDING', 'CORPORATE', 'FESTIVAL', 'OTHER'];
  const categoryTickets = categories.map(category => {
    const total = Math.floor(Math.random() * 1000) + 200;
    const sold = Math.floor(total * (Math.random() * 0.7 + 0.1)); // 10-80% sold rate
    const revenue = sold * (Math.floor(Math.random() * 50) + 20); // $20-70 per ticket

    return {
      category,
      total,
      sold,
      revenue,
      value: total // For pie chart compatibility
    };
  });

  // Generate tickets by type
  const types = ['Standard', 'VIP', 'Early Bird', 'Group', 'Student'];
  const typeTickets = types.map(type => {
    const total = Math.floor(Math.random() * 500) + 100;
    const sold = Math.floor(total * (Math.random() * 0.7 + 0.1)); // 10-80% sold rate
    const revenue = sold * (Math.floor(Math.random() * 50) + 20); // $20-70 per ticket

    return {
      type,
      total,
      sold,
      revenue
    };
  });

  // Calculate summary
  const totalTickets = dailyTickets.reduce((sum, day) => sum + day.total, 0);
  const soldTickets = dailyTickets.reduce((sum, day) => sum + day.sold, 0);
  const availableTickets = totalTickets - soldTickets;
  const totalRevenue = dailyTickets.reduce((sum, day) => sum + day.revenue, 0);

  return {
    reportType: 'tickets',
    dateRange: {
      start: dateRange.gte.toISOString(),
      end: dateRange.lte.toISOString()
    },
    summary: {
      totalTickets,
      soldTickets,
      availableTickets,
      salesRate: totalTickets > 0 ? soldTickets / totalTickets : 0,
      totalRevenue
    },
    dailyTickets,
    categoryTickets,
    typeTickets
  };
}
