import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * PATCH /api/admin/partners/:id/verify
 * Verify or unverify a partner
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const partnerId = resolvedParams.id;
    const { isVerified } = await request.json();

    // Check if the partner exists
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
    });

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    // Update the partner's verification status
    const updatedPartner = await db.partner.update({
      where: { id: partnerId },
      data: {
        isVerified,
        verifiedAt: isVerified ? new Date() : null,
      },
    });

    return NextResponse.json(updatedPartner);
  } catch (error) {
    console.error('Error updating partner verification status:', error);
    return NextResponse.json(
      { error: 'Failed to update partner verification status' },
      { status: 500 }
    );
  }
}
