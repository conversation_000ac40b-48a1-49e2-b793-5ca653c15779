import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { EventPayoutCard } from '@/components/events/event-payout-card';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { UserSession } from '@/types/session';

export default async function EventPayoutPage({ params }: { params: Promise<{ id: string }> }) {
  const session = await getSession() as UserSession;
  const resolvedParams = await params;
  const eventId = resolvedParams.id;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user?.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  // Get the event to check permissions
  const event = await db.event.findUnique({
    where: { id: eventId },
    select: {
      id: true,
      title: true,
      userId: true,
      status: true,
      endDate: true,
      teamId: true,
      team: {
        select: {
          members: {
            select: {
              userId: true,
            },
          },
        },
      },
    },
  });

  if (!event) {
    redirect('/dashboard/organizer/events');
  }

  // Check if user is authorized to view this event's payout info
  const isOwner = event.userId === session.user?.id;
  const isTeamMember = event.team?.members.some(member => member.userId === session.user?.id) || false;

  if (!isOwner && !isTeamMember) {
    redirect('/dashboard/organizer/events');
  }

  // Get ticket sales data for the event
  const ticketSales = await db.ticketSales.findMany({
    where: { eventId },
    select: {
      type: true,
      sold: true,
      revenue: true,
      price: true,
    },
  });

  // Calculate total revenue and sales
  const totalTicketSales = ticketSales.reduce((sum, sale) => sum + sale.sold, 0);
  const totalRevenue = ticketSales.reduce((sum, sale) => sum + sale.revenue, 0);
  
  // Calculate commission (10% platform fee)
  const commissionRate = 0.1; // 10%
  const commissionAmount = totalRevenue * commissionRate;
  const organizerAmount = totalRevenue - commissionAmount;

  // Check if event has ended
  const currentDate = new Date();
  const hasEndedEvent = new Date(event.endDate) < currentDate;

  // Check if there's already a payout request for this event
  const existingPayout = await db.eventPayout.findFirst({
    where: {
      eventId,
      userId: session.user?.id,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

  const hasPendingPayout = !!existingPayout && 
    (existingPayout.status === 'Pending' || existingPayout.status === 'Processing');

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href={`/dashboard/organizer/events/details/${eventId}`}>
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Event Details
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Event Payout</h1>
        <p className="text-gray-500 mt-1">
          Manage payouts for {event.title}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <EventPayoutCard
          eventId={event.id}
          eventTitle={event.title}
          totalRevenue={totalRevenue}
          totalTicketSales={totalTicketSales}
          commissionRate={commissionRate}
          commissionAmount={commissionAmount}
          organizerAmount={organizerAmount}
          hasEndedEvent={hasEndedEvent}
          hasPendingPayout={hasPendingPayout}
          payoutStatus={existingPayout?.status}
        />

        <div className="space-y-6">
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-100">
            <h2 className="text-xl font-semibold mb-4">Payout Information</h2>
            <ul className="space-y-4">
              <li className="flex items-start">
                <div className="bg-blue-100 p-2 rounded-full mr-3 mt-0.5">
                  <ChevronLeft className="h-4 w-4 text-blue-700" />
                </div>
                <div>
                  <h3 className="font-medium">Event Completion</h3>
                  <p className="text-sm text-gray-600">
                    Payouts can only be requested after the event has ended.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-100 p-2 rounded-full mr-3 mt-0.5">
                  <ChevronLeft className="h-4 w-4 text-blue-700" />
                </div>
                <div>
                  <h3 className="font-medium">Processing Time</h3>
                  <p className="text-sm text-gray-600">
                    Payout requests are typically processed within 5 business days.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-100 p-2 rounded-full mr-3 mt-0.5">
                  <ChevronLeft className="h-4 w-4 text-blue-700" />
                </div>
                <div>
                  <h3 className="font-medium">Platform Commission</h3>
                  <p className="text-sm text-gray-600">
                    A {(commissionRate * 100).toFixed(0)}% platform fee is deducted from the total revenue.
                  </p>
                </div>
              </li>
              <li className="flex items-start">
                <div className="bg-blue-100 p-2 rounded-full mr-3 mt-0.5">
                  <ChevronLeft className="h-4 w-4 text-blue-700" />
                </div>
                <div>
                  <h3 className="font-medium">Withdrawal Process</h3>
                  <p className="text-sm text-gray-600">
                    Once your payout is approved, the funds will be added to your account balance. 
                    You can then withdraw the funds to your bank account.
                  </p>
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
