'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { RoleGate } from '@/components/auth/role-gate';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  XCircle,
  ChevronRight,
  CreditCard,
  Calendar,
  Users,
  BarChart3,
  Mail,
  MessageSquare,
  Globe,
  Zap,
  Award,
  Crown,
  Shield
} from 'lucide-react';

// Define subscription tiers and their features
const SUBSCRIPTION_TIERS = {
  NONE: {
    name: 'Free',
    monthlyPrice: 0,
    yearlyPrice: 0,
    color: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-200',
    features: [
      { name: 'Create up to 2 events per month', included: true },
      { name: 'Standard commission rate (6%)', included: true },
      { name: 'Basic support', included: true },
      { name: 'Email marketing', included: false },
      { name: 'Analytics dashboard', included: false },
      { name: 'Vendor management', included: false },
      { name: 'Team members', included: false, details: '0' },
    ]
  },
  BASIC: {
    name: 'Basic',
    monthlyPrice: 29.99,
    yearlyPrice: 299.99,
    color: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-300',
    features: [
      { name: 'Create up to 5 events per month', included: true },
      { name: 'Standard commission rate (6%)', included: true },
      { name: 'Standard support (email)', included: true },
      { name: 'Basic email marketing', included: true },
      { name: 'Basic analytics dashboard', included: true },
      { name: 'Limited vendor management', included: true },
      { name: 'Team members', included: true, details: '1' },
    ]
  },
  PREMIUM: {
    name: 'Premium',
    monthlyPrice: 79.99,
    yearlyPrice: 799.99,
    color: 'bg-orange-50',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-300',
    features: [
      { name: 'Create up to 15 events per month', included: true },
      { name: 'Reduced commission rate (5%)', included: true },
      { name: 'Priority support (email + chat)', included: true },
      { name: 'Advanced email marketing campaigns', included: true },
      { name: 'Advanced analytics with detailed reports', included: true },
      { name: 'Enhanced vendor management', included: true },
      { name: 'Team members', included: true, details: '5' },
      { name: 'Custom event URL', included: true },
      { name: 'Priority in search results', included: true },
    ]
  },
  ELITE: {
    name: 'Elite',
    monthlyPrice: 199.99,
    yearlyPrice: 1999.99,
    color: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-300',
    features: [
      { name: 'Unlimited events', included: true },
      { name: 'Lowest commission rate (4%)', included: true },
      { name: 'Dedicated support manager', included: true },
      { name: 'Full marketing suite', included: true, details: 'Email, SMS, social' },
      { name: 'Premium analytics with predictive insights', included: true },
      { name: 'Complete vendor management system', included: true },
      { name: 'Team members', included: true, details: 'Unlimited' },
      { name: 'Custom branding options', included: true },
      { name: 'Featured organizer status', included: true },
      { name: 'API access', included: true },
      { name: 'Early access to new features', included: true },
    ]
  }
};

export default function SubscriptionPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [currentTier, setCurrentTier] = useState<'NONE' | 'BASIC' | 'PREMIUM' | 'ELITE'>('NONE');
  const [subscriptionEndDate, setSubscriptionEndDate] = useState<Date | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tierData, setTierData] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch current subscription details and tier information
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Fetch subscription details
        const subscriptionResponse = await fetch('/api/organizer/subscription');

        if (!subscriptionResponse.ok) {
          throw new Error(`Failed to fetch subscription data: ${subscriptionResponse.status}`);
        }

        const subscriptionData = await subscriptionResponse.json();

        if (subscriptionData.subscriptionTier) {
          setCurrentTier(subscriptionData.subscriptionTier);
        }

        if (subscriptionData.subscriptionEndDate) {
          setSubscriptionEndDate(new Date(subscriptionData.subscriptionEndDate));
        }

        if (subscriptionData.billingCycle) {
          setBillingCycle(subscriptionData.billingCycle);
        }

        // Fetch tier features and pricing
        const featuresResponse = await fetch('/api/organizer/subscription/features');

        if (!featuresResponse.ok) {
          throw new Error(`Failed to fetch features data: ${featuresResponse.status}`);
        }

        const featuresData = await featuresResponse.json();

        if (!featuresData || Object.keys(featuresData).length === 0) {
          throw new Error('No subscription tier data available from the database');
        }

        console.log('Fetched features data:', featuresData);

        // Create a new object with only the data from the database
        const dbTiers: any = {};

        Object.keys(featuresData).forEach(tier => {
          const tierData = featuresData[tier];

          if (!tierData) {
            console.warn(`Missing data for tier: ${tier}`);
            return;
          }

          // Create tier object with only database values
          dbTiers[tier] = {
            name: tierData.name,
            monthlyPrice: Number(tierData.monthlyPrice),
            yearlyPrice: Number(tierData.yearlyPrice),
            color: tier === 'NONE' ? 'bg-gray-100' :
                   tier === 'BASIC' ? 'bg-gray-100' :
                   tier === 'PREMIUM' ? 'bg-orange-50' :
                   tier === 'ELITE' ? 'bg-blue-50' : 'bg-gray-100',
            textColor: tier === 'NONE' ? 'text-gray-800' :
                       tier === 'BASIC' ? 'text-gray-800' :
                       tier === 'PREMIUM' ? 'text-orange-800' :
                       tier === 'ELITE' ? 'text-blue-800' : 'text-gray-800',
            borderColor: tier === 'NONE' ? 'border-gray-200' :
                         tier === 'BASIC' ? 'border-gray-300' :
                         tier === 'PREMIUM' ? 'border-orange-300' :
                         tier === 'ELITE' ? 'border-blue-300' : 'border-gray-200',
            features: []
          };

          // Create features based on database values
          const features = [];

          // Event limit feature
          if (tierData.maxEvents === null) {
            features.push({ name: 'Unlimited events', included: true });
          } else {
            features.push({ name: `Create up to ${tierData.maxEvents} events per month`, included: true });
          }

          // Commission rate feature
          const commissionPercent = (Number(tierData.commissionRate) * 100).toFixed(0);
          if (tier === 'NONE' || tier === 'BASIC') {
            features.push({ name: `Standard commission rate (${commissionPercent}%)`, included: true });
          } else if (tier === 'PREMIUM') {
            features.push({ name: `Reduced commission rate (${commissionPercent}%)`, included: true });
          } else if (tier === 'ELITE') {
            features.push({ name: `Lowest commission rate (${commissionPercent}%)`, included: true });
          }

          // Support feature
          if (tier === 'NONE') {
            features.push({ name: 'Basic support', included: true });
          } else if (tier === 'BASIC') {
            features.push({ name: 'Standard support (email)', included: true });
          } else if (tier === 'PREMIUM') {
            features.push({ name: 'Priority support (email + chat)', included: true });
          } else if (tier === 'ELITE') {
            features.push({ name: 'Dedicated support manager', included: true });
          }

          // Email marketing feature
          if (tier === 'NONE') {
            features.push({ name: 'Email marketing', included: false });
          } else if (tier === 'BASIC') {
            features.push({ name: 'Basic email marketing', included: true });
          } else if (tier === 'PREMIUM') {
            features.push({ name: 'Advanced email marketing campaigns', included: true });
          } else if (tier === 'ELITE') {
            features.push({ name: 'Full marketing suite', included: true, details: 'Email, SMS, social' });
          }

          // Analytics feature
          if (tier === 'NONE') {
            features.push({ name: 'Analytics dashboard', included: false });
          } else if (tier === 'BASIC') {
            features.push({ name: 'Basic analytics dashboard', included: true });
          } else if (tier === 'PREMIUM') {
            features.push({ name: 'Advanced analytics with detailed reports', included: true });
          } else if (tier === 'ELITE') {
            features.push({ name: 'Premium analytics with predictive insights', included: true });
          }

          // Vendor management feature
          if (tier === 'NONE') {
            features.push({ name: 'Vendor management', included: false });
          } else if (tier === 'BASIC') {
            features.push({ name: 'Limited vendor management', included: true });
          } else if (tier === 'PREMIUM') {
            features.push({ name: 'Enhanced vendor management', included: true });
          } else if (tier === 'ELITE') {
            features.push({ name: 'Complete vendor management system', included: true });
          }

          // Team members feature
          if (tier === 'NONE') {
            features.push({ name: 'Team members', included: false, details: '0' });
          } else {
            const teamMemberDetails = tierData.maxTeamMembers === null ? 'Unlimited' : tierData.maxTeamMembers.toString();
            features.push({ name: 'Team members', included: true, details: teamMemberDetails });
          }

          // Premium-only features
          if (tier === 'PREMIUM' || tier === 'ELITE') {
            features.push({ name: 'Custom event URL', included: true });
            features.push({ name: 'Priority in search results', included: true });
          }

          // Elite-only features
          if (tier === 'ELITE') {
            features.push({ name: 'Custom branding options', included: true });
            features.push({ name: 'Featured organizer status', included: true });
            features.push({ name: 'API access', included: true });
            features.push({ name: 'Early access to new features', included: true });
          }

          dbTiers[tier].features = features;
        });

        console.log('Database tiers:', dbTiers);

        // Ensure we have all required tiers
        if (!dbTiers.NONE || !dbTiers.BASIC || !dbTiers.PREMIUM || !dbTiers.ELITE) {
          throw new Error('Missing one or more required subscription tiers');
        }

        setTierData(dbTiers);
        setIsLoading(false);
      } catch (error: any) {
        console.error('Error fetching data:', error);
        setError(error.message || 'Failed to load subscription details');
        toast({
          title: 'Error',
          description: error.message || 'Failed to load subscription details',
          variant: 'destructive',
        });
        setIsLoading(false);
      }
    };

    if (status === 'authenticated') {
      fetchData();
    }
  }, [status]);

  const handleUpgrade = async (tier: 'BASIC' | 'PREMIUM' | 'ELITE') => {
    setIsLoading(true);

    try {
      // Redirect to checkout page with tier and billing cycle
      router.push(`/dashboard/organizer/subscription/checkout?tier=${tier}&cycle=${billingCycle}`);
    } catch (error) {
      console.error('Error initiating upgrade:', error);
      toast({
        title: 'Error',
        description: 'Failed to initiate upgrade process',
        variant: 'destructive',
      });
      setIsLoading(false);
    }
  };

  const formatDate = (date: Date | null) => {
    if (!date) return 'N/A';
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(date);
  };

  const getSubscriptionStatusBadge = () => {
    if (currentTier === 'NONE') {
      return <Badge variant="outline" className="ml-2">Free Plan</Badge>;
    }

    if (subscriptionEndDate && subscriptionEndDate > new Date()) {
      return <Badge className="bg-green-100 text-green-800 ml-2">Active</Badge>;
    }

    return <Badge variant="destructive" className="ml-2">Expired</Badge>;
  };

  const getTierIcon = (tier: 'NONE' | 'BASIC' | 'PREMIUM' | 'ELITE') => {
    switch (tier) {
      case 'ELITE':
        return <Crown className="h-6 w-6 text-blue-500" />;
      case 'PREMIUM':
        return <Award className="h-6 w-6 text-orange-500" />;
      case 'BASIC':
        return <Shield className="h-6 w-6 text-gray-500" />;
      default:
        return <Zap className="h-6 w-6 text-gray-400" />;
    }
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold">Subscription Management</h1>
            <p className="text-gray-500 mt-1">
              Upgrade your organizer account to unlock premium features
            </p>
          </div>
          {isLoading ? (
            <div className="flex items-center">
              <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-blue-500 rounded-full"></div>
              <span>Loading subscription data...</span>
            </div>
          ) : error ? (
            <div className="text-red-500">Error: {error}</div>
          ) : tierData ? (
            <div className="flex items-center">
              <span className="text-sm font-medium mr-2">Current Plan: {tierData[currentTier]?.name}</span>
              {getSubscriptionStatusBadge()}
            </div>
          ) : null}
        </div>

        {isLoading ? (
          <Card className="p-8">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="animate-spin mb-4 h-12 w-12 border-t-4 border-b-4 border-blue-500 rounded-full"></div>
              <h3 className="text-xl font-medium mb-2">Loading Subscription Data</h3>
              <p className="text-gray-500 text-center">
                Please wait while we fetch your subscription details from the database.
              </p>
            </div>
          </Card>
        ) : error ? (
          <Card className="p-8 border-red-200">
            <div className="flex flex-col items-center justify-center py-12">
              <XCircle className="h-12 w-12 text-red-500 mb-4" />
              <h3 className="text-xl font-medium mb-2">Error Loading Data</h3>
              <p className="text-red-500 text-center mb-4">
                {error}
              </p>
              <Button onClick={() => window.location.reload()}>
                Retry
              </Button>
            </div>
          </Card>
        ) : !tierData ? (
          <Card className="p-8">
            <div className="flex flex-col items-center justify-center py-12">
              <XCircle className="h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-xl font-medium mb-2">No Data Available</h3>
              <p className="text-gray-500 text-center">
                No subscription data is available. Please try again later.
              </p>
            </div>
          </Card>
        ) : currentTier !== 'NONE' && (
          <Card className="mb-8">
            <CardHeader>
              <CardTitle className="flex items-center">
                {getTierIcon(currentTier)}
                <span className="ml-2">{tierData[currentTier].name} Plan</span>
              </CardTitle>
              <CardDescription>
                Your current subscription details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center text-sm text-gray-500 mb-1">
                    <Calendar className="h-4 w-4 mr-1" />
                    Billing Cycle
                  </div>
                  <div className="font-medium">
                    {billingCycle === 'monthly' ? 'Monthly' : 'Yearly'}
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center text-sm text-gray-500 mb-1">
                    <Calendar className="h-4 w-4 mr-1" />
                    Next Billing Date
                  </div>
                  <div className="font-medium">
                    {formatDate(subscriptionEndDate)}
                  </div>
                </div>
                <div className="p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center text-sm text-gray-500 mb-1">
                    <CreditCard className="h-4 w-4 mr-1" />
                    Amount
                  </div>
                  <div className="font-medium">
                    ${billingCycle === 'monthly'
                      ? tierData[currentTier].monthlyPrice.toFixed(2)
                      : tierData[currentTier].yearlyPrice.toFixed(2)
                    } / {billingCycle === 'monthly' ? 'month' : 'year'}
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button variant="outline" className="mr-2">
                Cancel Subscription
              </Button>
              <Button>
                Manage Payment Methods
              </Button>
            </CardFooter>
          </Card>
        )}

        {isLoading ? (
          null // Already showing loading state above
        ) : error ? (
          null // Already showing error state above
        ) : !tierData ? (
          null // Already showing no data state above
        ) : (
          <div className="mb-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-2xl font-bold">Available Plans</h2>
              <div className="bg-gray-100 p-1 rounded-lg">
                <Button
                  variant={billingCycle === 'monthly' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setBillingCycle('monthly')}
                  className="rounded-lg"
                >
                  Monthly
                </Button>
                <Button
                  variant={billingCycle === 'yearly' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setBillingCycle('yearly')}
                  className="rounded-lg"
                >
                  Yearly (Save 16%)
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Basic Plan */}
              <Card className={`border-2 ${currentTier === 'BASIC' ? 'border-gray-400' : 'border-gray-200'} hover:border-gray-400 transition-all`}>
                <CardHeader className={`${tierData.BASIC.color}`}>
                  <div className="flex justify-between items-center">
                    <CardTitle className={`flex items-center ${tierData.BASIC.textColor}`}>
                      <Shield className="h-5 w-5 mr-2" />
                      Basic
                    </CardTitle>
                    {currentTier === 'BASIC' && (
                      <Badge variant="outline" className="bg-gray-800 text-white">Current Plan</Badge>
                    )}
                  </div>
                  <CardDescription>
                    For small organizers getting started
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-3xl font-bold">
                    ${billingCycle === 'monthly'
                      ? tierData.BASIC.monthlyPrice.toFixed(2)
                      : tierData.BASIC.yearlyPrice.toFixed(2)
                    }
                    <span className="text-sm font-normal text-gray-500">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </div>

                  <Separator />

                  <ul className="space-y-2">
                    {tierData.BASIC.features.map((feature: any, index: number) => (
                      <li key={index} className="flex items-start">
                        {feature.included ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        ) : (
                          <XCircle className="h-5 w-5 text-gray-300 mr-2 flex-shrink-0 mt-0.5" />
                        )}
                        <span className={!feature.included ? 'text-gray-400' : ''}>
                          {feature.name}
                          {feature.details && <span className="text-sm text-gray-500 block">{feature.details}</span>}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full"
                    variant={currentTier === 'BASIC' ? 'outline' : 'default'}
                    disabled={currentTier === 'BASIC' || isLoading}
                    onClick={() => handleUpgrade('BASIC')}
                  >
                    {currentTier === 'BASIC'
                      ? 'Current Plan'
                      : currentTier === 'NONE'
                        ? 'Upgrade to Basic'
                        : currentTier === 'ELITE' || currentTier === 'PREMIUM'
                          ? 'Downgrade to Basic'
                          : 'Select Plan'
                    }
                  </Button>
                </CardFooter>
              </Card>

              {/* Premium Plan */}
              <Card className={`border-2 ${currentTier === 'PREMIUM' ? 'border-orange-400' : 'border-orange-200'} hover:border-orange-400 transition-all`}>
                <CardHeader className={`${tierData.PREMIUM.color}`}>
                  <div className="flex justify-between items-center">
                    <CardTitle className={`flex items-center ${tierData.PREMIUM.textColor}`}>
                      <Award className="h-5 w-5 mr-2" />
                      Premium
                    </CardTitle>
                    {currentTier === 'PREMIUM' ? (
                      <Badge variant="outline" className="bg-orange-600 text-white">Current Plan</Badge>
                    ) : (
                      <Badge className="bg-orange-100 text-orange-800">Popular</Badge>
                    )}
                  </div>
                  <CardDescription>
                    For growing organizers with multiple events
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-3xl font-bold">
                    ${billingCycle === 'monthly'
                      ? tierData.PREMIUM.monthlyPrice.toFixed(2)
                      : tierData.PREMIUM.yearlyPrice.toFixed(2)
                    }
                    <span className="text-sm font-normal text-gray-500">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </div>

                  <Separator />

                  <ul className="space-y-2">
                    {tierData.PREMIUM.features.map((feature: any, index: number) => (
                      <li key={index} className="flex items-start">
                        {feature.included ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        ) : (
                          <XCircle className="h-5 w-5 text-gray-300 mr-2 flex-shrink-0 mt-0.5" />
                        )}
                        <span className={!feature.included ? 'text-gray-400' : ''}>
                          {feature.name}
                          {feature.details && <span className="text-sm text-gray-500 block">{feature.details}</span>}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full bg-orange-500 hover:bg-orange-600"
                    variant={currentTier === 'PREMIUM' ? 'outline' : 'default'}
                    disabled={currentTier === 'PREMIUM' || isLoading}
                    onClick={() => handleUpgrade('PREMIUM')}
                  >
                    {currentTier === 'PREMIUM'
                      ? 'Current Plan'
                      : currentTier === 'ELITE'
                        ? 'Downgrade to Premium'
                        : 'Upgrade to Premium'
                    }
                  </Button>
                </CardFooter>
              </Card>

              {/* Elite Plan */}
              <Card className={`border-2 ${currentTier === 'ELITE' ? 'border-blue-400' : 'border-blue-200'} hover:border-blue-400 transition-all`}>
                <CardHeader className={`${tierData.ELITE.color}`}>
                  <div className="flex justify-between items-center">
                    <CardTitle className={`flex items-center ${tierData.ELITE.textColor}`}>
                      <Crown className="h-5 w-5 mr-2" />
                      Elite
                    </CardTitle>
                    {currentTier === 'ELITE' && (
                      <Badge variant="outline" className="bg-blue-600 text-white">Current Plan</Badge>
                    )}
                  </div>
                  <CardDescription>
                    For professional organizers with high volume
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-3xl font-bold">
                    ${billingCycle === 'monthly'
                      ? tierData.ELITE.monthlyPrice.toFixed(2)
                      : tierData.ELITE.yearlyPrice.toFixed(2)
                    }
                    <span className="text-sm font-normal text-gray-500">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  </div>

                  <Separator />

                  <ul className="space-y-2">
                    {tierData.ELITE.features.map((feature: any, index: number) => (
                      <li key={index} className="flex items-start">
                        {feature.included ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        ) : (
                          <XCircle className="h-5 w-5 text-gray-300 mr-2 flex-shrink-0 mt-0.5" />
                        )}
                        <span className={!feature.included ? 'text-gray-400' : ''}>
                          {feature.name}
                          {feature.details && <span className="text-sm text-gray-500 block">{feature.details}</span>}
                        </span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Button
                    className="w-full bg-blue-500 hover:bg-blue-600"
                    variant={currentTier === 'ELITE' ? 'outline' : 'default'}
                    disabled={currentTier === 'ELITE' || isLoading}
                    onClick={() => handleUpgrade('ELITE')}
                  >
                    {currentTier === 'ELITE' ? 'Current Plan' : 'Upgrade to Elite'}
                  </Button>
                </CardFooter>
              </Card>
            </div>
          </div>
        )}

        {!isLoading && !error && tierData && (
          <>
            <div className="mt-12">
              <h2 className="text-2xl font-bold mb-4">Subscription Benefits</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
                      Advanced Analytics
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Gain deeper insights into your events with detailed analytics and reporting tools.
                      Track attendee behavior, sales patterns, and marketing effectiveness.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Users className="h-5 w-5 mr-2 text-green-500" />
                      Team Management
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Add team members to help manage your events. Assign roles and permissions
                      to streamline your event organization process.
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Mail className="h-5 w-5 mr-2 text-amber-500" />
                      Marketing Tools
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600">
                      Access powerful marketing tools to promote your events. Create email campaigns,
                      custom forms, and social media integrations.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>

            <div className="mt-8 bg-gray-50 p-6 rounded-lg">
              <h3 className="text-lg font-medium mb-2">Need help choosing a plan?</h3>
              <p className="text-gray-600 mb-4">
                Our team is ready to help you select the best plan for your needs. Contact us for personalized assistance.
              </p>
              <Button variant="outline">
                <MessageSquare className="h-4 w-4 mr-2" />
                Contact Support
              </Button>
            </div>
          </>
        )}
      </div>
    </RoleGate>
  );
}
