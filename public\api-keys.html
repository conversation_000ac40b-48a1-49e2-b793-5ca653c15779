<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Key Management - Event Platform</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      padding-top: 2rem;
      padding-bottom: 4rem;
    }
    .header {
      background-color: #f8f9fa;
      padding: 2rem 0;
      margin-bottom: 2rem;
      border-bottom: 1px solid #e9ecef;
    }
    .api-key-card {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      transition: all 0.3s ease;
    }
    .api-key-card:hover {
      box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    .api-key {
      font-family: monospace;
      background-color: #f8f9fa;
      padding: 0.5rem;
      border-radius: 4px;
      word-break: break-all;
    }
    .tier-badge {
      font-size: 0.8rem;
      padding: 0.3rem 0.6rem;
      border-radius: 4px;
      margin-left: 0.5rem;
    }
    .tier-free {
      background-color: #e9ecef;
      color: #495057;
    }
    .tier-basic {
      background-color: #cff4fc;
      color: #055160;
    }
    .tier-professional {
      background-color: #d1e7dd;
      color: #0f5132;
    }
    .tier-enterprise {
      background-color: #f8d7da;
      color: #842029;
    }
    .permission-check {
      margin-right: 0.5rem;
    }
  </style>
</head>
<body>
  <div class="header">
    <div class="container">
      <h1 class="display-4">API Key Management</h1>
      <p class="lead">Create and manage your API keys for the Event Platform API</p>
    </div>
  </div>

  <div class="container">
    <div class="row mb-5">
      <div class="col-md-8">
        <h2>Your API Keys</h2>
        <p>
          API keys are used to authenticate your requests to the Event Platform API.
          Each key has a set of permissions and a tier that determines the rate limits and available endpoints.
        </p>
      </div>
      <div class="col-md-4 text-end">
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createKeyModal">Create New API Key</button>
      </div>
    </div>

    <div class="row" id="apiKeysContainer">
      <!-- API keys will be displayed here -->
      <div class="col-12">
        <div class="text-center py-5">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <p class="mt-3">Loading your API keys...</p>
        </div>
      </div>
    </div>

    <div class="row mt-5">
      <div class="col-12">
        <h2>API Key Usage</h2>
        <p>
          Monitor your API key usage to ensure you stay within your plan's limits.
          Upgrade your plan if you need higher rate limits or access to more endpoints.
        </p>
        <div class="table-responsive">
          <table class="table table-striped">
            <thead>
              <tr>
                <th>API Key</th>
                <th>Requests Today</th>
                <th>Requests This Month</th>
                <th>Last Used</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody id="usageTableBody">
              <!-- Usage data will be displayed here -->
              <tr>
                <td colspan="5" class="text-center">No usage data available</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Create API Key Modal -->
  <div class="modal fade" id="createKeyModal" tabindex="-1" aria-labelledby="createKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="createKeyModalLabel">Create New API Key</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <form id="createKeyForm">
            <div class="mb-3">
              <label for="keyName" class="form-label">Key Name</label>
              <input type="text" class="form-control" id="keyName" placeholder="e.g., Production API Key">
              <div class="form-text">A descriptive name to help you identify this key</div>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Tier</label>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="tier" id="tierFree" value="FREE" checked>
                <label class="form-check-label" for="tierFree">
                  Free <span class="tier-badge tier-free">FREE</span>
                </label>
                <div class="form-text">100 requests/day, 10 requests/minute, basic event data</div>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="tier" id="tierBasic" value="BASIC">
                <label class="form-check-label" for="tierBasic">
                  Basic <span class="tier-badge tier-basic">BASIC</span>
                </label>
                <div class="form-text">5,000 requests/day, 60 requests/minute, advanced search</div>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="tier" id="tierProfessional" value="PROFESSIONAL">
                <label class="form-check-label" for="tierProfessional">
                  Professional <span class="tier-badge tier-professional">PROFESSIONAL</span>
                </label>
                <div class="form-text">20,000 requests/day, 300 requests/minute, analytics data</div>
              </div>
              <div class="form-check">
                <input class="form-check-input" type="radio" name="tier" id="tierEnterprise" value="ENTERPRISE">
                <label class="form-check-label" for="tierEnterprise">
                  Enterprise <span class="tier-badge tier-enterprise">ENTERPRISE</span>
                </label>
                <div class="form-text">Unlimited requests/day, 1,000 requests/minute, custom endpoints</div>
              </div>
            </div>
            
            <div class="mb-3">
              <label class="form-label">Permissions</label>
              <div class="row">
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permReadEvents" value="read:events" checked>
                    <label class="form-check-label" for="permReadEvents">read:events</label>
                    <div class="form-text">Read event data</div>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permReadTickets" value="read:tickets">
                    <label class="form-check-label" for="permReadTickets">read:tickets</label>
                    <div class="form-text">Read ticket data</div>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permReadOrders" value="read:orders">
                    <label class="form-check-label" for="permReadOrders">read:orders</label>
                    <div class="form-text">Read order data</div>
                  </div>
                </div>
                <div class="col-md-6">
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permWriteOrders" value="write:orders">
                    <label class="form-check-label" for="permWriteOrders">write:orders</label>
                    <div class="form-text">Create and update orders</div>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permWriteTickets" value="write:tickets">
                    <label class="form-check-label" for="permWriteTickets">write:tickets</label>
                    <div class="form-text">Create and update tickets</div>
                  </div>
                  <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="permReadAnalytics" value="read:analytics">
                    <label class="form-check-label" for="permReadAnalytics">read:analytics</label>
                    <div class="form-text">Access analytics data</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="expiresAt" class="form-label">Expires At</label>
              <input type="date" class="form-control" id="expiresAt">
              <div class="form-text">Leave blank for a non-expiring key</div>
            </div>
          </form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
          <button type="button" class="btn btn-primary" id="createKeyBtn">Create API Key</button>
        </div>
      </div>
    </div>
  </div>

  <!-- New API Key Modal -->
  <div class="modal fade" id="newKeyModal" tabindex="-1" aria-labelledby="newKeyModalLabel" aria-hidden="true">
    <div class="modal-dialog">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title" id="newKeyModalLabel">Your New API Key</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        </div>
        <div class="modal-body">
          <div class="alert alert-warning">
            <strong>Important:</strong> This is the only time your API key will be displayed. Please copy it now and store it securely.
          </div>
          <div class="mb-3">
            <label class="form-label">API Key</label>
            <div class="input-group">
              <input type="text" class="form-control" id="newApiKey" readonly>
              <button class="btn btn-outline-secondary" type="button" onclick="copyApiKey()">Copy</button>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Done</button>
        </div>
      </div>
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Sample API keys data (in a real app, this would come from your backend)
    const sampleApiKeys = [
      {
        id: '1',
        name: 'Development API Key',
        key: 'dev_sk_1234567890abcdef',
        tier: 'FREE',
        permissions: ['read:events'],
        createdAt: '2023-01-15T10:30:00Z',
        lastUsed: '2023-06-20T14:45:00Z',
        usageCount: 1250,
        expiresAt: null
      },
      {
        id: '2',
        name: 'Production API Key',
        key: 'prod_sk_abcdef1234567890',
        tier: 'BASIC',
        permissions: ['read:events', 'read:tickets', 'read:orders'],
        createdAt: '2023-03-10T08:15:00Z',
        lastUsed: '2023-06-22T09:30:00Z',
        usageCount: 8750,
        expiresAt: '2024-03-10T08:15:00Z'
      },
      {
        id: '3',
        name: 'Analytics API Key',
        key: 'analytics_sk_9876543210abcdef',
        tier: 'PROFESSIONAL',
        permissions: ['read:events', 'read:analytics'],
        createdAt: '2023-05-05T16:20:00Z',
        lastUsed: '2023-06-21T11:10:00Z',
        usageCount: 3420,
        expiresAt: null
      }
    ];

    // Sample usage data
    const sampleUsageData = [
      {
        keyId: '1',
        keyName: 'Development API Key',
        requestsToday: 45,
        requestsThisMonth: 1250,
        lastUsed: '2023-06-20T14:45:00Z',
        status: 'Active'
      },
      {
        keyId: '2',
        keyName: 'Production API Key',
        requestsToday: 320,
        requestsThisMonth: 8750,
        lastUsed: '2023-06-22T09:30:00Z',
        status: 'Active'
      },
      {
        keyId: '3',
        keyName: 'Analytics API Key',
        requestsToday: 78,
        requestsThisMonth: 3420,
        lastUsed: '2023-06-21T11:10:00Z',
        status: 'Active'
      }
    ];

    // Function to display API keys
    function displayApiKeys(apiKeys) {
      const container = document.getElementById('apiKeysContainer');
      
      if (apiKeys.length === 0) {
        container.innerHTML = `
          <div class="col-12">
            <div class="alert alert-info">
              You don't have any API keys yet. Click the "Create New API Key" button to get started.
            </div>
          </div>
        `;
        return;
      }
      
      let html = '';
      
      apiKeys.forEach(key => {
        const tierClass = `tier-${key.tier.toLowerCase()}`;
        const expiresText = key.expiresAt ? new Date(key.expiresAt).toLocaleDateString() : 'Never';
        
        html += `
          <div class="col-md-6 mb-4">
            <div class="api-key-card">
              <div class="d-flex justify-content-between align-items-start mb-3">
                <h4>${key.name} <span class="tier-badge ${tierClass}">${key.tier}</span></h4>
                <button class="btn btn-sm btn-outline-danger" onclick="revokeKey('${key.id}')">Revoke</button>
              </div>
              <div class="mb-3">
                <label class="form-label">API Key</label>
                <div class="api-key">${maskApiKey(key.key)}</div>
              </div>
              <div class="row mb-3">
                <div class="col-md-6">
                  <label class="form-label">Created</label>
                  <div>${new Date(key.createdAt).toLocaleDateString()}</div>
                </div>
                <div class="col-md-6">
                  <label class="form-label">Expires</label>
                  <div>${expiresText}</div>
                </div>
              </div>
              <div class="mb-3">
                <label class="form-label">Permissions</label>
                <div>
                  ${key.permissions.map(perm => `<span class="badge bg-secondary me-1">${perm}</span>`).join('')}
                </div>
              </div>
              <div class="mb-3">
                <label class="form-label">Usage</label>
                <div>${key.usageCount} requests</div>
              </div>
              <div class="mb-3">
                <label class="form-label">Last Used</label>
                <div>${new Date(key.lastUsed).toLocaleString()}</div>
              </div>
            </div>
          </div>
        `;
      });
      
      container.innerHTML = html;
    }

    // Function to display usage data
    function displayUsageData(usageData) {
      const tableBody = document.getElementById('usageTableBody');
      
      if (usageData.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="5" class="text-center">No usage data available</td></tr>`;
        return;
      }
      
      let html = '';
      
      usageData.forEach(data => {
        html += `
          <tr>
            <td>${data.keyName}</td>
            <td>${data.requestsToday}</td>
            <td>${data.requestsThisMonth}</td>
            <td>${new Date(data.lastUsed).toLocaleString()}</td>
            <td><span class="badge bg-success">${data.status}</span></td>
          </tr>
        `;
      });
      
      tableBody.innerHTML = html;
    }

    // Function to mask API key
    function maskApiKey(key) {
      if (key.length <= 8) return key;
      return key.substring(0, 4) + '...' + key.substring(key.length - 4);
    }

    // Function to revoke an API key
    function revokeKey(keyId) {
      if (confirm('Are you sure you want to revoke this API key? This action cannot be undone.')) {
        // In a real app, you would make an API call to revoke the key
        alert(`API key ${keyId} has been revoked.`);
        // Refresh the list of API keys
        displayApiKeys(sampleApiKeys.filter(key => key.id !== keyId));
      }
    }

    // Function to create a new API key
    function createApiKey() {
      const name = document.getElementById('keyName').value.trim();
      const tier = document.querySelector('input[name="tier"]:checked').value;
      const expiresAt = document.getElementById('expiresAt').value;
      
      // Get selected permissions
      const permissions = [];
      document.querySelectorAll('input[type="checkbox"]:checked').forEach(checkbox => {
        permissions.push(checkbox.value);
      });
      
      if (!name) {
        alert('Please enter a name for your API key.');
        return;
      }
      
      if (permissions.length === 0) {
        alert('Please select at least one permission for your API key.');
        return;
      }
      
      // In a real app, you would make an API call to create the key
      // For this demo, we'll just generate a random key
      const newKey = generateApiKey();
      
      // Display the new key
      document.getElementById('newApiKey').value = newKey;
      
      // Hide the create key modal and show the new key modal
      const createKeyModal = bootstrap.Modal.getInstance(document.getElementById('createKeyModal'));
      createKeyModal.hide();
      
      const newKeyModal = new bootstrap.Modal(document.getElementById('newKeyModal'));
      newKeyModal.show();
      
      // Reset the form
      document.getElementById('createKeyForm').reset();
    }

    // Function to generate a random API key
    function generateApiKey() {
      const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
      let key = '';
      for (let i = 0; i < 32; i++) {
        key += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      return key;
    }

    // Function to copy the API key to clipboard
    function copyApiKey() {
      const keyInput = document.getElementById('newApiKey');
      keyInput.select();
      document.execCommand('copy');
      alert('API key copied to clipboard!');
    }

    // Initialize the page
    document.addEventListener('DOMContentLoaded', function() {
      // Display API keys
      displayApiKeys(sampleApiKeys);
      
      // Display usage data
      displayUsageData(sampleUsageData);
      
      // Set up event listener for create key button
      document.getElementById('createKeyBtn').addEventListener('click', createApiKey);
    });
  </script>
</body>
</html>
