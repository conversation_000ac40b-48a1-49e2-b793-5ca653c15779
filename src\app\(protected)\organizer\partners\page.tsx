import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import OrganizerPartnersClient from '@/components/organizer/partners/partners-client';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
  };
};

export default async function OrganizerPartnersPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <Suspense fallback={<div className="animate-pulse">Loading partners...</div>}>
        <OrganizerPartnersClient />
      </Suspense>
    </div>
  );
}
