import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/auth';
import { db } from '@/lib/prisma';

/**
 * GET /api/admin/subscription/tiers/[tier]
 * Get a specific subscription tier price
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tier: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow admins to access this endpoint
    if (!session || !session.user || !['ADMIN', 'SUPERADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const { tier } = resolvedParams;

    // Get the subscription tier price using Prisma model
    const tierPrice = await db.subscriptionTierPrice.findUnique({
      where: {
        tier: tier
      }
    });

    if (!tierPrice) {
      return NextResponse.json(
        { error: 'Subscription tier not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(tierPrice);

  } catch (error) {
    console.error('Error fetching subscription tier price:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription tier price' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/subscription/tiers/[tier]
 * Update a specific subscription tier price
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ tier: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow admins to access this endpoint
    if (!session || !session.user || !['ADMIN', 'SUPERADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const { tier } = resolvedParams;
    const data = await request.json();

    // Check if tier exists using Prisma model
    const existingTier = await db.subscriptionTierPrice.findUnique({
      where: {
        tier: tier
      }
    });

    if (!existingTier) {
      return NextResponse.json(
        { error: 'Subscription tier not found' },
        { status: 404 }
      );
    }

    // Build update data object based on provided fields
    const updateData: any = {};

    if (data.monthlyPrice !== undefined) {
      updateData.monthlyPrice = data.monthlyPrice;
    }

    if (data.yearlyPrice !== undefined) {
      updateData.yearlyPrice = data.yearlyPrice;
    }

    if (data.commissionRate !== undefined) {
      updateData.commissionRate = data.commissionRate;
    }

    if (data.maxEvents !== undefined) {
      updateData.maxEvents = data.maxEvents;
    }

    if (data.maxTeamMembers !== undefined) {
      updateData.maxTeamMembers = data.maxTeamMembers;
    }

    if (data.maxEmailCampaigns !== undefined) {
      updateData.maxEmailCampaigns = data.maxEmailCampaigns;
    }

    if (data.maxAnalyticsReports !== undefined) {
      updateData.maxAnalyticsReports = data.maxAnalyticsReports;
    }

    if (data.maxVendorManagement !== undefined) {
      updateData.maxVendorManagement = data.maxVendorManagement;
    }

    if (data.isActive !== undefined) {
      updateData.isActive = data.isActive;
    }

    // Add updated_at field
    updateData.updatedAt = new Date();

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No fields to update' },
        { status: 400 }
      );
    }

    // Update the tier using Prisma model
    const result = await db.subscriptionTierPrice.update({
      where: {
        tier: tier
      },
      data: updateData
    });

    return NextResponse.json({
      message: 'Subscription tier updated',
      data: result
    });

  } catch (error) {
    console.error('Error updating subscription tier price:', error);
    return NextResponse.json(
      { error: 'Failed to update subscription tier price' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/subscription/tiers/[tier]
 * Delete a specific subscription tier price
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ tier: string }> }
) {
  try {
    const session = await getServerSession(authOptions);

    // Only allow admins to access this endpoint
    if (!session || !session.user || !['ADMIN', 'SUPERADMIN'].includes(session.user.role)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const { tier } = resolvedParams;

    // Check if tier exists using Prisma model
    const existingTier = await db.subscriptionTierPrice.findUnique({
      where: {
        tier: tier
      }
    });

    if (!existingTier) {
      return NextResponse.json(
        { error: 'Subscription tier not found' },
        { status: 404 }
      );
    }

    // Don't allow deleting the NONE tier
    if (tier === 'NONE') {
      return NextResponse.json(
        { error: 'Cannot delete the NONE tier' },
        { status: 400 }
      );
    }

    // Delete the tier using Prisma model
    await db.subscriptionTierPrice.delete({
      where: {
        tier: tier
      }
    });

    return NextResponse.json({
      message: 'Subscription tier deleted'
    });

  } catch (error) {
    console.error('Error deleting subscription tier price:', error);
    return NextResponse.json(
      { error: 'Failed to delete subscription tier price' },
      { status: 500 }
    );
  }
}
