import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Unauthorized. Only SUPERADMIN can manage other admins.' }, { status: 401 });
    }

    // Get all admin users
    const adminUsers = await db.user.findMany({
      where: {
        OR: [
          { role: 'ADMIN' },
          { role: 'SUPERADMIN' }
        ]
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        emailVerified: true,
        image: true,
        createdAt: true,
        updatedAt: true,
        managedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        managedUsers: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(adminUsers);
  } catch (error) {
    console.error('Error fetching admin users:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Unauthorized. Only SUPERADMIN can create new admins.' }, { status: 401 });
    }

    // Get request body
    const data = await request.json();
    const { name, email, password, role, managedById } = data;

    // Validate input
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'Name, email, and password are required' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await db.user.findUnique({
      where: { email }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already in use' },
        { status: 400 }
      );
    }

    // Create new admin user
    const newAdmin = await db.user.create({
      data: {
        name,
        email,
        password, // In a real implementation, this should be hashed
        role: role === 'SUPERADMIN' ? 'SUPERADMIN' : 'ADMIN',
        ...(managedById && { managedById }),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
      }
    });

    return NextResponse.json({
      user: newAdmin,
      message: 'Admin user created successfully',
    });
  } catch (error) {
    console.error('Error creating admin user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Unauthorized. Only SUPERADMIN can update admin roles.' }, { status: 401 });
    }

    // Get request body
    const data = await request.json();
    const { userId, role, managedById } = data;

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await db.user.findUnique({
      where: { id: userId },
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent changing own role
    if (userId === user.id) {
      return NextResponse.json(
        { error: 'You cannot change your own role' },
        { status: 400 }
      );
    }

    // Update user role and manager
    const updatedUser = await db.user.update({
      where: { id: userId },
      data: {
        ...(role && { role }),
        ...(managedById !== undefined && { 
          managedById: managedById || null // Allow setting to null to remove manager
        }),
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        managedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      },
    });

    return NextResponse.json({
      user: updatedUser,
      message: 'Admin user updated successfully',
    });
  } catch (error) {
    console.error('Error updating admin user:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
