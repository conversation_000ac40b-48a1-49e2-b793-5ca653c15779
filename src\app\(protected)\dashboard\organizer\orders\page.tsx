import React from 'react'
import { Suspense } from 'react'
import { RoleGate } from '@/components/auth/role-gate'
import Link from 'next/link'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Clock, CheckCircle, AlertCircle, Ticket } from 'lucide-react'
import ActiveOrdersList from '@/components/orders/active-orders-list'
import CompletedOrdersList from '@/components/orders/completed-orders-list'
import CancelledOrdersList from '@/components/orders/cancelled-orders-list'

export default function OrdersPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Orders & Tickets</h1>
          <p className="text-gray-600">
            Manage orders and tickets for your events
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/orders/active" className="flex items-center gap-3">
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <h3 className="font-medium">Active Orders</h3>
                  <p className="text-sm text-gray-500">View pending orders</p>
                </div>
              </Link>
            </CardContent>
          </Card>

          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/orders/completed" className="flex items-center gap-3">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium">Completed Orders</h3>
                  <p className="text-sm text-gray-500">View successful orders</p>
                </div>
              </Link>
            </CardContent>
          </Card>

          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/orders/cancelled" className="flex items-center gap-3">
                <div className="bg-red-100 p-3 rounded-full">
                  <AlertCircle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-medium">Cancelled Orders</h3>
                  <p className="text-sm text-gray-500">View cancelled orders</p>
                </div>
              </Link>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="active" className="space-y-4">
          <TabsList>
            <TabsTrigger value="active" className="flex gap-2">
              <Clock className="h-4 w-4" /> Active
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex gap-2">
              <CheckCircle className="h-4 w-4" /> Completed
            </TabsTrigger>
            <TabsTrigger value="cancelled" className="flex gap-2">
              <AlertCircle className="h-4 w-4" /> Cancelled
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            <Suspense fallback={<div>Loading active orders...</div>}>
              <ActiveOrdersList />
            </Suspense>
          </TabsContent>

          <TabsContent value="completed">
            <Suspense fallback={<div>Loading completed orders...</div>}>
              <CompletedOrdersList />
            </Suspense>
          </TabsContent>

          <TabsContent value="cancelled">
            <Suspense fallback={<div>Loading cancelled orders...</div>}>
              <CancelledOrdersList />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  )
}
