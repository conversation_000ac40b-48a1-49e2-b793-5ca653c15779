import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { startOfMonth, subMonths } from 'date-fns';

/**
 * GET /api/admin/finance/statistics
 * Get transaction statistics for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current date and start of current month
    const now = new Date();
    const currentMonthStart = startOfMonth(now);
    const previousMonthStart = startOfMonth(subMonths(now, 1));
    const previousMonthEnd = subMonths(currentMonthStart, 1);

    // Get total transactions count (orders + NFC transactions)
    const [totalOrders, totalNfcTransactions] = await Promise.all([
      db.order.count(),
      db.vendorNFCTransaction.count()
    ]);

    const totalTransactions = totalOrders + totalNfcTransactions;

    // Get successful transactions count
    const [successfulOrders, successfulNfcTransactions] = await Promise.all([
      db.order.count({
        where: {
          status: 'Completed' // First letter capitalized for OrderStatus enum
        }
      }),
      db.vendorNFCTransaction.count({
        where: {
          status: 'COMPLETED' // All caps for TransactionStatus enum
        }
      })
    ]);

    const successfulTransactions = successfulOrders + successfulNfcTransactions;
    const successRate = totalTransactions > 0
      ? (successfulTransactions / totalTransactions) * 100
      : 0;

    // Get current month transactions
    const [currentMonthOrders, currentMonthNfcTransactions] = await Promise.all([
      db.order.count({
        where: {
          createdAt: { gte: currentMonthStart }
        }
      }),
      db.vendorNFCTransaction.count({
        where: {
          createdAt: { gte: currentMonthStart }
        }
      })
    ]);

    const currentMonthTransactions = currentMonthOrders + currentMonthNfcTransactions;

    // Get previous month transactions
    const [previousMonthOrders, previousMonthNfcTransactions] = await Promise.all([
      db.order.count({
        where: {
          createdAt: {
            gte: previousMonthStart,
            lt: currentMonthStart
          }
        }
      }),
      db.vendorNFCTransaction.count({
        where: {
          createdAt: {
            gte: previousMonthStart,
            lt: currentMonthStart
          }
        }
      })
    ]);

    const previousMonthTransactions = previousMonthOrders + previousMonthNfcTransactions;

    // Calculate month-over-month growth
    const transactionGrowth = previousMonthTransactions > 0
      ? ((currentMonthTransactions - previousMonthTransactions) / previousMonthTransactions) * 100
      : 0;

    // Calculate average transaction amount
    const [orderRevenue, nfcRevenue] = await Promise.all([
      db.order.aggregate({
        _sum: { totalPrice: true }
      }),
      db.vendorNFCTransaction.aggregate({
        _sum: { amount: true }
      })
    ]);

    const totalRevenue = (orderRevenue._sum.totalPrice || 0) + (nfcRevenue._sum.amount || 0);
    const averageTransactionAmount = totalTransactions > 0
      ? totalRevenue / totalTransactions
      : 0;

    // Calculate previous month average transaction amount
    const [prevMonthOrderRevenue, prevMonthNfcRevenue] = await Promise.all([
      db.order.aggregate({
        where: {
          createdAt: {
            gte: previousMonthStart,
            lt: currentMonthStart
          }
        },
        _sum: { totalPrice: true }
      }),
      db.vendorNFCTransaction.aggregate({
        where: {
          createdAt: {
            gte: previousMonthStart,
            lt: currentMonthStart
          }
        },
        _sum: { amount: true }
      })
    ]);

    const prevMonthTotalRevenue = (prevMonthOrderRevenue._sum.totalPrice || 0) + (prevMonthNfcRevenue._sum.amount || 0);
    const prevMonthAverageAmount = previousMonthTransactions > 0
      ? prevMonthTotalRevenue / previousMonthTransactions
      : 0;

    // Calculate average amount growth
    const averageAmountGrowth = prevMonthAverageAmount > 0
      ? ((averageTransactionAmount - prevMonthAverageAmount) / prevMonthAverageAmount) * 100
      : 0;

    // Get failed transactions count
    const [failedOrders, failedNfcTransactions] = await Promise.all([
      db.order.count({
        where: {
          status: 'Cancelled' // Using 'Cancelled' as there's no 'Failed' in OrderStatus enum
        }
      }),
      db.vendorNFCTransaction.count({
        where: {
          status: 'FAILED'
        }
      })
    ]);

    const failedTransactions = failedOrders + failedNfcTransactions;
    const failureRate = totalTransactions > 0
      ? (failedTransactions / totalTransactions) * 100
      : 0;

    return NextResponse.json({
      totalTransactions,
      successfulTransactions,
      successRate,
      failedTransactions,
      failureRate,
      transactionGrowth,
      averageTransactionAmount,
      averageAmountGrowth
    });
  } catch (error) {
    console.error('Error fetching transaction statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transaction statistics' },
      { status: 500 }
    );
  }
}
