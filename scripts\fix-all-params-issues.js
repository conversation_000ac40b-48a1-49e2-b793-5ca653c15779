#!/usr/bin/env node

/**
 * Comprehensive fix for Next.js 15+ Promise-based params
 * This script fixes ALL params-related issues in API routes and pages
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Comprehensive fix for ALL Next.js 15+ Promise-based params issues...\n');

// Find all route.ts files
function findAllApiRoutes(dir) {
  const routes = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        scanDirectory(fullPath);
      } else if (item === 'route.ts') {
        routes.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return routes;
}

// Check if file needs updating
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  
  // Check for problematic patterns
  const problematicPatterns = [
    /const\s*\{\s*[^}]*\}\s*=\s*params\s*;/,  // const { ... } = params;
    /params\.[a-zA-Z_$][a-zA-Z0-9_$]*/,       // params.something
    /params\s*:\s*\{\s*[^}]*\s*\}(?!\s*\|\s*Promise)/,  // params: { ... } (not union with Promise)
  ];
  
  for (const pattern of problematicPatterns) {
    if (pattern.test(content)) {
      // Skip if already properly handled
      if (content.includes('await params') || content.includes('await Promise.resolve(params)')) {
        continue;
      }
      return true;
    }
  }
  
  return false;
}

// Update file to fix all params issues
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Pattern 1: Fix type definitions - ensure params is Promise<{...}>
  const httpMethods = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE'];
  
  for (const method of httpMethods) {
    // Fix function signatures with params
    const functionPattern = new RegExp(
      `(export\\s+async\\s+function\\s+${method}\\s*\\([^,]+,\\s*(?:\\{\\s*params\\s*\\}|\\w+)\\s*:\\s*\\{\\s*params:\\s*)\\{([^}]+)\\}(\\s*\\})`,
      'g'
    );
    
    content = content.replace(functionPattern, (match, prefix, paramTypes, suffix) => {
      if (!match.includes('Promise<{')) {
        updated = true;
        return `${prefix}Promise<{${paramTypes}}>${suffix}`;
      }
      return match;
    });
  }
  
  // Pattern 2: Fix destructuring assignments
  // Replace: const { id } = params; with: const { id } = await params;
  content = content.replace(
    /(\s+)const\s*\{\s*([^}]+)\s*\}\s*=\s*params\s*;/g,
    (match, indent, destructured) => {
      updated = true;
      return `${indent}const { ${destructured} } = await params;`;
    }
  );
  
  // Pattern 3: Fix direct property access
  // Replace: params.id with: (await params).id
  content = content.replace(
    /(?<!await\s)(?<!await\s+)params\.([a-zA-Z_$][a-zA-Z0-9_$]*)/g,
    (match, property) => {
      // Skip if already in an await context
      if (content.includes('await params') && content.indexOf('await params') < content.indexOf(match)) {
        return match;
      }
      updated = true;
      return `(await params).${property}`;
    }
  );
  
  // Pattern 4: Fix variable assignments from params
  // Replace: const partnerId = params.id; with: const { id: partnerId } = await params;
  content = content.replace(
    /(\s+)const\s+(\w+)\s*=\s*params\.(\w+)\s*;/g,
    (match, indent, varName, property) => {
      updated = true;
      if (varName === property) {
        return `${indent}const { ${property} } = await params;`;
      } else {
        return `${indent}const { ${property}: ${varName} } = await params;`;
      }
    }
  );
  
  // Pattern 5: Handle props.params pattern
  content = content.replace(
    /(\s+)const\s+params\s*=\s*await\s+props\.params\s*;\s*\n\s*const\s*\{\s*([^}]+)\s*\}\s*=\s*params\s*;/g,
    (match, indent, destructured) => {
      updated = true;
      return `${indent}const { ${destructured} } = await props.params;`;
    }
  );
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Updated: ${filePath}`);
  }
  
  return updated;
}

// Main execution
try {
  const apiDir = path.join(__dirname, '..', 'src', 'app', 'api');
  console.log(`Searching for ALL API routes in ${apiDir}...`);
  
  const allRoutes = findAllApiRoutes(apiDir);
  console.log(`Found ${allRoutes.length} API route files\n`);
  
  let updatedCount = 0;
  
  for (const routePath of allRoutes) {
    const relativePath = path.relative(process.cwd(), routePath);
    
    if (needsUpdate(routePath)) {
      if (updateFile(routePath)) {
        updatedCount++;
      }
    } else {
      console.log(`⏭️  Skipped: ${relativePath} (already updated or no params)`);
    }
  }
  
  console.log(`\n🎉 Updated ${updatedCount} API route files for Next.js 15+ compatibility`);
  
  console.log(`\n📋 Next steps:`);
  console.log(`1. Test the build: npm run build`);
  console.log(`2. Check for any remaining TypeScript errors`);
  console.log(`3. Test the API functionality`);
  
} catch (error) {
  console.error('❌ Error:', error);
  process.exit(1);
}
