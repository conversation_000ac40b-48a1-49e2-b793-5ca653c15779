import { Suspense } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Loader2, Store, CheckCircle, XCircle, Filter } from 'lucide-react';
import Link from 'next/link';
import VendorApplicationsList from '@/components/organizer/vendor-applications-list';

export default function VendorApplicationsPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold mb-2">Vendor Applications</h1>
          <p className="text-gray-600">
            Manage vendor applications for your events
          </p>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/events/vendors?status=pending" className="flex items-center gap-3">
                <div className="bg-yellow-100 p-3 rounded-full">
                  <Store className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <h3 className="font-medium">Pending Applications</h3>
                  <p className="text-sm text-gray-500">Review new vendor applications</p>
                </div>
              </Link>
            </CardContent>
          </Card>

          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/events/vendors?status=approved" className="flex items-center gap-3">
                <div className="bg-green-100 p-3 rounded-full">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium">Approved Vendors</h3>
                  <p className="text-sm text-gray-500">View approved vendors for your events</p>
                </div>
              </Link>
            </CardContent>
          </Card>

          <Card className="flex-1">
            <CardContent className="pt-6">
              <Link href="/dashboard/organizer/events/vendors?status=rejected" className="flex items-center gap-3">
                <div className="bg-red-100 p-3 rounded-full">
                  <XCircle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <h3 className="font-medium">Rejected Applications</h3>
                  <p className="text-sm text-gray-500">View rejected vendor applications</p>
                </div>
              </Link>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="pending" className="space-y-4">
          <TabsList>
            <TabsTrigger value="pending" className="flex gap-2">
              <Store className="h-4 w-4" /> Pending
            </TabsTrigger>
            <TabsTrigger value="approved" className="flex gap-2">
              <CheckCircle className="h-4 w-4" /> Approved
            </TabsTrigger>
            <TabsTrigger value="rejected" className="flex gap-2">
              <XCircle className="h-4 w-4" /> Rejected
            </TabsTrigger>
            <TabsTrigger value="all" className="flex gap-2">
              <Filter className="h-4 w-4" /> All
            </TabsTrigger>
          </TabsList>

          <TabsContent value="pending">
            <Suspense fallback={<div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <VendorApplicationsList status="PENDING" />
            </Suspense>
          </TabsContent>

          <TabsContent value="approved">
            <Suspense fallback={<div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <VendorApplicationsList status="APPROVED" />
            </Suspense>
          </TabsContent>

          <TabsContent value="rejected">
            <Suspense fallback={<div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <VendorApplicationsList status="REJECTED" />
            </Suspense>
          </TabsContent>

          <TabsContent value="all">
            <Suspense fallback={<div className="flex justify-center py-8"><Loader2 className="h-8 w-8 animate-spin" /></div>}>
              <VendorApplicationsList status="ALL" />
            </Suspense>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
