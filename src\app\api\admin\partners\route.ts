import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/partners
 * Get all partners for admin dashboard
 */
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can access partner data
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get URL parameters
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};

    if (type && type !== 'all') {
      where.partnerType = type;
    }

    if (status && status !== 'all') {
      switch (status) {
        case 'verified':
          where.isVerified = true;
          break;
        case 'pending':
          where.isVerified = false;
          break;
        case 'active':
          // Use verified as active since there's no isActive field
          where.isVerified = true;
          break;
        case 'inactive':
          // Use unverified as inactive
          where.isVerified = false;
          break;
      }
    }

    if (search) {
      where.OR = [
        {
          businessName: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          city: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          province: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ];
    }

    // Get partners with user information
    const partners = await db.partner.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.partner.count({ where });

    // Calculate summary statistics
    const totalPartners = await db.partner.count();
    const verifiedPartners = await db.partner.count({
      where: { isVerified: true }
    });
    const pendingVerification = await db.partner.count({
      where: { isVerified: false }
    });
    // Use verified partners as "active" since there's no isActive field
    const activePartners = verifiedPartners;

    // Format partners for response
    const formattedPartners = partners.map(partner => ({
      id: partner.id,
      businessName: partner.businessName,
      partnerType: partner.partnerType,
      tier: partner.tier,
      isVerified: partner.isVerified,
      // isActive: partner.isActive, // Removed: not present in model
      rating: partner.rating,
      totalReviews: partner.totalReviews,
      city: partner.city,
      province: partner.province,
      createdAt: partner.createdAt.toISOString(),
      user: {
        id: partner.user.id,
        name: partner.user.name,
        email: partner.user.email
      }
    }));

    return NextResponse.json({
      partners: formattedPartners,
      pagination: {
        total: totalCount,
        page,
        limit,
        pages: Math.ceil(totalCount / limit)
      },
      summary: {
        totalPartners,
        verifiedPartners,
        pendingVerification,
        activePartners
      }
    });

  } catch (error) {
    console.error('Error fetching partners:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
