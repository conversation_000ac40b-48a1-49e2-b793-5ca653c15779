import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Award, 
  Users, 
  Gift, 
  TrendingUp,
  Star,
  Settings,
  Plus,
  Edit,
  Crown,
  Target
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};

// Mock loyalty program data
const loyaltyStats = {
  totalMembers: 1247,
  activeMembers: 892,
  pointsIssued: 45670,
  pointsRedeemed: 23450,
  redemptionRate: 51.3,
  averagePointsPerMember: 51.2
};

const loyaltyTiers = [
  {
    name: 'Bronze',
    minPoints: 0,
    maxPoints: 499,
    benefits: ['5% discount on services', 'Birthday special offer'],
    members: 623,
    color: 'bg-orange-100 text-orange-800'
  },
  {
    name: 'Silver',
    minPoints: 500,
    maxPoints: 1499,
    benefits: ['10% discount on services', 'Priority booking', 'Free breakfast'],
    members: 456,
    color: 'bg-gray-100 text-gray-800'
  },
  {
    name: 'Gold',
    minPoints: 1500,
    maxPoints: 4999,
    benefits: ['15% discount on services', 'Room upgrades', 'Late checkout'],
    members: 134,
    color: 'bg-yellow-100 text-yellow-800'
  },
  {
    name: 'Platinum',
    minPoints: 5000,
    maxPoints: null,
    benefits: ['20% discount on services', 'VIP treatment', 'Concierge service'],
    members: 34,
    color: 'bg-purple-100 text-purple-800'
  }
];

const recentActivity = [
  {
    id: '1',
    customerName: 'John Smith',
    action: 'Earned 50 points',
    description: 'Room booking - Deluxe Suite',
    date: new Date(Date.now() - 2 * 60 * 60 * 1000),
    points: 50
  },
  {
    id: '2',
    customerName: 'Sarah Johnson',
    action: 'Redeemed 200 points',
    description: 'Free spa treatment',
    date: new Date(Date.now() - 4 * 60 * 60 * 1000),
    points: -200
  },
  {
    id: '3',
    customerName: 'Mike Wilson',
    action: 'Tier upgrade',
    description: 'Promoted to Silver tier',
    date: new Date(Date.now() - 6 * 60 * 60 * 1000),
    points: 0
  }
];

export default async function PartnerLoyaltyPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Loyalty Program</h1>
          <p className="text-gray-500 mt-1">
            Manage your customer loyalty program and rewards
          </p>
        </div>
        <Button className="mt-4 md:mt-0">
          <Settings className="mr-2 h-4 w-4" />
          Program Settings
        </Button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loyaltyStats.totalMembers.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {loyaltyStats.activeMembers} active this month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Points Issued</CardTitle>
            <Gift className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loyaltyStats.pointsIssued.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              +12% from last month
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Redemption Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loyaltyStats.redemptionRate}%</div>
            <p className="text-xs text-muted-foreground">
              {loyaltyStats.pointsRedeemed.toLocaleString()} points redeemed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Points/Member</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{loyaltyStats.averagePointsPerMember}</div>
            <p className="text-xs text-muted-foreground">
              Per active member
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Loyalty Tiers */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                Loyalty Tiers
              </CardTitle>
              <CardDescription>
                Manage your customer loyalty tiers and benefits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {loyaltyTiers.map((tier, index) => (
                <div key={tier.name} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Badge className={tier.color}>{tier.name}</Badge>
                      <span className="text-sm text-gray-500">
                        {tier.minPoints} - {tier.maxPoints || '∞'} points
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{tier.members} members</span>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-1">
                    {tier.benefits.map((benefit, benefitIndex) => (
                      <div key={benefitIndex} className="flex items-center gap-2 text-sm">
                        <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                        {benefit}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
              <Button variant="outline" className="w-full">
                <Plus className="mr-2 h-4 w-4" />
                Add New Tier
              </Button>
            </CardContent>
          </Card>

          {/* Program Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Program Configuration
              </CardTitle>
              <CardDescription>
                Configure how customers earn and redeem points
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="program-active">Program Active</Label>
                  <p className="text-sm text-gray-500">Enable or disable the loyalty program</p>
                </div>
                <Switch id="program-active" defaultChecked />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="earn-rate">Points per K1 spent</Label>
                  <Input 
                    id="earn-rate" 
                    defaultValue="1" 
                    className="mt-1"
                    type="number"
                  />
                </div>
                <div>
                  <Label htmlFor="redeem-rate">Points value (K per point)</Label>
                  <Input 
                    id="redeem-rate" 
                    defaultValue="0.10" 
                    className="mt-1"
                    type="number"
                    step="0.01"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="auto-enroll">Auto-enroll new customers</Label>
                  <p className="text-sm text-gray-500">Automatically add new customers to the program</p>
                </div>
                <Switch id="auto-enroll" defaultChecked />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="birthday-bonus">Birthday bonus points</Label>
                  <p className="text-sm text-gray-500">Give bonus points on customer birthdays</p>
                </div>
                <Switch id="birthday-bonus" defaultChecked />
              </div>

              <Button className="w-full">
                Save Configuration
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {recentActivity.map((activity) => (
                <div key={activity.id} className="border-b pb-3 last:border-b-0">
                  <div className="flex items-start justify-between">
                    <div>
                      <p className="font-medium text-sm">{activity.customerName}</p>
                      <p className="text-sm text-gray-600">{activity.action}</p>
                      <p className="text-xs text-gray-500">{activity.description}</p>
                    </div>
                    <div className="text-right">
                      <p className={`text-sm font-medium ${
                        activity.points > 0 ? 'text-green-600' : 
                        activity.points < 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {activity.points > 0 ? '+' : ''}{activity.points || '—'}
                      </p>
                      <p className="text-xs text-gray-500">{formatDate(activity.date)}</p>
                    </div>
                  </div>
                </div>
              ))}
              <Button variant="outline" size="sm" className="w-full">
                View All Activity
              </Button>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Gift className="mr-2 h-4 w-4" />
                Issue Bonus Points
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Users className="mr-2 h-4 w-4" />
                View All Members
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <TrendingUp className="mr-2 h-4 w-4" />
                Analytics Report
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Award className="mr-2 h-4 w-4" />
                Create Reward
              </Button>
            </CardContent>
          </Card>

          {/* Program Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Member Growth</span>
                <span className="text-sm font-medium text-green-600">+8.2%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Engagement Rate</span>
                <span className="text-sm font-medium text-blue-600">71.5%</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Avg. Visit Frequency</span>
                <span className="text-sm font-medium">2.3x/month</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Customer Lifetime Value</span>
                <span className="text-sm font-medium">K1,245</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
