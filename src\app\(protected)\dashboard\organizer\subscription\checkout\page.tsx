'use client';

import React, { useState, useEffect } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { RoleGate } from '@/components/auth/role-gate';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import {
  CreditCard,
  CheckCircle,
  ArrowLeft,
  Loader2,
  Calendar,
  Lock
} from 'lucide-react';

// Define subscription tiers and their features
const SUBSCRIPTION_TIERS = {
  BASIC: {
    name: 'Basic',
    monthlyPrice: 29.99,
    yearlyPrice: 299.99,
    color: 'bg-gray-100',
    textColor: 'text-gray-800',
    borderColor: 'border-gray-300',
  },
  PREMIUM: {
    name: 'Premium',
    monthlyPrice: 79.99,
    yearlyPrice: 799.99,
    color: 'bg-orange-50',
    textColor: 'text-orange-800',
    borderColor: 'border-orange-300',
  },
  ELITE: {
    name: 'Elite',
    monthlyPrice: 199.99,
    yearlyPrice: 1999.99,
    color: 'bg-blue-50',
    textColor: 'text-blue-800',
    borderColor: 'border-blue-300',
  }
};

export default function SubscriptionCheckoutPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();

  const [tier, setTier] = useState<'BASIC' | 'PREMIUM' | 'ELITE' | null>(null);
  const [cycle, setCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [tierData, setTierData] = useState(SUBSCRIPTION_TIERS);

  // Payment form state
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [cardName, setCardName] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [cardExpiry, setCardExpiry] = useState('');
  const [cardCvc, setCardCvc] = useState('');

  useEffect(() => {
    const fetchTierData = async () => {
      try {
        // Fetch tier features and pricing
        const featuresResponse = await fetch('/api/organizer/subscription/features');
        const featuresData = await featuresResponse.json();

        if (featuresData) {
          // Update tier data with fetched information
          const updatedTiers = { ...SUBSCRIPTION_TIERS };

          Object.keys(featuresData).forEach(tier => {
            if (updatedTiers[tier as keyof typeof updatedTiers]) {
              updatedTiers[tier as keyof typeof updatedTiers] = {
                ...updatedTiers[tier as keyof typeof updatedTiers],
                monthlyPrice: featuresData[tier].monthlyPrice,
                yearlyPrice: featuresData[tier].yearlyPrice
              };
            }
          });

          setTierData(updatedTiers);
        }
      } catch (error) {
        console.error('Error fetching tier data:', error);
      }
    };

    const tierParam = searchParams?.get('tier') as 'BASIC' | 'PREMIUM' | 'ELITE' | null;
    const cycleParam = searchParams?.get('cycle') as 'monthly' | 'yearly' | null;

    if (tierParam && Object.keys(SUBSCRIPTION_TIERS).includes(tierParam)) {
      setTier(tierParam);
      fetchTierData();
    } else {
      // Redirect back if no valid tier is provided
      router.push('/dashboard/organizer/subscription');
    }

    if (cycleParam && ['monthly', 'yearly'].includes(cycleParam)) {
      setCycle(cycleParam);
    }
  }, [searchParams, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tier) return;

    setIsLoading(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // In a real implementation, you would call your API to process the payment
      // and update the user's subscription

      // const response = await fetch('/api/organizer/subscription', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify({
      //     tier,
      //     cycle,
      //     paymentMethod,
      //     // Include other payment details as needed
      //   }),
      // });

      // if (!response.ok) {
      //   throw new Error('Failed to process payment');
      // }

      setIsSuccess(true);

      toast({
        title: 'Subscription Activated',
        description: `Your ${tierData[tier].name} plan has been successfully activated.`,
      });

      // Redirect after a short delay
      setTimeout(() => {
        router.push('/dashboard/organizer/subscription');
      }, 3000);

    } catch (error) {
      console.error('Error processing payment:', error);
      toast({
        title: 'Payment Failed',
        description: error instanceof Error ? error.message : 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const getPrice = () => {
    if (!tier) return 0;
    return cycle === 'monthly'
      ? tierData[tier].monthlyPrice
      : tierData[tier].yearlyPrice;
  };

  if (isSuccess) {
    return (
      <RoleGate allowedRole="ORGANIZER">
        <div className="max-w-md mx-auto mt-12">
          <Card>
            <CardHeader className="bg-green-50 border-b">
              <CardTitle className="flex items-center text-green-800">
                <CheckCircle className="mr-2 h-6 w-6 text-green-600" />
                Payment Successful
              </CardTitle>
              <CardDescription className="text-green-700">
                Your subscription has been activated
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-6 text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full mx-auto flex items-center justify-center mb-4">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold mb-2">Thank You!</h3>
              <p className="text-gray-600 mb-6">
                Your {tier && tierData[tier].name} subscription has been successfully activated.
                You will be redirected to your subscription dashboard shortly.
              </p>
              <Button
                className="w-full"
                onClick={() => router.push('/dashboard/organizer/subscription')}
              >
                Go to Subscription Dashboard
              </Button>
            </CardContent>
          </Card>
        </div>
      </RoleGate>
    );
  }

  if (!tier) {
    return (
      <RoleGate allowedRole="ORGANIZER">
        <div className="max-w-md mx-auto mt-12">
          <Card>
            <CardHeader>
              <CardTitle>Loading...</CardTitle>
            </CardHeader>
            <CardContent className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            </CardContent>
          </Card>
        </div>
      </RoleGate>
    );
  }

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="max-w-3xl mx-auto">
        <Button
          variant="ghost"
          className="mb-6"
          onClick={() => router.push('/dashboard/organizer/subscription')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Plans
        </Button>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader className="border-b">
                <CardTitle className="flex items-center">
                  <CreditCard className="mr-2 h-5 w-5" />
                  Payment Information
                </CardTitle>
                <CardDescription>
                  Enter your payment details to complete your subscription
                </CardDescription>
              </CardHeader>
              <form onSubmit={handleSubmit}>
                <CardContent className="pt-6 space-y-6">
                  {/* Payment Method Selection */}
                  <div className="space-y-3">
                    <Label className="text-base font-medium">Payment Method</Label>
                    <RadioGroup
                      value={paymentMethod}
                      onValueChange={setPaymentMethod}
                      className="grid grid-cols-2 gap-3"
                    >
                      <Label
                        htmlFor="card"
                        className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                          paymentMethod === 'card'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'
                        }`}
                      >
                        <RadioGroupItem value="card" id="card" className="sr-only" />
                        <CreditCard className={`h-5 w-5 mr-2 ${paymentMethod === 'card' ? 'text-blue-500' : 'text-gray-400'}`} />
                        <span className={paymentMethod === 'card' ? 'font-medium' : ''}>Credit/Debit Card</span>
                      </Label>

                      <Label
                        htmlFor="mobile"
                        className={`flex items-center p-3 border-2 rounded-lg cursor-pointer transition-all ${
                          paymentMethod === 'mobile'
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-gray-200 hover:border-blue-200 hover:bg-blue-50/30'
                        }`}
                      >
                        <RadioGroupItem value="mobile" id="mobile" className="sr-only" />
                        <svg className={`h-5 w-5 mr-2 ${paymentMethod === 'mobile' ? 'text-blue-500' : 'text-gray-400'}`} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <path d="M16 2H8C6.89543 2 6 2.89543 6 4V20C6 21.1046 6.89543 22 8 22H16C17.1046 22 18 21.1046 18 20V4C18 2.89543 17.1046 2 16 2Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                          <path d="M12 18H12.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        </svg>
                        <span className={paymentMethod === 'mobile' ? 'font-medium' : ''}>Mobile Money</span>
                      </Label>
                    </RadioGroup>
                  </div>

                  {/* Card Details */}
                  {paymentMethod === 'card' && (
                    <div className="space-y-4 bg-white p-4 rounded-lg border border-gray-200">
                      <div className="space-y-2">
                        <Label htmlFor="cardName" className="text-sm font-medium">Cardholder Name</Label>
                        <Input
                          id="cardName"
                          value={cardName}
                          onChange={(e) => setCardName(e.target.value)}
                          placeholder="John Doe"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cardNumber" className="text-sm font-medium">Card Number</Label>
                        <div className="relative">
                          <Input
                            id="cardNumber"
                            value={cardNumber}
                            onChange={(e) => setCardNumber(e.target.value)}
                            placeholder="4242 4242 4242 4242"
                            className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 pl-10"
                            required
                          />
                          <CreditCard className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="cardExpiry" className="text-sm font-medium">Expiry Date</Label>
                          <div className="relative">
                            <Input
                              id="cardExpiry"
                              value={cardExpiry}
                              onChange={(e) => setCardExpiry(e.target.value)}
                              placeholder="MM/YY"
                              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 pl-10"
                              required
                            />
                            <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cardCvc" className="text-sm font-medium">CVC</Label>
                          <div className="relative">
                            <Input
                              id="cardCvc"
                              value={cardCvc}
                              onChange={(e) => setCardCvc(e.target.value)}
                              placeholder="123"
                              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 pl-10"
                              required
                            />
                            <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Mobile Money Details */}
                  {paymentMethod === 'mobile' && (
                    <div className="space-y-4 bg-white p-4 rounded-lg border border-gray-200">
                      <div className="space-y-2">
                        <Label htmlFor="mobileNumber" className="text-sm font-medium">Mobile Number</Label>
                        <Input
                          id="mobileNumber"
                          placeholder="+260 97X XXX XXX"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                          required
                        />
                      </div>
                      <p className="text-sm text-gray-500">
                        You will receive a prompt on your mobile phone to complete the payment.
                      </p>
                    </div>
                  )}

                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <div className="flex items-center text-sm text-gray-500 mb-2">
                      <Lock className="h-4 w-4 mr-1 text-green-500" />
                      Secure Payment
                    </div>
                    <p className="text-sm text-gray-600">
                      Your payment information is encrypted and secure. We do not store your full card details.
                    </p>
                  </div>
                </CardContent>
                <CardFooter className="border-t bg-gray-50 py-4">
                  <Button
                    type="submit"
                    className="w-full bg-blue-500 hover:bg-blue-600"
                    disabled={isLoading}
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      `Pay $${getPrice().toFixed(2)}`
                    )}
                  </Button>
                </CardFooter>
              </form>
            </Card>
          </div>

          <div>
            <Card>
              <CardHeader className={`${tierData[tier].color} border-b`}>
                <CardTitle className={tierData[tier].textColor}>
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-6 space-y-4">
                <div className="flex justify-between items-center">
                  <span className="font-medium">{tierData[tier].name} Plan</span>
                  <span>${cycle === 'monthly'
                    ? tierData[tier].monthlyPrice.toFixed(2)
                    : tierData[tier].yearlyPrice.toFixed(2)}
                  </span>
                </div>

                <div className="flex justify-between items-center text-sm text-gray-600">
                  <span>Billing Cycle</span>
                  <span className="capitalize">{cycle}</span>
                </div>

                {cycle === 'yearly' && (
                  <div className="flex justify-between items-center text-sm text-green-600">
                    <span>Yearly Discount</span>
                    <span>Save 16%</span>
                  </div>
                )}

                <Separator />

                <div className="flex justify-between items-center font-bold">
                  <span>Total</span>
                  <span>${getPrice().toFixed(2)}</span>
                </div>

                <div className="text-sm text-gray-600">
                  {cycle === 'monthly'
                    ? 'Billed monthly. Cancel anytime.'
                    : 'Billed annually. Cancel anytime.'}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </RoleGate>
  );
}
