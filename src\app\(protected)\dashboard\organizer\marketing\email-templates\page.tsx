import React, { Suspense } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import EmailTemplatesClient from '@/components/email-templates/email-templates-client';

export default function EmailTemplatesPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <Suspense fallback={<div>Loading email templates...</div>}>
        <EmailTemplatesClient />
      </Suspense>
    </RoleGate>
  );
}
