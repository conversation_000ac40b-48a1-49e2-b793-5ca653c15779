'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertCircle, Check, Edit, Plus, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

interface SubscriptionTier {
  tier: string;
  monthlyPrice: number;
  yearlyPrice: number;
  commissionRate: number;
  maxEvents: number | null;
  maxTeamMembers: number | null;
  maxEmailCampaigns: number | null;
  maxAnalyticsReports: number | null;
  maxVendorManagement: number | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

const tierNames: Record<string, string> = {
  'NONE': 'Free',
  'BASIC': 'Basic',
  'PREMIUM': 'Premium',
  'ELITE': 'Elite'
};

export default function SubscriptionTiersPage() {
  const router = useRouter();
  const [tiers, setTiers] = useState<SubscriptionTier[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingTier, setEditingTier] = useState<SubscriptionTier | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [tierToDelete, setTierToDelete] = useState<string | null>(null);

  // Fetch subscription tiers
  const fetchTiers = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/subscription/tiers');

      if (!response.ok) {
        throw new Error('Failed to fetch subscription tiers');
      }

      const data = await response.json();
      setTiers(data);
    } catch (error) {
      console.error('Error fetching subscription tiers:', error);
      toast.error('Failed to fetch subscription tiers');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTiers();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const response = await fetch('/api/admin/subscription/tiers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editingTier),
      });

      if (!response.ok) {
        throw new Error('Failed to save subscription tier');
      }

      toast.success('Subscription tier saved successfully');
      setIsDialogOpen(false);
      fetchTiers();
    } catch (error) {
      console.error('Error saving subscription tier:', error);
      toast.error('Failed to save subscription tier');
    }
  };

  // Handle tier deletion
  const handleDelete = async () => {
    if (!tierToDelete) return;

    try {
      const response = await fetch(`/api/admin/subscription/tiers/${tierToDelete}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete subscription tier');
      }

      toast.success('Subscription tier deleted successfully');
      setIsDeleteDialogOpen(false);
      setTierToDelete(null);
      fetchTiers();
    } catch (error) {
      console.error('Error deleting subscription tier:', error);
      toast.error('Failed to delete subscription tier');
    }
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!editingTier) return;

    const { name, value, type } = e.target;

    setEditingTier({
      ...editingTier,
      [name]: type === 'number' ? (value === '' ? null : parseFloat(value)) : value,
    });
  };

  // Handle switch change
  const handleSwitchChange = (checked: boolean) => {
    if (!editingTier) return;

    setEditingTier({
      ...editingTier,
      isActive: checked,
    });
  };

  // Create a new tier
  const handleCreateTier = () => {
    setEditingTier({
      tier: '',
      monthlyPrice: 0,
      yearlyPrice: 0,
      commissionRate: 0,
      maxEvents: null,
      maxTeamMembers: null,
      maxEmailCampaigns: null,
      maxAnalyticsReports: null,
      maxVendorManagement: null,
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    });
    setIsDialogOpen(true);
  };

  // Edit an existing tier
  const handleEditTier = (tier: SubscriptionTier) => {
    setEditingTier({ ...tier });
    setIsDialogOpen(true);
  };

  // Confirm tier deletion
  const confirmDelete = (tier: string) => {
    setTierToDelete(tier);
    setIsDeleteDialogOpen(true);
  };

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">Subscription Tiers</h1>
          <p className="text-gray-500 mt-1">Manage subscription tiers and pricing</p>
        </div>
        <Button onClick={handleCreateTier}>
          <Plus className="mr-2 h-4 w-4" />
          Add Tier
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Subscription Tiers</CardTitle>
          <CardDescription>
            Configure pricing, commission rates, and feature limits for each subscription tier
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tier</TableHead>
                  <TableHead>Monthly Price</TableHead>
                  <TableHead>Yearly Price</TableHead>
                  <TableHead>Commission Rate</TableHead>
                  <TableHead>Max Events</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tiers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No subscription tiers found. Click "Add Tier" to create one.
                    </TableCell>
                  </TableRow>
                ) : (
                  tiers.map((tier) => (
                    <TableRow key={tier.tier}>
                      <TableCell className="font-medium">
                        {tierNames[tier.tier] || tier.tier}
                      </TableCell>
                      <TableCell>K{tier.monthlyPrice.toFixed(2)}</TableCell>
                      <TableCell>K{tier.yearlyPrice.toFixed(2)}</TableCell>
                      <TableCell>{(tier.commissionRate * 100).toFixed(0)}%</TableCell>
                      <TableCell>
                        {tier.maxEvents === null ? 'Unlimited' : tier.maxEvents}
                      </TableCell>
                      <TableCell>
                        <Badge variant={tier.isActive ? 'default' : 'secondary'}>
                          {tier.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditTier(tier)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {tier.tier !== 'NONE' && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => confirmDelete(tier.tier)}
                              className="text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit/Create Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[1200px] w-[95vw] border-4 border-orange-500 max-h-[90vh] overflow-y-auto">
          <DialogHeader className="bg-blue-600 p-4 rounded-t-md">
            <DialogTitle className="text-white font-bold text-xl">
              {editingTier?.tier ? `Edit ${tierNames[editingTier.tier] || editingTier.tier} Tier` : 'Create New Tier'}
            </DialogTitle>
            <DialogDescription className="text-white">
              Configure the subscription tier details and feature limits
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4 px-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                {/* First column - Tier ID and Monthly/Yearly Price */}
                <div className="space-y-4">
                  {!editingTier?.tier && (
                    <div className="grid grid-cols-5 items-center gap-2">
                      <Label htmlFor="tier" className="col-span-2 text-right text-blue-600 font-bold">
                        Tier ID
                      </Label>
                      <div className="col-span-3">
                        <Input
                          id="tier"
                          name="tier"
                          value={editingTier?.tier || ''}
                          onChange={handleInputChange}
                          required
                          placeholder="e.g., BASIC, PREMIUM, ELITE"
                          className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                        />
                        <p className="text-xs text-orange-500 font-medium mt-1">
                          Use uppercase letters only (e.g., BASIC, PREMIUM, ELITE)
                        </p>
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="monthlyPrice" className="col-span-2 text-right text-blue-600 font-bold">
                      Monthly Price
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="monthlyPrice"
                        name="monthlyPrice"
                        type="number"
                        step="0.01"
                        value={editingTier?.monthlyPrice === 0 ? '' : editingTier?.monthlyPrice}
                        onChange={handleInputChange}
                        required
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="yearlyPrice" className="col-span-2 text-right text-blue-600 font-bold">
                      Yearly Price
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="yearlyPrice"
                        name="yearlyPrice"
                        type="number"
                        step="0.01"
                        value={editingTier?.yearlyPrice === 0 ? '' : editingTier?.yearlyPrice}
                        onChange={handleInputChange}
                        required
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Second column - Commission Rate */}
                <div className="space-y-4">
                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="commissionRate" className="col-span-2 text-right text-blue-600 font-bold">
                      Commission Rate (%)
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="commissionRate"
                        name="commissionRate"
                        type="number"
                        step="0.01"
                        min="0"
                        max="100"
                        value={editingTier?.commissionRate ? (editingTier.commissionRate * 100) : ''}
                        onChange={(e) => {
                          if (!editingTier) return;
                          const value = e.target.value === '' ? 0 : parseFloat(e.target.value);
                          setEditingTier({
                            ...editingTier,
                            commissionRate: value / 100,
                          });
                        }}
                        required
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                      <p className="text-xs text-orange-500 font-medium mt-1">
                        Enter as percentage (e.g., 6 for 6%)
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-orange-500 h-1" />

              <h3 className="font-bold text-blue-600 text-lg my-3 bg-blue-100 p-2 rounded-md border-l-4 border-blue-500">Feature Limits</h3>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
                {/* First column */}
                <div className="space-y-4">
                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="maxEvents" className="col-span-2 text-right text-blue-600 font-bold">
                      Max Events
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="maxEvents"
                        name="maxEvents"
                        type="number"
                        value={editingTier?.maxEvents === null ? '' : editingTier?.maxEvents}
                        onChange={handleInputChange}
                        placeholder="Leave empty for unlimited"
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="maxTeamMembers" className="col-span-2 text-right text-blue-600 font-bold">
                      Max Team Members
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="maxTeamMembers"
                        name="maxTeamMembers"
                        type="number"
                        value={editingTier?.maxTeamMembers === null ? '' : editingTier?.maxTeamMembers}
                        onChange={handleInputChange}
                        placeholder="Leave empty for unlimited"
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Second column */}
                <div className="space-y-4">
                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="maxEmailCampaigns" className="col-span-2 text-right text-blue-600 font-bold">
                      Max Email Campaigns
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="maxEmailCampaigns"
                        name="maxEmailCampaigns"
                        type="number"
                        value={editingTier?.maxEmailCampaigns === null ? '' : editingTier?.maxEmailCampaigns}
                        onChange={handleInputChange}
                        placeholder="Leave empty for unlimited"
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="maxAnalyticsReports" className="col-span-2 text-right text-blue-600 font-bold">
                      Max Analytics Reports
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="maxAnalyticsReports"
                        name="maxAnalyticsReports"
                        type="number"
                        value={editingTier?.maxAnalyticsReports === null ? '' : editingTier?.maxAnalyticsReports}
                        onChange={handleInputChange}
                        placeholder="Leave empty for unlimited"
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>
                </div>

                {/* Third column */}
                <div className="space-y-4">
                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="maxVendorManagement" className="col-span-2 text-right text-blue-600 font-bold">
                      Max Vendor Management
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="maxVendorManagement"
                        name="maxVendorManagement"
                        type="number"
                        value={editingTier?.maxVendorManagement === null ? '' : editingTier?.maxVendorManagement}
                        onChange={handleInputChange}
                        placeholder="Leave empty for unlimited"
                        onFocus={(e) => e.target.select()}
                        className="border-2 border-orange-500 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-5 items-center gap-2">
                    <Label htmlFor="isActive" className="col-span-2 text-right text-blue-600 font-bold">
                      Active
                    </Label>
                    <div className="col-span-3 flex items-center space-x-2">
                      <Switch
                        id="isActive"
                        checked={editingTier?.isActive || false}
                        onCheckedChange={handleSwitchChange}
                        className="data-[state=checked]:bg-orange-500 data-[state=checked]:border-orange-600"
                      />
                      <Label htmlFor="isActive" className={editingTier?.isActive ? "text-orange-500 font-bold" : "text-gray-500"}>
                        {editingTier?.isActive ? 'Active' : 'Inactive'}
                      </Label>
                    </div>
                  </div>
                </div>
              </div>

              <Separator className="bg-orange-500 h-1 mt-4" />
            </div>
            <DialogFooter className="bg-orange-500 p-6 rounded-b-md">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
                className="border-2 border-blue-600 bg-white text-blue-600 hover:bg-blue-100 font-bold"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-orange-500 hover:bg-orange-600 text-white font-bold border-2 border-orange-600"
              >
                Save Changes
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px] border-4 border-orange-500">
          <DialogHeader className="bg-red-600 p-4 rounded-t-md">
            <DialogTitle className="text-white font-bold text-xl">Confirm Deletion</DialogTitle>
            <DialogDescription className="text-white">
              Are you sure you want to delete this subscription tier? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Alert variant="destructive" className="border-2 border-red-500 bg-red-50">
              <AlertCircle className="h-5 w-5 text-red-600" />
              <AlertTitle className="text-red-700 font-bold">Warning</AlertTitle>
              <AlertDescription className="text-red-600 font-medium">
                Deleting this tier may affect users currently subscribed to it.
              </AlertDescription>
            </Alert>
          </div>
          <DialogFooter className="bg-red-500 p-6 rounded-b-md">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              className="border-2 border-blue-600 bg-white text-blue-600 hover:bg-blue-100 font-bold"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              className="bg-red-500 hover:bg-red-600 text-white font-bold border-2 border-red-600"
            >
              Delete
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
