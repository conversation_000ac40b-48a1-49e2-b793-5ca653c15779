'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { RoleGate } from '@/components/auth/role-gate';
import { 
  Users, 
  Building, 
  Clock, 
  ChevronLeft,
  Mail,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialog<PERSON>ontent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON>ialog<PERSON><PERSON><PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

// Invitation interface
interface Invitation {
  id: string;
  email: string;
  role: string;
  status: string;
  expiresAt: string;
  createdAt: string;
  updatedAt?: string;
  team: {
    id: string;
    name: string;
    description?: string;
    owner: {
      id: string;
      name: string;
      email: string;
      image: string;
    };
    _count: {
      members: number;
      events: number;
    };
  };
  invitedBy: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

export default function TeamInvitationsPage() {
  const router = useRouter();
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('received');
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch invitations
  useEffect(() => {
    const fetchInvitations = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/teams/invitations');
        
        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }
        
        const data = await response.json();
        setInvitations(data.invitations || []);
      } catch (error) {
        console.error('Error fetching invitations:', error);
        toast({
          title: 'Error',
          description: 'Failed to load invitations. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchInvitations();
  }, []);

  // Handle cancel invitation
  const handleCancelInvitation = async () => {
    if (!invitationToCancel) return;
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/teams/invitations/${invitationToCancel.id}/cancel`, {
        method: 'POST',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      toast({
        title: 'Success',
        description: 'Invitation cancelled successfully',
      });
      
      // Update the invitations list
      setInvitations(invitations.filter(inv => inv.id !== invitationToCancel.id));
      
      // Reset state and close dialog
      setInvitationToCancel(null);
      setIsCancelDialogOpen(false);
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return 'Admin';
      case 'ORGANIZER_MANAGER':
        return 'Manager';
      case 'ORGANIZER_EDITOR':
        return 'Editor';
      case 'ORGANIZER_ANALYST':
        return 'Analyst';
      case 'ORGANIZER_SUPPORT':
        return 'Support';
      default:
        return role;
    }
  };

  // Get role badge color
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Admin</Badge>;
      case 'ORGANIZER_MANAGER':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Manager</Badge>;
      case 'ORGANIZER_EDITOR':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Editor</Badge>;
      case 'ORGANIZER_ANALYST':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Analyst</Badge>;
      case 'ORGANIZER_SUPPORT':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Support</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'PENDING':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
      case 'ACCEPTED':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Accepted</Badge>;
      case 'DECLINED':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Declined</Badge>;
      case 'EXPIRED':
        return <Badge className="bg-gray-100 text-gray-800 border-gray-200">Expired</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Filter invitations by status
  const pendingInvitations = invitations.filter(inv => inv.status === 'PENDING');
  const pastInvitations = invitations.filter(inv => inv.status !== 'PENDING');

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <RoleGate allowedRole={['ORGANIZER', 'ADMIN', 'SUPERADMIN']}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link href="/dashboard/organizer/team" className="text-sm text-gray-500 hover:text-gray-700 flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Teams
          </Link>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Team Invitations</h1>
            <p className="text-gray-500 mt-1">
              Manage your team invitations
            </p>
          </div>
        </div>

        <Tabs defaultValue="pending" className="mt-6">
          <TabsList className="mb-6">
            <TabsTrigger value="pending">Pending Invitations</TabsTrigger>
            <TabsTrigger value="past">Past Invitations</TabsTrigger>
          </TabsList>
          
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>Pending Invitations</CardTitle>
                <CardDescription>
                  Invitations waiting for a response
                </CardDescription>
              </CardHeader>
              <CardContent>
                {pendingInvitations.length === 0 ? (
                  <div className="text-center py-6">
                    <Mail className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Pending Invitations</h3>
                    <p className="text-gray-500 mb-6 max-w-md mx-auto">
                      You don't have any pending invitations at the moment.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pendingInvitations.map((invitation) => (
                      <div key={invitation.id} className="border rounded-lg p-4">
                        <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                          <div className="flex items-center mb-3 md:mb-0">
                            <Avatar className="h-10 w-10 mr-3">
                              <AvatarImage src={invitation.invitedBy.image || ''} alt={invitation.invitedBy.name || 'Inviter'} />
                              <AvatarFallback>{getUserInitials(invitation.invitedBy.name || '')}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{invitation.invitedBy.name || 'Unnamed User'}</div>
                              <div className="text-sm text-gray-500">{invitation.invitedBy.email}</div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getRoleBadge(invitation.role)}
                            {getStatusBadge(invitation.status)}
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 rounded-lg p-3 mb-4">
                          <div className="flex items-center mb-2">
                            <Building className="h-5 w-5 mr-2 text-gray-400" />
                            <div className="font-medium">{invitation.team.name}</div>
                          </div>
                          {invitation.team.description && (
                            <p className="text-sm text-gray-600 ml-7 mb-2">
                              {invitation.team.description}
                            </p>
                          )}
                          <div className="flex items-center text-sm text-gray-500 ml-7">
                            <Users className="h-4 w-4 mr-2" />
                            <span>{invitation.team._count.members} members</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-500">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>Expires on {format(new Date(invitation.expiresAt), 'PPP')}</span>
                          </div>
                          
                          <div className="flex space-x-2">
                            <Button 
                              variant="outline" 
                              size="sm"
                              onClick={() => {
                                setInvitationToCancel(invitation);
                                setIsCancelDialogOpen(true);
                              }}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <XCircle className="h-4 w-4 mr-2" />
                              Decline
                            </Button>
                            <Button 
                              size="sm"
                              asChild
                            >
                              <Link href={`/invitations/${invitation.id}`}>
                                <CheckCircle className="h-4 w-4 mr-2" />
                                View
                              </Link>
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="past">
            <Card>
              <CardHeader>
                <CardTitle>Past Invitations</CardTitle>
                <CardDescription>
                  Invitations that have been accepted, declined, or expired
                </CardDescription>
              </CardHeader>
              <CardContent>
                {pastInvitations.length === 0 ? (
                  <div className="text-center py-6">
                    <Mail className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Past Invitations</h3>
                    <p className="text-gray-500 mb-6 max-w-md mx-auto">
                      You don't have any past invitations.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {pastInvitations.map((invitation) => (
                      <div key={invitation.id} className="border rounded-lg p-4">
                        <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                          <div className="flex items-center mb-3 md:mb-0">
                            <Avatar className="h-10 w-10 mr-3">
                              <AvatarImage src={invitation.invitedBy.image || ''} alt={invitation.invitedBy.name || 'Inviter'} />
                              <AvatarFallback>{getUserInitials(invitation.invitedBy.name || '')}</AvatarFallback>
                            </Avatar>
                            <div>
                              <div className="font-medium">{invitation.invitedBy.name || 'Unnamed User'}</div>
                              <div className="text-sm text-gray-500">{invitation.invitedBy.email}</div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            {getRoleBadge(invitation.role)}
                            {getStatusBadge(invitation.status)}
                          </div>
                        </div>
                        
                        <div className="bg-gray-50 rounded-lg p-3 mb-4">
                          <div className="flex items-center mb-2">
                            <Building className="h-5 w-5 mr-2 text-gray-400" />
                            <div className="font-medium">{invitation.team.name}</div>
                          </div>
                          {invitation.team.description && (
                            <p className="text-sm text-gray-600 ml-7 mb-2">
                              {invitation.team.description}
                            </p>
                          )}
                          <div className="flex items-center text-sm text-gray-500 ml-7">
                            <Users className="h-4 w-4 mr-2" />
                            <span>{invitation.team._count.members} members</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-sm text-gray-500">
                            <Clock className="h-4 w-4 mr-2" />
                            <span>
                              {invitation.status === 'ACCEPTED' ? 'Accepted on ' : 
                               invitation.status === 'DECLINED' ? 'Declined on ' : 
                               'Expired on '}
                              {format(new Date(invitation.updatedAt || invitation.expiresAt), 'PPP')}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Cancel Invitation Confirmation Dialog */}
        <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Decline Invitation</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to decline the invitation to join the team "{invitationToCancel?.team.name}"?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={(e) => {
                  e.preventDefault();
                  handleCancelInvitation();
                }}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'Declining...' : 'Decline'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </RoleGate>
  );
}
