import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/revenue-breakdown
 * Get revenue breakdown by month and source
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date(new Date().setFullYear(new Date().getFullYear() - 1));
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();
    
    // Get all months between start and end date
    const months = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      months.push(new Date(currentDate));
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Get revenue data for each month
    const revenueData = await Promise.all(months.map(async (month) => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);

      // Get ticket sales for the month
      const ticketSales = await db.order.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalPrice: true
        }
      });

      // Get vendor sales for the month
      const vendorSales = await db.vendorOrder.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalAmount: true
        }
      });

      // Get NFC transactions for the month
      const nfcTransactions = await db.vendorNFCTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          amount: true
        }
      });

      return {
        name: month.toLocaleString('default', { month: 'short' }) + ' ' + month.getFullYear(),
        ticketSales: ticketSales._sum.totalPrice || 0,
        vendorSales: vendorSales._sum.totalAmount || 0,
        nfcTransactions: nfcTransactions._sum.amount || 0
      };
    }));

    return NextResponse.json(revenueData);
  } catch (error) {
    console.error('Error fetching revenue breakdown data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch revenue breakdown data' },
      { status: 500 }
    );
  }
}
