# NFC Vendor Payment System

## Overview

The NFC Vendor Payment System is a comprehensive solution for event vendors to process contactless payments using NFC technology. This system allows vendors to accept payments from attendees with NFC-enabled cards, track transactions, and analyze sales performance through an intuitive dashboard.

## Features

- **NFC Terminal**: Process payments by scanning NFC cards
- **Transaction History**: View and manage all payment transactions
- **Analytics Dashboard**: Visualize sales data and track performance metrics
- **Card Management**: Register and manage NFC cards for customers
- **Offline Support**: Process transactions without internet connectivity
- **Receipt Generation**: Create and share digital receipts

## System Architecture

### Components

1. **NFCCardReader Component**
   - Handles the detection and reading of NFC cards
   - Provides both automatic scanning and manual entry options
   - Located at: `src/components/vendor/nfc/NFCCardReader.tsx`

2. **NFC Terminal Page**
   - Main interface for vendors to process payments
   - Integrates product selection, custom amounts, and transaction processing
   - Located at: `src/app/(protected)/dashboard/vendor/nfc-terminal/[eventId]/page.tsx`

3. **Transaction History Component**
   - Displays recent transactions for a vendor at a specific event
   - Shows transaction details including products, amounts, and customer information
   - Located at: `src/components/vendor/nfc/TransactionHistory.tsx`

4. **Analytics Dashboard**
   - Visualizes transaction data with charts and metrics
   - Provides filtering by date range and event
   - Shows key performance indicators like total revenue and average transaction value
   - Located at: `src/app/(protected)/dashboard/vendor/nfc/analytics/page.tsx`

5. **NFC Utility Functions**
   - Helper functions for NFC card validation and formatting
   - Web NFC API integration for hardware support
   - Located at: `src/lib/web-nfc.ts` and `src/lib/nfc-utils.ts`

6. **API Routes**
   - `/api/vendors/nfc/transaction` - Processes NFC payment transactions
   - `/api/vendors/nfc/transactions/history` - Retrieves transaction history
   - `/api/vendors/nfc/analytics` - Provides analytics data for the dashboard

### Database Schema

The system relies on the following database tables:

- `VendorProfile` - Stores vendor information
- `NFCCard` - Stores NFC card information linked to users
- `VendorNFCTransaction` - Records all NFC payment transactions
- `EventVendor` - Links vendors to events they're participating in

## How to Use

### For Vendors

1. **Access the NFC Terminal**
   - Navigate to the Vendor Dashboard
   - Select an event you're approved for
   - Click on "NFC Terminal" to open the payment interface

2. **Process a Payment**
   - Scan the customer's NFC card using the card reader
   - Add products to the cart or enter a custom amount
   - Click "Process Payment" to complete the transaction

3. **View Transaction History**
   - Navigate to the Vendor Dashboard
   - Click on "Transaction History" in the NFC Payment section
   - View all transactions with filtering options

4. **Access Analytics Dashboard**
   - Navigate to the Vendor Dashboard
   - Click on "Analytics Dashboard" in the NFC Payment section
   - View transaction metrics, charts, and performance data
   - Use filters to analyze data by date range or specific event

### Analytics Dashboard Features

The Analytics Dashboard provides comprehensive insights into your NFC payment transactions:

- **Key Metrics**: View total transactions, revenue, average transaction value, and completion rate
- **Time-based Analysis**: Track transaction volume and revenue over time with interactive charts
- **Status Distribution**: Visualize the distribution of transaction statuses (completed, pending, failed, etc.)
- **Top Events**: Identify your best-performing events by transaction volume and revenue
- **Filtering Options**: Analyze data by different time periods (today, this week, this month, all time)
- **Event-specific Analysis**: Filter data by specific events to compare performance
- **Data Export**: Export analytics data for further analysis or reporting

## Technical Implementation

### Web NFC API Integration

The system integrates with the Web NFC API for hardware support on compatible devices. Key features include:

- Detection of NFC hardware availability
- Reading NFC card data with proper error handling
- Fallback to simulation mode when hardware is not available
- Support for offline transactions with synchronization

### Responsive Design

All interfaces are built with responsive design principles to work across desktop and mobile devices:

- Terminal interface optimized for tablet use at vendor booths
- Analytics dashboard with responsive charts that adapt to screen size
- Mobile-friendly transaction history for on-the-go management

## Limitations and Future Improvements

- Currently uses simulated NFC reading on unsupported browsers; fully functional with Web NFC API on supported devices
- Add support for offline transactions with synchronization
- Implement transaction receipt generation and sharing
- Add support for refunds and transaction adjustments
- Enhance analytics with more advanced reporting features

## Troubleshooting

### Common Issues

1. **NFC Card Not Detected**
   - Ensure the device supports Web NFC API (Chrome for Android 89+)
   - Check that the NFC hardware is enabled on the device
   - Position the card closer to the NFC reader area

2. **Analytics Data Not Loading**
   - Verify internet connectivity
   - Try refreshing the page
   - Clear browser cache if issues persist

3. **Transaction Processing Errors**
   - Check that the card has sufficient balance
   - Ensure the vendor account is properly verified
   - Verify that the event is active and the vendor is approved

## Support

For technical support or feature requests, please contact the system administrator or submit an issue through the support portal.