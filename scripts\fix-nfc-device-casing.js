#!/usr/bin/env node

/**
 * Fix all instances of db.nFCDevice to db.nfcDevice in the codebase
 * This script fixes the TypeScript error where nFCDevice should be nfcDevice
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing all nFCDevice casing issues in the codebase...\n');

// Find all TypeScript files in src directory
function findAllTsFiles(dir) {
  const files = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (!item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        }
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

// Check if file contains incorrect NFC casing references
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('nFCDevice') ||
         content.includes('nfcDeviceScan') ||
         content.includes('nfcCard') ||
         content.includes('nfcTerminalSettings') ||
         content.includes('nfcProductPricing');
}

// Update file to fix nFCDevice casing
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Replace all instances of incorrect NFC casing with correct casing
  const patterns = [
    // NFCDevice should be nfcDevice
    /db\.nFCDevice/g,
    /prisma\.nFCDevice/g,
    /\.nFCDevice\./g,
    /\.nFCDevice\s/g,
    /\.nFCDevice$/g,
    // NFCDeviceScan should be nFCDeviceScan (keep original casing)
    /db\.nfcDeviceScan/g,
    /prisma\.nfcDeviceScan/g,
    // NFCCard should be nFCCard (keep original casing)
    /db\.nfcCard/g,
    /prisma\.nfcCard/g,
    // NFCTerminalSettings should be nFCTerminalSettings (keep original casing)
    /db\.nfcTerminalSettings/g,
    /prisma\.nfcTerminalSettings/g,
    // NFCProductPricing should be nFCProductPricing (keep original casing)
    /db\.nfcProductPricing/g,
    /prisma\.nfcProductPricing/g
  ];

  const replacements = [
    // NFCDevice -> nfcDevice
    'db.nfcDevice',
    'prisma.nfcDevice',
    '.nfcDevice.',
    '.nfcDevice ',
    '.nfcDevice',
    // NFCDeviceScan -> nFCDeviceScan
    'db.nFCDeviceScan',
    'prisma.nFCDeviceScan',
    // NFCCard -> nFCCard
    'db.nFCCard',
    'prisma.nFCCard',
    // NFCTerminalSettings -> nFCTerminalSettings
    'db.nFCTerminalSettings',
    'prisma.nFCTerminalSettings',
    // NFCProductPricing -> nFCProductPricing
    'db.nFCProductPricing',
    'prisma.nFCProductPricing'
  ];
  
  for (let i = 0; i < patterns.length; i++) {
    const originalContent = content;
    content = content.replace(patterns[i], replacements[i]);
    if (content !== originalContent) {
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

// Main execution
try {
  const srcDir = path.join(__dirname, '..', 'src');
  console.log(`Searching for TypeScript files in ${srcDir}...`);
  
  const allFiles = findAllTsFiles(srcDir);
  console.log(`Found ${allFiles.length} TypeScript files\n`);
  
  let updatedCount = 0;
  
  for (const filePath of allFiles) {
    const relativePath = path.relative(process.cwd(), filePath);
    
    if (needsUpdate(filePath)) {
      if (updateFile(filePath)) {
        updatedCount++;
      }
    }
  }
  
  console.log(`\n🎉 Fixed ${updatedCount} files with nFCDevice casing issues!`);
  
  if (updatedCount === 0) {
    console.log('✨ No files needed updating - all nFCDevice references are already correctly cased!');
  }
  
} catch (error) {
  console.error('❌ Error fixing nFCDevice casing:', error);
  process.exit(1);
}
