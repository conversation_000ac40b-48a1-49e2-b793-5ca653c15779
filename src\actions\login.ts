'use server'

import { signIn } from "@/auth"
import { getUserByEmail } from "@/data/user"
import { DEFAULT_LOGIN_REDIRECT } from "@/routes"
import { LoginSchema, loginSchema } from "@/schemas"
import {
  sendVerificationEmail,
  sendTwoFactorTokenEmail,
} from "@/lib/mail"
import {
  generateVerificationToken,
  generateTwoFactorToken,
} from "@/data/tokens"
import { getTwoFactorTokenByEmail } from "@/data/two-factor-token"
import { db } from "@/lib/prisma"
import { getTwoFactorConfirmationByUserId } from "@/data/two-factor-confirmation"
import {
  isLoginBlocked,
  recordFailedLoginAttempt,
  recordSuccessfulLoginAttempt
} from "@/lib/rate-limiter-auth"
import { headers } from "next/headers"
import { logLoginAttempt } from "@/lib/activity-logger"

export async function login(
  values: LoginSchema,
  callbackUrl?: string | null
) {
  // Get IP address from headers
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || 'unknown-ip';
  const userAgent = headersList.get('user-agent') || 'unknown-browser';

  const validatedFields = loginSchema.safeParse(values)

  if (!validatedFields.success) {
    return { error: 'Invalid fields!' }
  }

  const { email, password, code } = validatedFields.data

  // Check if login is blocked for this IP or email
  const blockStatus = await isLoginBlocked(email, ipAddress);
  if (blockStatus.isBlocked) {
    const minutes = Math.ceil((blockStatus.timeLeft || 0) / 60);
    return {
      error: `Too many failed login attempts. Please try again in ${minutes} minute${minutes > 1 ? 's' : ''}.`
    };
  }

  const existingUser = await getUserByEmail(email)

  // Check if email really exists
  if (!existingUser || !existingUser.email) {
    // Record failed login attempt for non-existent email
    await recordFailedLoginAttempt(email, ipAddress, userAgent);
    await logLoginAttempt(email, false, undefined, 'Email does not exist', ipAddress, userAgent);
    return { error: 'This email is not registered. Please check your email or create a new account.' }
  }

  // Check if user verified email
  if (!existingUser.emailVerified) {
    const verificationToken = await generateVerificationToken(
      existingUser.email
    )

    await sendVerificationEmail(verificationToken.email, verificationToken.token)

    return { success: 'Your email is not verified. We\'ve sent a new verification email to your inbox. Please check your email and click the verification link to continue.'}
  }

  // 2FA check
  if (existingUser.isTwoFactorEnabled) {
    if (code) {
      const twoFactorToken = await getTwoFactorTokenByEmail(existingUser.email)

      if (!twoFactorToken) {
        return { error: 'Invalid code!' }
      }

      if (twoFactorToken.token !== code) {
        return { error: 'Invalid code!' }
      }

      const hasExpired = new Date(twoFactorToken.expires) < new Date()

      debugger

      if (hasExpired) {
        return { error: 'Code expired!' }
      }

      await db.twoFactorToken.delete({
        where: {id: twoFactorToken.id }
      })

      const existingConfirmation = await getTwoFactorConfirmationByUserId(
        existingUser.id
      )

      if (existingConfirmation) {
        await db.twoFactorConfirmation.delete({
          where: { id: existingConfirmation.id }
        })
      }

      await db.twoFactorConfirmation.create({
        data: {
          userId: existingUser.id
        }
      })
    } else {
      const twoFactorToken = await generateTwoFactorToken(existingUser.email)
      await sendTwoFactorTokenEmail(
        twoFactorToken.email,
        twoFactorToken.token,
      )

      return { twoFactor: true }
    }
  }

  console.log(callbackUrl)

  try {
    console.log('Attempting to sign in with:', { email, redirectTo: callbackUrl || DEFAULT_LOGIN_REDIRECT });

    // Use the server-side signIn function from @/auth
    const result = await signIn("credentials", {
      email,
      password,
      redirectTo: callbackUrl || DEFAULT_LOGIN_REDIRECT
    });

    console.log('Auth result:', result);

    // If authentication is successful, we'll return success
    if (result?.ok) {
      // Record successful login attempt
      await recordSuccessfulLoginAttempt(email, ipAddress, userAgent);
      await logLoginAttempt(email, true, existingUser.id, undefined, ipAddress, userAgent);
      return { success: 'Logged in successfully!' };
    }

    // Check for specific error types
    if (result?.error === "CredentialsSignin") {
      // Record failed login attempt
      await recordFailedLoginAttempt(email, ipAddress, userAgent);
      await logLoginAttempt(email, false, existingUser.id, 'Invalid credentials', ipAddress, userAgent);
      return { error: 'Invalid email or password. Please check your credentials and try again.' };
    }

    // If we get here, authentication failed
    await recordFailedLoginAttempt(email, ipAddress, userAgent);
    await logLoginAttempt(email, false, existingUser.id, result?.error || 'Authentication failed', ipAddress, userAgent);
    return { error: result?.error || 'Authentication failed. Please check your credentials.' };
  } catch (error: any) {
    // Detailed error logging
    console.error('Login error details:', {
      message: error.message,
      name: error.name,
      stack: error.stack,
      type: error.type,
      cause: error.cause,
      ...error
    });

    // For any other errors
    return { error: `Login failed: ${error.message || 'Unknown error'}` };
  }
}