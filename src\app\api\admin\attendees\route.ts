import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const eventId = searchParams.get('eventId') || '';
    const status = searchParams.get('status') || '';
    const checkInStatus = searchParams.get('checkInStatus') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Build the where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { customerName: { contains: search, mode: 'insensitive' } },
        { customerEmail: { contains: search, mode: 'insensitive' } },
        { customerPhone: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (eventId) {
      where.eventId = eventId;
    }

    if (status) {
      where.status = status;
    }

    if (checkInStatus) {
      where.checkInStatus = checkInStatus;
    }

    // Find all attendees (orders) with pagination
    const attendees = await db.order.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            location: true,
            venue: true,
            status: true
          }
        },
        tickets: {
          select: {
            id: true,
            type: true,
            quantity: true,
            price: true,
            isUsed: true,
            scannedAt: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.order.count({ where });

    // Format attendees data
    const formattedAttendees = attendees.map(order => {
      // Calculate total tickets
      const totalTickets = order.tickets.reduce((sum, ticket) => sum + ticket.quantity, 0);

      // Calculate used tickets
      const usedTickets = order.tickets.filter(ticket => ticket.isUsed).reduce((sum, ticket) => sum + ticket.quantity, 0);

      return {
        id: order.id,
        customerName: order.customerName,
        customerEmail: order.customerEmail,
        customerPhone: order.customerPhone,
        status: order.status,
        totalPrice: order.totalPrice,
        createdAt: order.createdAt.toISOString(),
        checkInStatus: order.checkInStatus || 'NOT_CHECKED_IN',
        checkInTime: order.checkInTime ? order.checkInTime.toISOString() : null,
        event: {
          id: order.event.id,
          title: order.event.title,
          startDate: order.event.startDate.toISOString(),
          endDate: order.event.endDate.toISOString(),
          location: order.event.location,
          venue: order.event.venue,
          status: order.event.status
        },
        tickets: order.tickets.map(ticket => ({
          id: ticket.id,
          type: ticket.type,
          quantity: ticket.quantity,
          price: ticket.price,
          isUsed: ticket.isUsed,
          scannedAt: ticket.scannedAt ? ticket.scannedAt.toISOString() : null
        })),
        ticketStats: {
          total: totalTickets,
          used: usedTickets,
          remaining: totalTickets - usedTickets
        }
      };
    });

    // Get order statuses for filters
    const orderStatusesResult = await db.$queryRaw`SELECT DISTINCT status FROM "Order"`;
    const orderStatuses = Array.isArray(orderStatusesResult) ? orderStatusesResult.map(row => row.status) : [];

    // Get check-in statuses for filters
    const checkInStatusesResult = await db.$queryRaw`SELECT DISTINCT "checkInStatus" FROM "Order" WHERE "checkInStatus" IS NOT NULL`;
    const checkInStatuses = Array.isArray(checkInStatusesResult) ? checkInStatusesResult.map(row => row.checkInStatus) : [];

    return NextResponse.json({
      attendees: formattedAttendees,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      },
      filters: {
        orderStatuses,
        checkInStatuses
      }
    });
  } catch (error) {
    console.error('Error fetching attendees:', error);
    return NextResponse.json(
      { error: 'Failed to fetch attendees' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the request body
    const body = await request.json();
    const { attendeeId, checkInStatus, notes } = body;

    // Validate required fields
    if (!attendeeId || !checkInStatus) {
      return NextResponse.json(
        { error: 'Attendee ID and check-in status are required' },
        { status: 400 }
      );
    }

    // Verify check-in status is valid
    if (checkInStatus !== 'CHECKED_IN' && checkInStatus !== 'NOT_CHECKED_IN') {
      return NextResponse.json(
        { error: 'Check-in status must be either "CHECKED_IN" or "NOT_CHECKED_IN"' },
        { status: 400 }
      );
    }

    // Update the attendee's check-in status
    const updatedAttendee = await db.order.update({
      where: { id: attendeeId },
      data: {
        checkInStatus,
        checkInTime: checkInStatus === 'CHECKED_IN' ? new Date() : null,
        notes: notes || undefined
      },
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        }
      }
    });

    // If checking in, update ticket status
    if (checkInStatus === 'CHECKED_IN') {
      await db.ticket.updateMany({
        where: { orderId: attendeeId },
        data: {
          isUsed: true,
          scannedAt: new Date(),
          scannedBy: `Admin: ${user.name || user.email}`
        }
      });
    }

    return NextResponse.json({
      success: true,
      attendee: {
        id: updatedAttendee.id,
        customerName: updatedAttendee.customerName,
        customerEmail: updatedAttendee.customerEmail,
        checkInStatus: updatedAttendee.checkInStatus,
        checkInTime: updatedAttendee.checkInTime ? updatedAttendee.checkInTime.toISOString() : null,
        event: updatedAttendee.event.title
      }
    });
  } catch (error) {
    console.error('Error updating attendee check-in status:', error);
    return NextResponse.json(
      { error: 'Failed to update attendee check-in status' },
      { status: 500 }
    );
  }
}
