import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only organizers can access this endpoint
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    const skip = (page - 1) * limit;

    // Build the base query for financial transactions
    let where: any = {
      userId: user.id!
    };

    // Add type filter if provided
    if (type && type !== 'all') {
      where.type = type;
    }

    // Add status filter if provided
    if (status && status !== 'all') {
      where.status = status;
    }

    // Add search filter if provided
    if (search) {
      where.OR = [
        { description: { contains: search, mode: 'insensitive' } },
        { id: { contains: search, mode: 'insensitive' } }
      ];
    }

    // Get total count for pagination
    const totalCount = await db.financialTransaction.count({ where });

    // Get transactions with pagination
    const transactions = await db.financialTransaction.findMany({
      where,
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit,
    });

    // Get event-related transactions (orders)
    const eventTransactions = await db.order.findMany({
      where: {
        event: {
          userId: user.id!
        },
        status: 'Completed'
      },
      include: {
        event: {
          select: {
            title: true
          }
        },
        user: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    });

    // Format transactions
    const formattedTransactions = [
      ...transactions.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: tx.amount,
        status: tx.status,
        date: tx.createdAt.toISOString(),
        description: tx.description || 'Transaction',
        user: {
          id: user.id,
          name: user.name || 'Unknown',
          email: user.email || '<EMAIL>'
        },
        reference: tx.id
      })),
      ...eventTransactions.map(order => ({
        id: `order-${order.id}`,
        type: 'TICKET_PURCHASE',
        amount: order.totalPrice,
        status: 'COMPLETED',
        date: order.createdAt.toISOString(),
        description: `Ticket purchase for ${order.event.title}`,
        user: {
          id: order.userId,
          name: order.user?.name || 'Unknown',
          email: order.user?.email || '<EMAIL>'
        },
        event: {
          id: order.eventId,
          title: order.event.title
        },
        reference: order.id
      }))
    ].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
     .slice(0, limit);

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        total: totalCount + eventTransactions.length,
        page,
        limit,
        pages: Math.ceil((totalCount + eventTransactions.length) / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching organizer transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch transactions' },
      { status: 500 }
    );
  }
}
