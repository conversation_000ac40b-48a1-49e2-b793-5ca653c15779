import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { PartnerPromotionsClient } from '@/components/partner/PartnerPromotionsClient';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};



export default async function PartnerPromotionsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  // Get partner and promotions from database
  const partner = await db.partner.findUnique({
    where: { userId: session.user.id },
    include: {
      promotions: {
        orderBy: { createdAt: 'desc' }
      }
    }
  });

  if (!partner) {
    redirect('/dashboard');
  }

  const now = new Date();
  const promotions = partner.promotions.map(promo => {
    // Transform null values to undefined for TypeScript compatibility
    const transformedPromo = {
      ...promo,
      imageUrl: promo.imageUrl || undefined,
      eventId: promo.eventId || undefined,
      discountValue: promo.discountValue || undefined,
      maxUses: promo.maxUses || undefined,
      discountType: promo.discountType || undefined,
      promoCode: promo.promoCode || undefined,
    };

    // Add status based on dates and activity with proper typing
    const status: 'active' | 'upcoming' | 'expired' =
      promo.isActive && promo.startDate <= now && promo.endDate >= now ? 'active' :
      promo.startDate > now ? 'upcoming' : 'expired';

    return {
      ...transformedPromo,
      status
    };
  });

  return (
    <PartnerPromotionsClient
      partnerId={partner.id}
      promotions={promotions}
    />
  );
}
