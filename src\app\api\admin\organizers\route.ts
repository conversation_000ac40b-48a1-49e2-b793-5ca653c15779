import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Find all organizers (users with ORGANIZER role)
    console.log('Fetching organizers with search:', search);

    // Now fetch organizers specifically
    const organizers = await db.user.findMany({
      where: {
        role: 'ORGANIZER',
        OR: search ? [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ] : undefined
      },
      select: {
        id: true,
        name: true,
        email: true,
        emailVerified: true,
        createdAt: true,
        accountBalance: true,
        _count: {
          select: {
            events: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.user.count({
      where: {
        role: 'ORGANIZER',
        OR: search ? [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } }
        ] : undefined
      }
    });

    console.log(`Found ${organizers.length} organizers out of ${totalCount} total`);

    // For each organizer, get their events, total sales, and tickets sold
    const organizersWithDetails = await Promise.all(
      organizers.map(async (organizer) => {
        // Get events for this organizer
        const events = await db.event.findMany({
          where: { userId: organizer.id },
          select: {
            id: true,
            title: true,
            startDate: true,
            status: true,
            imagePath: true,
            _count: {
              select: {
                orders: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 5 // Limit to 5 most recent events
        });

        // Get financial transactions for this organizer
        const transactions = await db.financialTransaction.findMany({
          where: {
            userId: organizer.id
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10 // Limit to 10 most recent transactions
        });

        // Calculate total sales and tickets sold
        const salesData = await db.order.aggregate({
          where: {
            event: {
              userId: organizer.id
            },
            status: 'Completed'
          },
          _sum: {
            totalPrice: true
          }
        });

        const ticketData = await db.ticket.aggregate({
          where: {
            event: {
              userId: organizer.id
            }
          },
          _sum: {
            quantity: true
          }
        });

        // Get event details with ticket sales
        const eventDetails = await Promise.all(
          events.map(async (event) => {
            const eventSales = await db.order.aggregate({
              where: {
                eventId: event.id,
                status: 'Completed'
              },
              _sum: {
                totalPrice: true
              }
            });

            const eventTickets = await db.ticket.aggregate({
              where: {
                eventId: event.id
              },
              _sum: {
                quantity: true
              }
            });

            return {
              id: event.id,
              title: event.title,
              date: event.startDate.toISOString(),
              status: event.status,
              imagePath: event.imagePath,
              ticketsSold: eventTickets._sum.quantity || 0,
              revenue: eventSales._sum.totalPrice || 0
            };
          })
        );

        return {
          id: organizer.id,
          name: organizer.name || 'Unknown',
          email: organizer.email,
          isVerified: organizer.emailVerified !== null,
          accountBalance: organizer.accountBalance || 0,
          totalEvents: organizer._count.events,
          totalSales: salesData._sum.totalPrice || 0,
          totalTicketsSold: ticketData._sum.quantity || 0,
          joinedDate: organizer.createdAt.toISOString(),
          events: eventDetails,
          recentTransactions: transactions.map(tx => ({
            id: tx.id,
            amount: tx.amount,
            type: tx.type,
            description: tx.description,
            date: tx.createdAt.toISOString()
          }))
        };
      })
    );

    return NextResponse.json({
      organizers: organizersWithDetails,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching organizers:', error);
    return NextResponse.json(
      { error: 'Failed to fetch organizers' },
      { status: 500 }
    );
  }
}
