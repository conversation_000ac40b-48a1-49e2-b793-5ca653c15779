import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import WalletPageClient from '@/components/wallet/wallet-page-client';
import { UserSession } from '@/types/session';

export default async function WalletPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only organizers can access this page
  if (session.user.role !== 'ORGANIZER') {
    redirect('/dashboard');
  }

  // Get user with account balance
  const userData = await db.user.findUnique({
    where: { id: session.user.id },
    select: {
      id: true,
      name: true,
      email: true,
      accountBalance: true,
    },
  });

  if (!userData) {
    redirect('/dashboard');
  }

  // Ensure name and email are strings (not null)
  const user = {
    id: userData.id,
    name: userData.name || 'User',
    email: userData.email || 'No email',
    accountBalance: userData.accountBalance,
  };

  return (
    <div className="container mx-auto py-8">
      <WalletPageClient user={user} />
    </div>
  );
}
