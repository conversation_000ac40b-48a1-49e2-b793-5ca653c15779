import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { ABTestStatus, ABTestEventType } from '@prisma/client';

/**
 * GET /api/admin/featuring/ab-testing
 * Get A/B testing data for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';

    // Build where clause based on status filter
    const where: any = {};
    
    if (status === 'active') {
      where.status = 'ACTIVE';
    } else if (status === 'paused') {
      where.status = 'PAUSED';
    } else if (status === 'completed') {
      where.status = 'COMPLETED';
    }

    // Get all A/B tests
    const experiments = await db.aBTest.findMany({
      where,
      include: {
        variantA: true,
        variantB: true,
      },
      orderBy: [
        { status: 'asc' },
        { startDate: 'desc' },
      ],
    });

    // Get event counts for each experiment
    const experimentsWithEvents = await Promise.all(
      experiments.map(async (experiment) => {
        // Get impression counts
        const [variantAImpressions, variantBImpressions] = await Promise.all([
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantA.id,
              type: 'IMPRESSION',
            },
          }),
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantB.id,
              type: 'IMPRESSION',
            },
          }),
        ]);

        // Get click counts
        const [variantAClicks, variantBClicks] = await Promise.all([
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantA.id,
              type: 'CLICK',
            },
          }),
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantB.id,
              type: 'CLICK',
            },
          }),
        ]);

        // Get conversion counts
        const [variantAConversions, variantBConversions] = await Promise.all([
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantA.id,
              type: 'CONVERSION',
            },
          }),
          db.aBTestEvent.count({
            where: {
              experimentId: experiment.id,
              variant: experiment.variantB.id,
              type: 'CONVERSION',
            },
          }),
        ]);

        // Calculate metrics
        const variantACTR = variantAImpressions > 0 ? (variantAClicks / variantAImpressions) * 100 : 0;
        const variantBCTR = variantBImpressions > 0 ? (variantBClicks / variantBImpressions) * 100 : 0;
        const variantAConversionRate = variantAClicks > 0 ? (variantAConversions / variantAClicks) * 100 : 0;
        const variantBConversionRate = variantBClicks > 0 ? (variantBConversions / variantBClicks) * 100 : 0;

        // Format the experiment data
        return {
          id: experiment.id,
          name: experiment.name,
          description: experiment.description,
          status: experiment.status,
          startDate: experiment.startDate.toISOString(),
          endDate: experiment.endDate ? experiment.endDate.toISOString() : null,
          winner: experiment.winner,
          variants: [
            {
              id: experiment.variantA.id,
              name: experiment.variantA.name,
              description: experiment.variantA.description,
              trafficPercentage: experiment.variantA.trafficPercentage,
              impressions: variantAImpressions,
              clicks: variantAClicks,
              conversions: variantAConversions,
              ctr: variantACTR,
              conversionRate: variantAConversionRate,
            },
            {
              id: experiment.variantB.id,
              name: experiment.variantB.name,
              description: experiment.variantB.description,
              trafficPercentage: experiment.variantB.trafficPercentage,
              impressions: variantBImpressions,
              clicks: variantBClicks,
              conversions: variantBConversions,
              ctr: variantBCTR,
              conversionRate: variantBConversionRate,
            },
          ],
        };
      })
    );

    // Get counts for each status
    const [activeCount, pausedCount, completedCount, totalCount] = await Promise.all([
      db.aBTest.count({ where: { status: 'ACTIVE' } }),
      db.aBTest.count({ where: { status: 'PAUSED' } }),
      db.aBTest.count({ where: { status: 'COMPLETED' } }),
      db.aBTest.count({}),
    ]);

    // Split experiments by status
    const activeExperiments = experimentsWithEvents.filter(exp => exp.status === 'ACTIVE');
    const completedExperiments = experimentsWithEvents.filter(exp => exp.status === 'COMPLETED');

    return NextResponse.json({
      activeExperiments,
      completedExperiments,
      stats: {
        activeCount,
        pausedCount,
        completedCount,
        totalCount,
      },
    });
  } catch (error) {
    console.error('Error fetching A/B testing data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch A/B testing data' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/featuring/ab-testing
 * Create a new A/B test
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      name,
      description,
      variantAName,
      variantADescription,
      variantBName,
      variantBDescription,
      trafficSplit,
    } = body;

    if (!name || !variantAName || !variantBName) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create variant A
    const variantA = await db.aBTestVariant.create({
      data: {
        name: variantAName,
        description: variantADescription,
        trafficPercentage: trafficSplit || 50,
      },
    });

    // Create variant B
    const variantB = await db.aBTestVariant.create({
      data: {
        name: variantBName,
        description: variantBDescription,
        trafficPercentage: 100 - (trafficSplit || 50),
      },
    });

    // Create the experiment
    const experiment = await db.aBTest.create({
      data: {
        name,
        description,
        status: 'ACTIVE' as ABTestStatus,
        variantAId: variantA.id,
        variantBId: variantB.id,
      },
      include: {
        variantA: true,
        variantB: true,
      },
    });

    return NextResponse.json({
      success: true,
      experiment: {
        id: experiment.id,
        name: experiment.name,
        description: experiment.description,
        status: experiment.status,
        startDate: experiment.startDate.toISOString(),
        endDate: experiment.endDate ? experiment.endDate.toISOString() : null,
        winner: experiment.winner,
        variants: [
          {
            id: experiment.variantA.id,
            name: experiment.variantA.name,
            description: experiment.variantA.description,
            trafficPercentage: experiment.variantA.trafficPercentage,
            impressions: 0,
            clicks: 0,
            conversions: 0,
            ctr: 0,
            conversionRate: 0,
          },
          {
            id: experiment.variantB.id,
            name: experiment.variantB.name,
            description: experiment.variantB.description,
            trafficPercentage: experiment.variantB.trafficPercentage,
            impressions: 0,
            clicks: 0,
            conversions: 0,
            ctr: 0,
            conversionRate: 0,
          },
        ],
      },
    });
  } catch (error) {
    console.error('Error creating A/B test:', error);
    return NextResponse.json(
      { error: 'Failed to create A/B test' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/featuring/ab-testing
 * Update an A/B test (end, pause, or select winner)
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { experimentId, action, winnerId } = body;

    if (!experimentId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get the experiment
    const experiment = await db.aBTest.findUnique({
      where: { id: experimentId },
      include: {
        variantA: true,
        variantB: true,
      },
    });

    if (!experiment) {
      return NextResponse.json(
        { error: 'Experiment not found' },
        { status: 404 }
      );
    }

    // Update the experiment based on the action
    if (action === 'end' && winnerId) {
      // End the experiment with a winner
      await db.aBTest.update({
        where: { id: experimentId },
        data: {
          status: 'COMPLETED' as ABTestStatus,
          endDate: new Date(),
          winner: winnerId,
        },
      });
    } else if (action === 'pause') {
      // Pause the experiment
      await db.aBTest.update({
        where: { id: experimentId },
        data: {
          status: 'PAUSED' as ABTestStatus,
        },
      });
    } else if (action === 'resume') {
      // Resume the experiment
      await db.aBTest.update({
        where: { id: experimentId },
        data: {
          status: 'ACTIVE' as ABTestStatus,
        },
      });
    } else if (action === 'end-no-winner') {
      // End the experiment without a winner
      await db.aBTest.update({
        where: { id: experimentId },
        data: {
          status: 'COMPLETED' as ABTestStatus,
          endDate: new Date(),
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Experiment ${action === 'end' ? 'ended with winner' : action === 'pause' ? 'paused' : action === 'resume' ? 'resumed' : 'ended without winner'} successfully`,
    });
  } catch (error) {
    console.error('Error updating A/B test:', error);
    return NextResponse.json(
      { error: 'Failed to update A/B test' },
      { status: 500 }
    );
  }
}
