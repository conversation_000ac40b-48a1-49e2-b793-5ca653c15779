import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

// POST /api/admin/teams/invitations/[id]/cancel - Cancel an invitation (admin only)
export async function POST(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
    const resolvedParams = await context.params;

  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await context.params;
    const token = resolvedParams.id;

    // Find the invitation
    const invitation = await db.teamInvitation.findUnique({
      where: { token },
      include: {
        team: true,
      },
    });

    // Check if invitation exists
    if (!invitation) {
      return NextResponse.json(
        { error: 'Invitation not found' },
        { status: 404 }
      );
    }

    // Check if invitation has already been accepted or declined
    if (invitation.status !== 'PENDING') {
      return NextResponse.json(
        { error: `Invitation has already been ${invitation.status.toLowerCase()}` },
        { status: 400 }
      );
    }

    // Update the invitation status
    await db.teamInvitation.update({
      where: { id: invitation.id },
      data: {
        status: 'DECLINED',
      },
    });

    // Create notification for the invited user if they exist in the system
    if (invitation.email) {
      const invitedUser = await db.user.findUnique({
        where: { email: invitation.email },
      });

      if (invitedUser) {
        await db.notification.create({
          data: {
            userId: invitedUser.id,
            message: `Your invitation to join the team "${invitation.team.name}" has been cancelled by an administrator`,
            type: 'TEAM_INVITATION_CANCELLED',
            isRead: false,
          },
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Invitation cancelled successfully',
    });
  } catch (error) {
    console.error('Error cancelling invitation:', error);
    return NextResponse.json(
      { error: 'Failed to cancel invitation' },
      { status: 500 }
    );
  }
}
