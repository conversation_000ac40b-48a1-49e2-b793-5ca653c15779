import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { getSession } from '@/auth';
import { UserSession } from '@/types/session';

// GET: Fetch a specific payout
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const session = await getSession() as UserSession;
    const id = resolvedParams.id;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the payout
    const payout = await db.eventPayout.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            accountBalance: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
            endDate: true,
            status: true,
          },
        },
        withdrawals: {
          include: {
            bankAccount: true,
          },
        },
      },
    });

    if (!payout) {
      return NextResponse.json({ error: 'Payout not found' }, { status: 404 });
    }

    // Check if user is authorized to view this payout
    const isAdmin = session.user.role === 'ADMIN' || session.user.role === 'SUPERADMIN';
    const isOwner = payout.userId === session.user.id;

    if (!isAdmin && !isOwner) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    return NextResponse.json(payout);
  } catch (error) {
    console.error('Error fetching payout:', error);
    return NextResponse.json(
      { error: 'Failed to fetch payout' },
      { status: 500 }
    );
  }
}

// PATCH: Update a payout status (admin only)
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getSession() as UserSession;
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can update payout status
    const isAdmin = session.user.role === 'ADMIN' || session.user.role === 'SUPERADMIN';
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get the payout
    const payout = await db.eventPayout.findUnique({
      where: { id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            accountBalance: true,
          },
        },
        event: {
          select: {
            id: true,
            title: true,
          },
        },
      },
    });

    if (!payout) {
      return NextResponse.json({ error: 'Payout not found' }, { status: 404 });
    }

    // Parse request body
    const body = await req.json();
    const { status, notes, reference, transactionId } = body;

    // Validate status
    const validStatuses = ['Pending', 'Processing', 'Completed', 'Failed', 'Cancelled'];
    if (!status || !validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status. Must be one of: ' + validStatuses.join(', ') },
        { status: 400 }
      );
    }

    // Handle status transitions
    if (status === 'Completed' && payout.status !== 'Processing') {
      return NextResponse.json(
        { error: 'Payout must be in Processing status before it can be marked as Completed' },
        { status: 400 }
      );
    }

    // Update the payout
    const updatedPayout = await db.eventPayout.update({
      where: { id },
      data: {
        status,
        adminNotes: notes ? `${payout.adminNotes || ''}\n\n${notes}` : payout.adminNotes,
        reference: reference || payout.reference,
        transactionId: transactionId || payout.transactionId,
        processedDate: status === 'Completed' ? new Date() : payout.processedDate,
      },
    });

    // If status is changed to Processing, add the amount to the user's account balance
    if (status === 'Processing' && payout.status !== 'Processing') {
      await db.user.update({
        where: { id: payout.userId },
        data: {
          accountBalance: {
            increment: payout.amount,
          },
        },
      });

      // Create a notification for the user
      await db.notification.create({
        data: {
          userId: payout.userId,
          type: 'PAYOUT_APPROVED',
          message: `Your payout request for ${payout.event.title} has been approved. The amount of ${payout.amount.toFixed(2)} has been added to your account balance.`,
        },
      });
    }

    // If status is changed to Cancelled or Failed, create a notification
    if ((status === 'Cancelled' || status === 'Failed') && 
        (payout.status !== 'Cancelled' && payout.status !== 'Failed')) {
      await db.notification.create({
        data: {
          userId: payout.userId,
          type: 'PAYOUT_REJECTED',
          message: `Your payout request for ${payout.event.title} has been ${status.toLowerCase()}. Please contact support for more information.`,
        },
      });
    }

    // If status is changed to Completed, create a notification
    if (status === 'Completed' && payout.status !== 'Completed') {
      await db.notification.create({
        data: {
          userId: payout.userId,
          type: 'PAYOUT_COMPLETED',
          message: `Your payout for ${payout.event.title} has been processed successfully.`,
        },
      });
    }

    return NextResponse.json({
      message: `Payout status updated to ${status}`,
      payout: updatedPayout,
    });
  } catch (error) {
    console.error('Error updating payout:', error);
    return NextResponse.json(
      { error: 'Failed to update payout' },
      { status: 500 }
    );
  }
}
