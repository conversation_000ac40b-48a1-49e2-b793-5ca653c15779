'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Mail, Phone, Edit, RefreshCw, UserIcon } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

// Define types for the attendee data
interface Ticket {
  id: string;
  type: string;
  quantity: number;
  price: number;
}

interface EventInfo {
  id: string;
  title: string;
  date: string;
  imagePath?: string;
}

interface Attendee {
  id: string;
  name: string | null;
  email: string | null;
  phone: string | null;
  event: EventInfo;
  tickets: Ticket[];
  totalSpent: number;
  purchaseDate: string;
  status: string;
}

interface AttendeeFormData {
  name: string;
  email: string;
  phone: string;
}

export default function AttendeesPage() {
  const [attendees, setAttendees] = useState<Attendee[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAttendee, setSelectedAttendee] = useState<Attendee | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [formData, setFormData] = useState<AttendeeFormData>({ name: '', email: '', phone: '' });
  const [formErrors, setFormErrors] = useState<Partial<AttendeeFormData>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [missingInfoCount, setMissingInfoCount] = useState(0);

  // Function to format phone number
  const formatPhoneNumber = (phoneNumber: string): string => {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');

    // Check if the input is of correct length
    const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);

    if (match) {
      return `(${match[1]}) ${match[2]}-${match[3]}`;
    }

    // International format if longer
    if (cleaned.length > 10) {
      // Try to format as international number
      const intMatch = cleaned.match(/^(\d{1,3})(\d{3})(\d{3})(\d{4})$/);
      if (intMatch) {
        return `+${intMatch[1]} (${intMatch[2]}) ${intMatch[3]}-${intMatch[4]}`;
      }
    }

    // Return the cleaned number if it doesn't match expected formats
    return cleaned;
  };

  // Function to validate form data
  const validateForm = (data: AttendeeFormData) => {
    const errors: Partial<AttendeeFormData> = {};

    if (!data.name || data.name.trim() === '') {
      errors.name = 'Name is required';
    }

    if (!data.email || data.email.trim() === '') {
      errors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(data.email)) {
      errors.email = 'Email is invalid';
    }

    if (!data.phone || data.phone.trim() === '') {
      errors.phone = 'Phone number is required';
    } else {
      // Basic phone validation - should have at least 10 digits
      const digitsOnly = data.phone.replace(/\D/g, '');
      if (digitsOnly.length < 10) {
        errors.phone = 'Phone number should have at least 10 digits';
      }
    }

    return errors;
  };

  // Function to update attendee information
  const updateAttendeeInfo = async () => {
    // Validate form data
    const errors = validateForm(formData);
    setFormErrors(errors);

    if (Object.keys(errors).length > 0) {
      return; // Don't proceed if there are validation errors
    }

    if (!selectedAttendee) return;

    setIsSaving(true);

    try {
      // Format the phone number before sending to API
      const formattedPhone = formatPhoneNumber(formData.phone);

      const response = await fetch(`/api/dashboard/organizer/attendees/${selectedAttendee.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName: formData.name,
          customerEmail: formData.email,
          customerPhone: formattedPhone,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to update attendee: ${response.status}`);
      }

      // Update the attendee in the local state
      setAttendees(prev =>
        prev.map(a =>
          a.id === selectedAttendee.id
            ? { ...a, name: formData.name, email: formData.email, phone: formattedPhone }
            : a
        )
      );

      // Close the dialog and reset form
      setIsEditDialogOpen(false);
      toast({
        title: 'Success',
        description: 'Attendee information updated successfully',
        variant: 'default',
      });
    } catch (err) {
      console.error('Error updating attendee:', err);
      toast({
        title: 'Error',
        description: err instanceof Error ? err.message : 'Failed to update attendee information',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Function to open the edit dialog
  const openEditDialog = (attendee: Attendee) => {
    setSelectedAttendee(attendee);
    setFormData({
      name: attendee.name || '',
      email: attendee.email || '',
      phone: attendee.phone || '',
    });
    setFormErrors({});
    setIsEditDialogOpen(true);
  };

  // Function to fetch attendees
  const fetchAttendees = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/dashboard/organizer/attendees');

      if (!response.ok) {
        throw new Error(`API returned status: ${response.status}`);
      }

      const data = await response.json();
      console.log('API Response:', data);

      if (data && data.attendees) {
        setAttendees(data.attendees);

        // Count attendees with missing information
        const missing = data.attendees.filter(
          (a: Attendee) => !a.name || !a.email || !a.phone
        ).length;
        setMissingInfoCount(missing);
      } else {
        setAttendees([]);
        setMissingInfoCount(0);
      }
    } catch (err) {
      console.error('Error fetching attendees:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Fetch attendees on component mount
  useEffect(() => {
    fetchAttendees();
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Attendees</h1>
          <p className="text-gray-500">
            View and manage attendees for your events
          </p>
        </div>
        <Button onClick={fetchAttendees} variant="outline" className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4" />
          Refresh
        </Button>
      </div>

      {missingInfoCount > 0 && (
        <Card className="bg-yellow-50 border-yellow-200">
          <CardContent className="p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-600 mr-2 mt-0.5" />
              <div>
                <h3 className="font-medium text-yellow-800">Missing Contact Information</h3>
                <p className="text-sm text-yellow-700">
                  {missingInfoCount} {missingInfoCount === 1 ? 'attendee has' : 'attendees have'} incomplete contact information.
                  Click the edit button to update their details.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Attendees List</CardTitle>
          <CardDescription>
            Manage contact information for your event attendees
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-4">
              <p>Loading attendees...</p>
            </div>
          ) : error ? (
            <div className="p-4 bg-red-50 text-red-800 rounded-md">
              <p className="font-medium">Error loading attendees</p>
              <p className="text-sm mt-1">{error}</p>
            </div>
          ) : attendees.length === 0 ? (
            <div className="p-4 bg-blue-50 text-blue-800 rounded-md">
              <p className="font-medium">No attendees found</p>
              <p className="text-sm mt-1">You have events, but no attendees have registered yet.</p>
            </div>
          ) : (
            <div>
              <p className="mb-4">Found {attendees.length} attendees</p>
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Name</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Email</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Phone</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Event</th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Status</th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {attendees.map((attendee) => (
                      <tr key={attendee.id} className="bg-white border-b">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {attendee.name ? (
                            <div className="flex items-center gap-1">
                              <UserIcon className="h-3 w-3" />
                              {attendee.name}
                            </div>
                          ) : (
                            <span className="text-red-500 italic">Missing</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {attendee.email ? (
                            <a href={`mailto:${attendee.email}`} className="flex items-center gap-1 text-blue-600 hover:underline">
                              <Mail className="h-3 w-3" />
                              {attendee.email}
                            </a>
                          ) : (
                            <span className="text-red-500 italic">Missing</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {attendee.phone ? (
                            <a href={`tel:${attendee.phone}`} className="flex items-center gap-1 hover:underline">
                              <Phone className="h-3 w-3" />
                              {attendee.phone}
                            </a>
                          ) : (
                            <span className="text-red-500 italic">Missing</span>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {attendee.event?.title || 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm">
                          <Badge className={`px-2 py-1 ${attendee.status === 'COMPLETED' || attendee.status === 'Completed'
                            ? 'bg-green-100 text-green-800 border-green-200'
                            : attendee.status === 'PENDING' || attendee.status === 'Pending'
                            ? 'bg-yellow-100 text-yellow-800 border-yellow-200'
                            : 'bg-red-100 text-red-800 border-red-200'}`}>
                            {attendee.status || 'Unknown'}
                          </Badge>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-right">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(attendee)}
                            className="flex items-center gap-1"
                          >
                            <Edit className="h-3 w-3" />
                            Edit
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Attendee Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Attendee Information</DialogTitle>
            <DialogDescription>
              Fill in the missing contact information for this attendee.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="Enter full name"
              />
              {formErrors.name && (
                <p className="text-sm text-red-500">{formErrors.name}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                placeholder="Enter email address"
              />
              {formErrors.email && (
                <p className="text-sm text-red-500">{formErrors.email}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                value={formData.phone}
                onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                placeholder="(*************"
                type="tel"
                pattern="[0-9()-+ ]*"
                inputMode="tel"
              />
              <p className="text-xs text-gray-500">Format: (************* or +1 (*************</p>
              {formErrors.phone && (
                <p className="text-sm text-red-500">{formErrors.phone}</p>
              )}
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={updateAttendeeInfo} disabled={isSaving}>
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
