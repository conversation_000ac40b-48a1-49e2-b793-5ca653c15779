"use client";

import React, { useState, useEffect } from 'react';
import { useNotifications } from '@/hooks/use-notifications';
import { formatDistanceToNow, format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Notification } from '@/contexts/notification-context';
import { Loader2, Trash2, CheckCircle, AlertCircle, LogIn } from 'lucide-react';
import { cn } from '@/lib/utils';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { useCurrentUser } from '@/hooks/use-current-user';
import { useRouter } from 'next/navigation';

export default function NotificationsPage() {
  const router = useRouter();
  const user = useCurrentUser();
  const {
    notifications,
    loading,
    error,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllRead,
  } = useNotifications();
  const [activeTab, setActiveTab] = useState('all');
  const [groupedNotifications, setGroupedNotifications] = useState<Record<string, Notification[]>>({});

  // Redirect to login if user is not authenticated
  useEffect(() => {
    if (!user) {
      router.push('/auth/login?callbackUrl=/notifications');
    }
  }, [user, router]);

  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);

  useEffect(() => {
    // Group notifications by date
    const grouped = notifications.reduce((acc, notification) => {
      const date = new Date(notification.createdAt).toDateString();
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(notification);
      return acc;
    }, {} as Record<string, Notification[]>);

    setGroupedNotifications(grouped);
  }, [notifications]);

  // Filter notifications based on active tab
  const getFilteredNotifications = () => {
    if (activeTab === 'all') {
      return notifications;
    } else if (activeTab === 'unread') {
      return notifications.filter(n => !n.isRead);
    } else if (activeTab === 'read') {
      return notifications.filter(n => n.isRead);
    }
    return notifications;
  };

  // Get notification type badge
  const getNotificationTypeBadge = (type: string) => {
    let variant = 'default';
    let label = type.replace(/_/g, ' ');

    if (type.includes('APPROVED') || type.includes('PURCHASED') || type.includes('RECEIVED')) {
      variant = 'success';
    } else if (type.includes('REJECTED') || type.includes('FAILED') || type.includes('EXPIRED')) {
      variant = 'destructive';
    } else if (type.includes('SUBMITTED') || type.includes('EXPIRING')) {
      variant = 'warning';
    } else if (type.includes('FEATURED') || type.includes('INVITATION')) {
      variant = 'info';
    }

    return (
      <Badge variant={variant as any} className="ml-2">
        {label}
      </Badge>
    );
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
    toast({
      title: "Success",
      description: "All notifications marked as read",
      variant: "success",
    });
  };

  // Handle delete all read
  const handleDeleteAllRead = async () => {
    await deleteAllRead();
    toast({
      title: "Success",
      description: "All read notifications deleted",
      variant: "success",
    });
  };

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    if (type.includes('APPROVED') || type.includes('PURCHASED') || type.includes('RECEIVED')) {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (type.includes('REJECTED') || type.includes('FAILED') || type.includes('EXPIRED')) {
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    } else {
      return <div className="h-5 w-5 rounded-full bg-blue-500 flex items-center justify-center text-white text-xs">i</div>;
    }
  };

  // Show loading state if user is not yet loaded
  if (!user) {
    return (
      <div className="container mx-auto py-6 max-w-4xl flex justify-center items-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Notifications</h1>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleMarkAllAsRead}
            disabled={loading || notifications.filter(n => !n.isRead).length === 0}
          >
            Mark all as read
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleDeleteAllRead}
            disabled={loading || notifications.filter(n => n.isRead).length === 0}
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete read
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Your Notifications</CardTitle>
          <CardDescription>
            Stay updated with events, tickets, and system notifications.
          </CardDescription>
        </CardHeader>
        <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
          <div className="px-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="all">All</TabsTrigger>
              <TabsTrigger value="unread">
                Unread
                {notifications.filter(n => !n.isRead).length > 0 && (
                  <Badge variant="destructive" className="ml-2">
                    {notifications.filter(n => !n.isRead).length}
                  </Badge>
                )}
              </TabsTrigger>
              <TabsTrigger value="read">Read</TabsTrigger>
            </TabsList>
          </div>

          <CardContent className="pt-4">
            {loading && notifications.length === 0 ? (
              <div className="flex justify-center items-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <div className="text-center py-8 text-red-500">
                <AlertCircle className="h-8 w-8 mx-auto mb-2" />
                <p>{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={fetchNotifications}
                  className="mt-4"
                >
                  Try Again
                </Button>
              </div>
            ) : getFilteredNotifications().length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                {activeTab !== 'all' ? (
                  <>
                    <p>No {activeTab} notifications found</p>
                    <Button
                      variant="link"
                      size="sm"
                      onClick={() => setActiveTab('all')}
                      className="mt-2"
                    >
                      View all notifications
                    </Button>
                  </>
                ) : (
                  <p>You have no notifications</p>
                )}
              </div>
            ) : (
              <div className="space-y-6">
                {Object.entries(groupedNotifications)
                  .sort((a, b) => new Date(b[0]).getTime() - new Date(a[0]).getTime())
                  .map(([date, dateNotifications]) => {
                    // Filter notifications based on active tab
                    const filteredDateNotifications = dateNotifications.filter(notification => {
                      if (activeTab === 'unread') return !notification.isRead;
                      if (activeTab === 'read') return notification.isRead;
                      return true;
                    });

                    if (filteredDateNotifications.length === 0) return null;

                    return (
                      <div key={date}>
                        <h3 className="text-sm font-medium text-gray-500 mb-2">
                          {new Date(date).toDateString() === new Date().toDateString()
                            ? 'Today'
                            : new Date(date).toDateString() === new Date(Date.now() - 86400000).toDateString()
                            ? 'Yesterday'
                            : format(new Date(date), 'MMMM d, yyyy')}
                        </h3>
                        <div className="space-y-2">
                          {filteredDateNotifications.map((notification) => (
                            <div
                              key={notification.id}
                              className={cn(
                                "flex items-start p-3 rounded-md border",
                                !notification.isRead
                                  ? "bg-blue-50 border-blue-200"
                                  : "bg-white border-gray-200"
                              )}
                            >
                              <div className="flex-shrink-0 mr-3 mt-0.5">
                                {getNotificationIcon(notification.type)}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center mb-1">
                                  <p className={cn("text-sm", !notification.isRead && "font-medium")}>
                                    {notification.message}
                                  </p>
                                </div>
                                <div className="flex items-center text-xs text-gray-500">
                                  <span>
                                    {formatDistanceToNow(new Date(notification.createdAt), {
                                      addSuffix: true,
                                    })}
                                  </span>
                                  {getNotificationTypeBadge(notification.type)}
                                </div>
                              </div>
                              <div className="flex-shrink-0 ml-3 flex space-x-2">
                                {!notification.isRead && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-8 w-8 p-0"
                                    onClick={() => markAsRead(notification.id)}
                                  >
                                    <CheckCircle className="h-4 w-4" />
                                    <span className="sr-only">Mark as read</span>
                                  </Button>
                                )}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-8 w-8 p-0 text-gray-500 hover:text-red-500"
                                  onClick={() => deleteNotification(notification.id)}
                                >
                                  <Trash2 className="h-4 w-4" />
                                  <span className="sr-only">Delete</span>
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    );
                  })}
              </div>
            )}
          </CardContent>
        </Tabs>
        <CardFooter className="flex justify-between border-t pt-4">
          <p className="text-xs text-gray-500">
            Showing {getFilteredNotifications().length} notifications
          </p>
          {notifications.length > 0 && (
            <Button variant="ghost" size="sm" onClick={fetchNotifications}>
              Refresh
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
