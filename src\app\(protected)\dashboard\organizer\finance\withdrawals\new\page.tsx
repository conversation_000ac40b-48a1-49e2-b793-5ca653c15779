'use client';

import React, { useState } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, Wallet, AlertCircle, Info } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

export default function NewWithdrawalPage() {
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('bank');
  const [bankAccount, setBankAccount] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Available balance (in a real app, this would come from an API)
  const availableBalance = 24320.75;

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Handle amount change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and decimal point
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
      setError('');
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate amount
    const numAmount = parseFloat(amount);
    if (!amount || isNaN(numAmount) || numAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }
    
    if (numAmount > availableBalance) {
      setError('Amount exceeds available balance');
      return;
    }
    
    if (numAmount < 100) {
      setError('Minimum withdrawal amount is $100');
      return;
    }
    
    // In a real app, you would submit to an API here
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to withdrawals page
      router.push('/dashboard/organizer/finance/withdrawals?success=true');
    }, 1500);
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="max-w-2xl mx-auto">
        <Button variant="ghost" asChild className="mb-4 -ml-4">
          <Link href="/dashboard/organizer/finance/withdrawals">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Withdrawals
          </Link>
        </Button>
        
        <h1 className="text-3xl font-bold mb-2">Withdraw Funds</h1>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Request a withdrawal from your available balance
        </p>
        
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Available Balance</CardTitle>
              <CardDescription>Amount available for withdrawal</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-3xl font-bold">{formatCurrency(availableBalance)}</p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Minimum withdrawal: {formatCurrency(100)}
                  </p>
                </div>
                <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded-full">
                  <Wallet className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Withdrawal Details</CardTitle>
                <CardDescription>Enter the amount and payment method</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="amount">Amount</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <Input
                      id="amount"
                      placeholder="0.00"
                      className="pl-8"
                      value={amount}
                      onChange={handleAmountChange}
                    />
                  </div>
                  {error && (
                    <div className="flex items-center text-red-600 text-sm mt-1">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {error}
                    </div>
                  )}
                </div>
                
                <div className="space-y-2">
                  <Label>Payment Method</Label>
                  <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod} className="flex flex-col space-y-2">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="bank" id="bank" />
                      <Label htmlFor="bank" className="cursor-pointer">Bank Transfer</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="paypal" id="paypal" />
                      <Label htmlFor="paypal" className="cursor-pointer">PayPal</Label>
                    </div>
                  </RadioGroup>
                </div>
                
                {paymentMethod === 'bank' && (
                  <div className="space-y-2">
                    <Label htmlFor="bankAccount">Bank Account</Label>
                    <Select value={bankAccount} onValueChange={setBankAccount}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a bank account" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="account1">**** **** **** 1234 - Checking</SelectItem>
                        <SelectItem value="account2">**** **** **** 5678 - Savings</SelectItem>
                        <SelectItem value="new">+ Add New Bank Account</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                {paymentMethod === 'paypal' && (
                  <div className="space-y-2">
                    <Label htmlFor="paypalEmail">PayPal Email</Label>
                    <Input id="paypalEmail" type="email" placeholder="<EMAIL>" />
                  </div>
                )}
                
                <div className="flex items-start p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="text-sm text-blue-800 dark:text-blue-300">
                    <p className="font-medium">Processing Time</p>
                    <p className="mt-1">Withdrawals are typically processed within 3-5 business days. Bank transfers may take additional time to appear in your account.</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/organizer/finance/withdrawals">Cancel</Link>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Processing...' : 'Withdraw Funds'}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </div>
      </div>
    </RoleGate>
  );
}
