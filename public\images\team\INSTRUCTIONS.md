# Instructions for Replacing the CEO Image

To replace the CEO image with the one from Facebook:

1. Download the image from this Facebook link:
   https://www.facebook.com/photo.php?fbid=2372869942955451&set=pb.100006974047538.-2207520000&type=3

2. Save the image as `blessing_mwandila.jpg` in this directory (`public/images/team/`).

3. Update the `TeamSection.tsx` component to use the local image path:
   ```typescript
   // In src/components/ui/TeamSection/TeamSection.tsx
   const team = [
     {
       name: 'B<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>',
       role: 'CEO',
       image: '/images/team/blessing_mwandila.jpg', // Use this local path
     },
     // ...
   ];
   ```

4. Restart the development server to see the changes.

## Alternative Approach

If you prefer to use the image directly from Facebook (not recommended due to potential CORS issues):

1. Update the `next.config.js` file to allow images from Facebook:
   ```javascript
   // In next.config.js
   const nextConfig = {
     images: {
       remotePatterns: [
         // ... existing patterns
         {
           protocol: 'https',
           hostname: '*.facebook.com',
         }
       ],
     },
   }
   ```

2. Use the direct Facebook image URL in the component.

However, it's generally better to host the image locally for reliability and performance reasons.
