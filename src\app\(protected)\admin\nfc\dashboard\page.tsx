'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  BarChart3,
  CreditCard,
  History,
  RefreshCw,
  Settings,
  Store,
  AlertCircle,
  ArrowUpRight,
  Users,
  DollarSign
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';

export default function AdminNFCDashboardPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [stats, setStats] = useState({
    totalCards: 0,
    activeCards: 0,
    totalTransactions: 0,
    totalRevenue: 0,
    activeEvents: 0,
    activeVendors: 0,
    recentTransactions: [],
    topEvents: [],
    topVendors: []
  });

  // Fetch NFC system stats
  const fetchStats = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError(null);

    try {
      // Call the API to get NFC system stats
      const response = await fetch('/api/admin/nfc/stats');

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching NFC stats:', err);
      setError('Failed to load NFC system statistics. Please try again.');

      // Set empty data on error
      setStats({
        totalCards: 0,
        activeCards: 0,
        totalTransactions: 0,
        totalRevenue: 0,
        activeEvents: 0,
        activeVendors: 0,
        recentTransactions: [],
        topEvents: [],
        topVendors: []
      });
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchStats();
  }, []);

  // Refresh data
  const refreshData = () => {
    fetchStats(true);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">NFC System Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Global overview and management of the NFC payment system
          </p>
        </div>
        <Button onClick={refreshData} disabled={isRefreshing}>
          {isRefreshing ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Card Stats */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-blue-500" />
              NFC Cards
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{stats.totalCards}</div>
                <p className="text-sm text-gray-500">
                  {stats.activeCards} active cards ({Math.round((stats.activeCards / stats.totalCards) * 100)}%)
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/cards">
                View All Cards
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Transaction Stats */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <History className="h-5 w-5 mr-2 text-purple-500" />
              Transactions
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{stats.totalTransactions.toLocaleString()}</div>
                <p className="text-sm text-gray-500">
                  Across {stats.activeEvents} active events
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/transactions">
                View All Transactions
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Revenue Stats */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <DollarSign className="h-5 w-5 mr-2 text-green-500" />
              Total Revenue
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
                <p className="text-sm text-gray-500">
                  From {stats.totalTransactions.toLocaleString()} transactions
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/analytics">
                View Analytics
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>

        {/* Vendor Stats */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Store className="h-5 w-5 mr-2 text-amber-500" />
              Active Vendors
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-4 w-3/4" />
              </div>
            ) : (
              <>
                <div className="text-3xl font-bold">{stats.activeVendors}</div>
                <p className="text-sm text-gray-500">
                  Using NFC payment terminals
                </p>
              </>
            )}
          </CardContent>
          <CardFooter>
            <Button variant="ghost" size="sm" className="w-full" asChild>
              <Link href="/admin/nfc/vendors">
                Manage Vendors
                <ArrowUpRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>

      <Tabs defaultValue="transactions" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="transactions">Recent Transactions</TabsTrigger>
          <TabsTrigger value="events">Top Events</TabsTrigger>
          <TabsTrigger value="vendors">Top Vendors</TabsTrigger>
        </TabsList>

        {/* Recent Transactions Tab */}
        <TabsContent value="transactions">
          <Card>
            <CardHeader>
              <CardTitle>Recent Transactions</CardTitle>
              <CardDescription>
                The most recent NFC payment transactions across all events
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-6 w-1/4" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {stats.recentTransactions.map((tx: any) => (
                    <div key={tx.id} className="flex justify-between items-center border-b pb-2">
                      <div>
                        <div className="font-medium">{tx.vendor.name}</div>
                        <div className="text-sm text-gray-500">{tx.event.name}</div>
                      </div>
                      <div className="text-right">
                        <div className={`font-medium ${tx.status === 'completed' ? 'text-green-600' : 'text-red-600'}`}>
                          {formatCurrency(tx.amount)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(tx.timestamp).toLocaleString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/nfc/transactions">
                  View All Transactions
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Top Events Tab */}
        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Top Events by Revenue</CardTitle>
              <CardDescription>
                Events with the highest NFC payment revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-6 w-1/4" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {stats.topEvents.map((event: any) => (
                    <div key={event.id} className="flex justify-between items-center border-b pb-2">
                      <div>
                        <div className="font-medium">{event.name}</div>
                        <div className="text-sm text-gray-500">{event.transactions.toLocaleString()} transactions</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-green-600">
                          {formatCurrency(event.revenue)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/nfc/analytics">
                  View Detailed Analytics
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        {/* Top Vendors Tab */}
        <TabsContent value="vendors">
          <Card>
            <CardHeader>
              <CardTitle>Top Vendors by Revenue</CardTitle>
              <CardDescription>
                Vendors with the highest NFC payment revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-4">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="flex justify-between items-center">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-6 w-1/4" />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="space-y-4">
                  {stats.topVendors.map((vendor: any) => (
                    <div key={vendor.id} className="flex justify-between items-center border-b pb-2">
                      <div>
                        <div className="font-medium">{vendor.name}</div>
                        <div className="text-sm text-gray-500">{vendor.transactions.toLocaleString()} transactions</div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-green-600">
                          {formatCurrency(vendor.revenue)}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href="/admin/nfc/vendors">
                  Manage Vendors
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button className="w-full justify-start" asChild>
              <Link href="/admin/nfc/cards">
                <CreditCard className="mr-2 h-4 w-4" />
                Manage NFC Cards
              </Link>
            </Button>
            <Button className="w-full justify-start" asChild>
              <Link href="/admin/nfc/transactions">
                <History className="mr-2 h-4 w-4" />
                View All Transactions
              </Link>
            </Button>
            <Button className="w-full justify-start" asChild>
              <Link href="/admin/nfc/vendors">
                <Store className="mr-2 h-4 w-4" />
                Manage Vendor Terminals
              </Link>
            </Button>
            <Button className="w-full justify-start" asChild>
              <Link href="/admin/nfc/analytics">
                <BarChart3 className="mr-2 h-4 w-4" />
                View System Analytics
              </Link>
            </Button>
            <Button className="w-full justify-start" asChild>
              <Link href="/admin/nfc/settings">
                <Settings className="mr-2 h-4 w-4" />
                Global NFC Settings
              </Link>
            </Button>
            <Button className="w-full justify-start" asChild>
              <Link href="/dashboard/admin/nfc-pricing">
                <DollarSign className="mr-2 h-4 w-4" />
                Manage NFC Pricing
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="font-medium">NFC System</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Operational
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Transaction Processing</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Operational
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Card Management</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Operational
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Analytics</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Operational
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="font-medium">Vendor Terminals</span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Operational
                </span>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button variant="outline" className="w-full" asChild>
              <Link href="/admin/nfc/settings">
                System Configuration
              </Link>
            </Button>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}
