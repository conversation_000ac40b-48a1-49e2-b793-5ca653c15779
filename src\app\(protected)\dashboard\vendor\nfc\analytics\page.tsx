'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import {
  CreditCard,
  Search,
  Loader2,
  Calendar,
  BarChart2,
  DollarSign,
  Users,
  ShoppingBag,
  Filter,
  Download,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Clock
} from 'lucide-react';
import { format, subDays, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

// Chart components
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Pie<PERSON>hart,
  Pie,
  Cell
} from 'recharts';

interface Transaction {
  id: string;
  eventId: string;
  eventTitle: string;
  userId: string;
  userName: string;
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED' | 'CANCELLED';
  createdAt: string;
  processedAt: string | null;
  productCount: number;
}

interface AnalyticsSummary {
  totalTransactions: number;
  totalAmount: number;
  completedTransactions: number;
  completedAmount: number;
  averageTransactionValue: number;
  topEvents: {
    eventId: string;
    eventTitle: string;
    transactionCount: number;
    totalAmount: number;
  }[];
  transactionsByDay: {
    date: string;
    count: number;
    amount: number;
  }[];
  transactionsByStatus: {
    status: string;
    count: number;
  }[];
}

export default function NFCAnalyticsPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState<'TODAY' | 'WEEK' | 'MONTH' | 'ALL'>('WEEK');
  const [eventFilter, setEventFilter] = useState<string>('ALL');
  const [analytics, setAnalytics] = useState<AnalyticsSummary | null>(null);
  const [events, setEvents] = useState<{id: string, title: string}[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];
  const STATUS_COLORS = {
    'COMPLETED': '#10B981',
    'PENDING': '#F59E0B',
    'FAILED': '#EF4444',
    'REFUNDED': '#6366F1',
    'CANCELLED': '#9CA3AF'
  };

  useEffect(() => {
    fetchEvents();
  }, []);

  useEffect(() => {
    fetchAnalytics();
  }, [dateRange, eventFilter]);

  async function fetchEvents() {
    try {
      const response = await fetch('/api/vendors/events?status=APPROVED');
      
      if (!response.ok) {
        throw new Error('Failed to fetch events');
      }
      
      const data = await response.json();
      setEvents(data.map((event: any) => ({
        id: event.id,
        title: event.title
      })));
    } catch (err) {
      console.error('Error fetching events:', err);
      toast({
        title: 'Error',
        description: 'Failed to load events. Please try again.',
        variant: 'destructive'
      });
    }
  }

  async function fetchAnalytics() {
    try {
      setLoading(true);
      setRefreshing(true);
      
      // Build query parameters
      const params = new URLSearchParams();
      
      if (dateRange !== 'ALL') {
        params.append('dateRange', dateRange);
      }
      
      if (eventFilter !== 'ALL') {
        params.append('eventId', eventFilter);
      }
      
      const response = await fetch(`/api/vendors/nfc/analytics?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      
      const data = await response.json();
      setAnalytics(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError('Failed to load analytics data. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load analytics data. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }

  function getDateRangeLabel() {
    const today = new Date();
    
    switch(dateRange) {
      case 'TODAY':
        return format(today, 'PPP');
      case 'WEEK':
        return `${format(startOfWeek(today), 'MMM d')} - ${format(endOfWeek(today), 'MMM d, yyyy')}`;
      case 'MONTH':
        return format(today, 'MMMM yyyy');
      default:
        return 'All Time';
    }
  }

  function formatCurrency(amount: number) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">NFC Payment Analytics</h1>
          <p className="text-muted-foreground">
            View and analyze your NFC payment transactions
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchAnalytics()}
            disabled={refreshing}
          >
            {refreshing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              // Export analytics data as CSV
              toast({
                title: 'Export Started',
                description: 'Your analytics data is being exported.',
              });
            }}
          >
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4">
        <Select
          value={dateRange}
          onValueChange={(value) => setDateRange(value as 'TODAY' | 'WEEK' | 'MONTH' | 'ALL')}
        >
          <SelectTrigger className="w-full md:w-[180px]">
            <Calendar className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="TODAY">Today</SelectItem>
            <SelectItem value="WEEK">This Week</SelectItem>
            <SelectItem value="MONTH">This Month</SelectItem>
            <SelectItem value="ALL">All Time</SelectItem>
          </SelectContent>
        </Select>
        
        <Select
          value={eventFilter}
          onValueChange={setEventFilter}
        >
          <SelectTrigger className="w-full md:w-[250px]">
            <Filter className="mr-2 h-4 w-4" />
            <SelectValue placeholder="Filter by event" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="ALL">All Events</SelectItem>
            {events.map(event => (
              <SelectItem key={event.id} value={event.id}>
                {event.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      
      {loading && !analytics ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading analytics data...</span>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64">
            <p className="text-destructive text-center">{error}</p>
            <Button
              variant="outline"
              onClick={() => fetchAnalytics()}
              className="mt-4"
            >
              Try Again
            </Button>
          </CardContent>
        </Card>
      ) : analytics ? (
        <>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            {/* Total Transactions Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Transactions
                </CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analytics.totalTransactions}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {getDateRangeLabel()}
                </p>
              </CardContent>
            </Card>
            
            {/* Total Revenue Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Revenue
                </CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analytics.totalAmount)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {getDateRangeLabel()}
                </p>
              </CardContent>
            </Card>
            
            {/* Average Transaction Value Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Average Value
                </CardTitle>
                <BarChart2 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analytics.averageTransactionValue)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  Per transaction
                </p>
              </CardContent>
            </Card>
            
            {/* Completion Rate Card */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Completion Rate
                </CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analytics.totalTransactions > 0 
                    ? `${Math.round((analytics.completedTransactions / analytics.totalTransactions) * 100)}%` 
                    : '0%'}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analytics.completedTransactions} of {analytics.totalTransactions} completed
                </p>
              </CardContent>
            </Card>
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            {/* Transactions Over Time Chart */}
            <Card className="col-span-2 md:col-span-1">
              <CardHeader>
                <CardTitle>Transactions Over Time</CardTitle>
                <CardDescription>
                  Transaction volume and revenue for {getDateRangeLabel()}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {analytics.transactionsByDay.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={analytics.transactionsByDay}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis yAxisId="left" />
                        <YAxis yAxisId="right" orientation="right" />
                        <Tooltip />
                        <Legend />
                        <Line
                          yAxisId="left"
                          type="monotone"
                          dataKey="count"
                          name="Transactions"
                          stroke="#8884d8"
                          activeDot={{ r: 8 }}
                        />
                        <Line
                          yAxisId="right"
                          type="monotone"
                          dataKey="amount"
                          name="Revenue"
                          stroke="#82ca9d"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No transaction data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
            
            {/* Transactions by Status Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Transactions by Status</CardTitle>
                <CardDescription>
                  Distribution of transaction statuses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {analytics.transactionsByStatus.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analytics.transactionsByStatus}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="count"
                          nameKey="status"
                        >
                          {analytics.transactionsByStatus.map((entry, index) => (
                            <Cell 
                              key={`cell-${index}`} 
                              fill={STATUS_COLORS[entry.status as keyof typeof STATUS_COLORS] || COLORS[index % COLORS.length]} 
                            />
                          ))}
                        </Pie>
                        <Tooltip />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No status data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Top Events Table */}
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Events</CardTitle>
              <CardDescription>
                Events with the highest transaction volume and revenue
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analytics.topEvents.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Event</TableHead>
                      <TableHead className="text-right">Transactions</TableHead>
                      <TableHead className="text-right">Revenue</TableHead>
                      <TableHead className="text-right">Avg. Value</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analytics.topEvents.map((event) => (
                      <TableRow key={event.eventId}>
                        <TableCell className="font-medium">{event.eventTitle}</TableCell>
                        <TableCell className="text-right">{event.transactionCount}</TableCell>
                        <TableCell className="text-right">{formatCurrency(event.totalAmount)}</TableCell>
                        <TableCell className="text-right">
                          {formatCurrency(event.transactionCount > 0 
                            ? event.totalAmount / event.transactionCount 
                            : 0)}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="py-6 text-center text-muted-foreground">
                  No event data available
                </div>
              )}
            </CardContent>
          </Card>
        </>
      ) : null}
    </div>
  );
}