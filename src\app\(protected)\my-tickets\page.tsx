'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, MapPin, QrCode, Ticket, CreditCard, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { format } from 'date-fns';
import Image from 'next/image';
import Link from 'next/link';
import { toast } from '@/components/ui/use-toast';

// Types
interface NfcDevice {
  id: string;
  type: 'CARD' | 'WRISTBAND' | 'TAG';
  status: 'ACTIVE' | 'DEACTIVATED' | 'LOST';
  balance: number;
  lastUsed: string | null;
  issuedAt: string;
}

interface Ticket {
  id: string;
  eventId: string;
  eventTitle: string;
  eventDate: string;
  eventTime: string;
  eventLocation: string;
  eventImageUrl: string;
  ticketType: string;
  ticketPrice: number;
  purchaseDate: string;
  status: 'UPCOMING' | 'PAST' | 'CANCELLED';
  isUsed: boolean;
  qrCodeData: string;
}

export default function MyTicketsPage() {
  const { data: session, status: sessionStatus } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('upcoming');
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [nfcDevices, setNfcDevices] = useState<NfcDevice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTicket, setSelectedTicket] = useState<Ticket | null>(null);
  const [showQrCode, setShowQrCode] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (sessionStatus === 'unauthenticated') {
      router.push('/auth/login?callbackUrl=/my-tickets');
    }
  }, [sessionStatus, router]);

  // Fetch tickets and NFC devices
  useEffect(() => {
    const fetchUserData = async () => {
      if (sessionStatus !== 'authenticated') return;

      setIsLoading(true);

      try {
        // Fetch tickets
        const ticketsResponse = await fetch('/api/user/tickets');
        if (ticketsResponse.ok) {
          const ticketsData = await ticketsResponse.json();
          setTickets(ticketsData);
        }

        // Fetch NFC devices
        const nfcResponse = await fetch('/api/user/nfc-devices');
        if (nfcResponse.ok) {
          const nfcData = await nfcResponse.json();
          setNfcDevices(nfcData);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load your tickets and devices',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserData();
  }, [sessionStatus]);

  // Filter tickets based on active tab
  const filteredTickets = tickets.filter(ticket => {
    if (activeTab === 'upcoming') return ticket.status === 'UPCOMING';
    if (activeTab === 'past') return ticket.status === 'PAST';
    if (activeTab === 'cancelled') return ticket.status === 'CANCELLED';
    return true;
  });

  // Handle NFC device status change
  const handleNfcStatusChange = async (deviceId: string, newStatus: 'ACTIVE' | 'DEACTIVATED') => {
    try {
      const response = await fetch(`/api/user/nfc-devices/${deviceId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        // Update local state
        setNfcDevices(prev =>
          prev.map(device =>
            device.id === deviceId ? { ...device, status: newStatus } : device
          )
        );

        toast({
          title: 'Success',
          description: `Device ${newStatus === 'ACTIVE' ? 'activated' : 'deactivated'} successfully`,
        });
      } else {
        throw new Error('Failed to update device status');
      }
    } catch (error) {
      console.error('Error updating NFC device status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update device status',
        variant: 'destructive',
      });
    }
  };

  // Handle reporting a device as lost
  const handleReportLost = async (deviceId: string) => {
    try {
      const response = await fetch(`/api/user/nfc-devices/${deviceId}/report-lost`, {
        method: 'PUT',
      });

      if (response.ok) {
        // Update local state
        setNfcDevices(prev =>
          prev.map(device =>
            device.id === deviceId ? { ...device, status: 'LOST' } : device
          )
        );

        toast({
          title: 'Device Reported Lost',
          description: 'Your device has been deactivated for security',
        });
      } else {
        throw new Error('Failed to report device as lost');
      }
    } catch (error) {
      console.error('Error reporting device as lost:', error);
      toast({
        title: 'Error',
        description: 'Failed to report device as lost',
        variant: 'destructive',
      });
    }
  };

  // Show loading skeleton if data is loading
  if (isLoading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <h1 className="text-2xl font-bold mb-6">My Tickets & Devices</h1>

        <Tabs defaultValue="upcoming">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="past">Past</TabsTrigger>
            <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
            <TabsTrigger value="nfc">NFC Devices</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="overflow-hidden">
                <div className="flex flex-col md:flex-row">
                  <div className="md:w-1/3 bg-gray-200 h-48 md:h-auto">
                    <Skeleton className="h-full w-full" />
                  </div>
                  <div className="p-4 md:p-6 md:w-2/3">
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-4 w-1/2 mb-4" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-2/3" />
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <h1 className="text-2xl font-bold mb-6">My Tickets & Devices</h1>

      <Tabs defaultValue="upcoming" onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4 mb-8">
          <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
          <TabsTrigger value="past">Past</TabsTrigger>
          <TabsTrigger value="cancelled">Cancelled</TabsTrigger>
          <TabsTrigger value="nfc">NFC Devices</TabsTrigger>
        </TabsList>

        {/* Tickets Tabs */}
        {['upcoming', 'past', 'cancelled'].map((tabValue) => (
          <TabsContent key={tabValue} value={tabValue} className="space-y-4">
            {filteredTickets.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Ticket className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-xl font-medium text-gray-700">No {tabValue} tickets found</h3>
                  <p className="text-gray-500 mt-2 text-center">
                    {tabValue === 'upcoming'
                      ? "You don't have any upcoming events. Browse events to find your next experience!"
                      : tabValue === 'past'
                      ? "You haven't attended any events yet."
                      : "You don't have any cancelled tickets."}
                  </p>
                  {tabValue === 'upcoming' && (
                    <Button asChild className="mt-6">
                      <Link href="/events">Browse Events</Link>
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              filteredTickets.map((ticket) => (
                <Card key={ticket.id} className="overflow-hidden">
                  <div className="flex flex-col md:flex-row">
                    <div className="md:w-1/3 relative h-48 md:h-auto">
                      <Image
                        src={ticket.eventImageUrl || '/images/event-placeholder.jpg'}
                        alt={ticket.eventTitle}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div className="p-4 md:p-6 md:w-2/3">
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="text-xl font-semibold">{ticket.eventTitle}</h3>
                          <p className="text-gray-500 mb-4">{ticket.ticketType}</p>
                        </div>
                        <Badge
                          className={
                            ticket.status === 'UPCOMING'
                              ? 'bg-green-100 text-green-800 hover:bg-green-100'
                              : ticket.status === 'PAST'
                              ? 'bg-gray-100 text-gray-800 hover:bg-gray-100'
                              : 'bg-red-100 text-red-800 hover:bg-red-100'
                          }
                        >
                          {ticket.status === 'UPCOMING' && !ticket.isUsed && 'Upcoming'}
                          {ticket.status === 'UPCOMING' && ticket.isUsed && 'Used'}
                          {ticket.status === 'PAST' && 'Past'}
                          {ticket.status === 'CANCELLED' && 'Cancelled'}
                        </Badge>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{format(new Date(ticket.eventDate), 'EEEE, MMMM d, yyyy')}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{ticket.eventTime}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{ticket.eventLocation}</span>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                        {ticket.status === 'UPCOMING' && !ticket.isUsed && (
                          <>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                setSelectedTicket(ticket);
                                setShowQrCode(true);
                              }}
                            >
                              <QrCode className="h-4 w-4 mr-2" />
                              Show QR Code
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              asChild
                            >
                              <Link href={`/api/tickets/download/${ticket.id}`} target="_blank">
                                <Ticket className="h-4 w-4 mr-2" />
                                Download Ticket
                              </Link>
                            </Button>
                          </>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/events/${ticket.eventId}`}>
                            View Event
                          </Link>
                        </Button>
                      </div>
                    </div>
                  </div>
                </Card>
              ))
            )}
          </TabsContent>
        ))}

        {/* NFC Devices Tab */}
        <TabsContent value="nfc">
          <Card>
            <CardHeader>
              <CardTitle>My NFC Devices</CardTitle>
              <CardDescription>
                Manage your NFC cards, wristbands, and tags for contactless payments
              </CardDescription>
            </CardHeader>
            <CardContent>
              {nfcDevices.length === 0 ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-xl font-medium text-gray-700">No NFC devices found</h3>
                  <p className="text-gray-500 mt-2 text-center">
                    You don't have any NFC devices linked to your account yet.
                  </p>
                  <Button asChild className="mt-6">
                    <Link href="/nfc-store">Get an NFC Device</Link>
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  {nfcDevices.map((device) => (
                    <div key={device.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div className="flex items-center">
                          {device.type === 'CARD' && <CreditCard className="h-5 w-5 mr-2 text-blue-600" />}
                          {device.type === 'WRISTBAND' && <div className="h-5 w-5 mr-2 rounded-full border-2 border-blue-600" />}
                          {device.type === 'TAG' && <QrCode className="h-5 w-5 mr-2 text-blue-600" />}
                          <div>
                            <h3 className="font-medium">{device.type.charAt(0) + device.type.slice(1).toLowerCase()}</h3>
                            <p className="text-sm text-gray-500">
                              Issued: {format(new Date(device.issuedAt), 'MMM d, yyyy')}
                            </p>
                          </div>
                        </div>
                        <Badge
                          className={
                            device.status === 'ACTIVE'
                              ? 'bg-green-100 text-green-800 hover:bg-green-100'
                              : device.status === 'DEACTIVATED'
                              ? 'bg-gray-100 text-gray-800 hover:bg-gray-100'
                              : 'bg-red-100 text-red-800 hover:bg-red-100'
                          }
                        >
                          {device.status}
                        </Badge>
                      </div>

                      <div className="mt-4 grid grid-cols-2 gap-4">
                        <div>
                          <p className="text-sm text-gray-500">Balance</p>
                          <p className="font-semibold">${device.balance.toFixed(2)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-500">Last Used</p>
                          <p className="font-semibold">
                            {device.lastUsed
                              ? format(new Date(device.lastUsed), 'MMM d, yyyy')
                              : 'Never used'}
                          </p>
                        </div>
                      </div>

                      <div className="mt-4 pt-4 border-t border-gray-200 flex flex-wrap gap-2">
                        {device.status === 'ACTIVE' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleNfcStatusChange(device.id, 'DEACTIVATED')}
                          >
                            <XCircle className="h-4 w-4 mr-2 text-red-500" />
                            Deactivate
                          </Button>
                        ) : device.status === 'DEACTIVATED' ? (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleNfcStatusChange(device.id, 'ACTIVE')}
                          >
                            <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                            Activate
                          </Button>
                        ) : null}

                        {device.status !== 'LOST' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleReportLost(device.id)}
                          >
                            <AlertTriangle className="h-4 w-4 mr-2 text-amber-500" />
                            Report Lost
                          </Button>
                        )}

                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/nfc-topup?deviceId=${device.id}`}>
                            Top Up
                          </Link>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <Link href={`/my-tickets?tab=nfc&deviceId=${device.id}`}>
                            View Details
                          </Link>
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
            <CardFooter className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 sm:justify-between">
              <Button variant="outline" asChild className="w-full sm:w-auto">
                <Link href="/dashboard">Back to Dashboard</Link>
              </Button>
              <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2">
                <Button variant="outline" asChild className="w-full sm:w-auto">
                  <Link href="/nfc-store">Buy NFC Devices</Link>
                </Button>
                <Button asChild className="w-full sm:w-auto">
                  <Link href="/nfc-topup">Top Up Balance</Link>
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>

      {/* QR Code Modal */}
      {showQrCode && selectedTicket && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-xl font-semibold mb-4">{selectedTicket.eventTitle}</h3>
            <div className="bg-gray-100 p-4 rounded-lg flex items-center justify-center mb-4">
              <div className="w-64 h-64 relative">
                <Image
                  src={`/api/tickets/qr/${selectedTicket.id}`}
                  alt="Ticket QR Code"
                  fill
                  className="object-contain"
                />
              </div>
            </div>
            <p className="text-center text-sm text-gray-500 mb-4">
              Present this QR code at the event entrance for scanning
            </p>
            <div className="flex justify-end">
              <Button onClick={() => setShowQrCode(false)}>Close</Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
