const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  try {
    // Check if organizer already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>',
      },
    });

    if (existingUser) {
      console.log('Organizer already exists with email: <EMAIL>');
      return;
    }

    // Create a new organizer
    const hashedPassword = await bcrypt.hash('password123', 10);
    
    const organizer = await prisma.user.create({
      data: {
        name: 'Test Organizer',
        email: '<EMAIL>',
        password: hashedPassword,
        role: 'ORGANIZER',
        emailVerified: new Date(),
        accountBalance: 1000.00,
        isVerified: true,
      },
    });

    console.log('Created organizer:', organizer);

    // Create a sample event for the organizer
    const event = await prisma.event.create({
      data: {
        title: 'Sample Music Festival',
        description: 'A great music festival with local and international artists',
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(Date.now() + 32 * 24 * 60 * 60 * 1000), // 32 days from now
        startTime: '18:00',
        endTime: '23:00',
        location: 'Central Park',
        venue: 'Main Stage',
        category: 'MUSIC',
        eventType: 'PHYSICAL',
        status: 'Published',
        userId: organizer.id,
        timeZone: 'UTC+2'
      },
    });

    console.log('Created event:', event);

    // Create a financial transaction for the organizer
    const transaction = await prisma.financialTransaction.create({
      data: {
        userId: organizer.id,
        amount: 500.00,
        type: 'DEPOSIT',
        description: 'Initial deposit',
      },
    });

    console.log('Created transaction:', transaction);

    console.log('Setup complete!');
  } catch (error) {
    console.error('Error creating organizer:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
