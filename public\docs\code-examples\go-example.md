# Go API Examples

This guide provides examples of how to use the QuickTime Events API with Go.

## Prerequisites

- Go 1.16 or higher

## Authentication

```go
package main

import (
	"fmt"
	"net/http"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// Create a custom HTTP client with timeout
var client = &http.Client{
	Timeout: time.Second * 10,
}

// Helper function to create a new request with API key
func newRequest(method, url string, body []byte) (*http.Request, error) {
	req, err := http.NewRequest(method, url, nil)
	if err != nil {
		return nil, err
	}
	
	// Add API key header
	req.Header.Add("X-API-Key", APIKey)
	req.Header.Add("Content-Type", "application/json")
	
	return req, nil
}
```

## Get Published Events

```go
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// Event represents an event from the API
type Event struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	StartDate   time.Time `json:"startDate"`
	EndDate     time.Time `json:"endDate"`
	Location    string    `json:"location"`
	Venue       string    `json:"venue"`
	Category    string    `json:"category"`
	EventType   string    `json:"eventType"`
}

// GetPublishedEvents fetches published events from the API
func GetPublishedEvents() ([]Event, error) {
	// Create a new HTTP client
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	
	// Create a new request
	req, err := http.NewRequest("GET", BaseURL+"/events/published", nil)
	if err != nil {
		return nil, err
	}
	
	// Add API key header
	req.Header.Add("X-API-Key", APIKey)
	
	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var events []Event
	if err := json.NewDecoder(resp.Body).Decode(&events); err != nil {
		return nil, err
	}
	
	return events, nil
}

func main() {
	events, err := GetPublishedEvents()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Found %d events\n", len(events))
	for _, event := range events {
		fmt.Printf("Event: %s\n", event.Title)
		fmt.Printf("Date: %s\n", event.StartDate.Format(time.RFC3339))
		fmt.Printf("Location: %s\n", event.Location)
		fmt.Println("---")
	}
}
```

## Create an Event

```go
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// Event represents an event from the API
type Event struct {
	ID          string    `json:"id,omitempty"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	StartDate   time.Time `json:"startDate"`
	EndDate     time.Time `json:"endDate"`
	Location    string    `json:"location"`
	Venue       string    `json:"venue"`
	Category    string    `json:"category"`
	EventType   string    `json:"eventType"`
}

// CreateEvent creates a new event via the API
func CreateEvent(event Event) (*Event, error) {
	// Create a new HTTP client
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	
	// Convert event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}
	
	// Create a new request
	req, err := http.NewRequest("POST", BaseURL+"/events/create", bytes.NewBuffer(eventJSON))
	if err != nil {
		return nil, err
	}
	
	// Add headers
	req.Header.Add("X-API-Key", APIKey)
	req.Header.Add("Content-Type", "application/json")
	
	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var createdEvent Event
	if err := json.NewDecoder(resp.Body).Decode(&createdEvent); err != nil {
		return nil, err
	}
	
	return &createdEvent, nil
}

func main() {
	// Create an event starting tomorrow
	tomorrow := time.Now().AddDate(0, 0, 1)
	dayAfter := tomorrow.AddDate(0, 0, 1)
	
	newEvent := Event{
		Title:       "Go Meetup",
		Description: "A meetup for Go enthusiasts",
		StartDate:   tomorrow,
		EndDate:     dayAfter,
		Location:    "San Francisco",
		Venue:       "Tech Hub",
		Category:    "TECHNOLOGY",
		EventType:   "WORKSHOP",
	}
	
	createdEvent, err := CreateEvent(newEvent)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Event created with ID: %s\n", createdEvent.ID)
}
```

## Get Event Details

```go
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// Event represents an event from the API
type Event struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	StartDate   time.Time `json:"startDate"`
	EndDate     time.Time `json:"endDate"`
	Location    string    `json:"location"`
	Venue       string    `json:"venue"`
	Category    string    `json:"category"`
	EventType   string    `json:"eventType"`
}

// GetEventDetails fetches details for a specific event
func GetEventDetails(eventID string) (*Event, error) {
	// Create a new HTTP client
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	
	// Create a new request
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/eventdetails/%s", BaseURL, eventID), nil)
	if err != nil {
		return nil, err
	}
	
	// Add API key header
	req.Header.Add("X-API-Key", APIKey)
	
	// Send the request
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var event Event
	if err := json.NewDecoder(resp.Body).Decode(&event); err != nil {
		return nil, err
	}
	
	return &event, nil
}

func main() {
	eventID := "your-event-id"
	event, err := GetEventDetails(eventID)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Event: %s\n", event.Title)
	fmt.Printf("Description: %s\n", event.Description)
	fmt.Printf("Date: %s to %s\n", event.StartDate.Format(time.RFC3339), event.EndDate.Format(time.RFC3339))
	fmt.Printf("Location: %s, %s\n", event.Location, event.Venue)
	fmt.Printf("Category: %s\n", event.Category)
	fmt.Printf("Type: %s\n", event.EventType)
}
```

## Handling Rate Limits

```go
package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"strconv"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// MakeRequestWithRateLimitHandling makes an API request with rate limit handling
func MakeRequestWithRateLimitHandling(method, url string, body []byte) (*http.Response, error) {
	// Create a new HTTP client
	client := &http.Client{
		Timeout: time.Second * 10,
	}
	
	maxRetries := 5
	retries := 0
	
	for retries < maxRetries {
		// Create a new request
		req, err := http.NewRequest(method, url, nil)
		if err != nil {
			return nil, err
		}
		
		// Add headers
		req.Header.Add("X-API-Key", APIKey)
		req.Header.Add("Content-Type", "application/json")
		
		// Send the request
		resp, err := client.Do(req)
		if err != nil {
			retries++
			if retries == maxRetries {
				return nil, err
			}
			
			// Exponential backoff
			waitTime := time.Duration(math.Pow(2, float64(retries))) * time.Second
			fmt.Printf("Request failed. Retrying in %v seconds...\n", waitTime.Seconds())
			time.Sleep(waitTime)
			continue
		}
		
		// Check for rate limit headers
		if limit := resp.Header.Get("X-RateLimit-Limit"); limit != "" {
			remaining := resp.Header.Get("X-RateLimit-Remaining")
			fmt.Printf("Rate limit: %s/%s requests remaining\n", remaining, limit)
		}
		
		// If rate limited, wait and retry
		if resp.StatusCode == http.StatusTooManyRequests {
			retries++
			
			// Get retry delay from headers or default to 60 seconds
			retryAfter := 60
			if retryHeader := resp.Header.Get("Retry-After"); retryHeader != "" {
				if seconds, err := strconv.Atoi(retryHeader); err == nil {
					retryAfter = seconds
				}
			}
			
			fmt.Printf("Rate limited. Waiting %d seconds...\n", retryAfter)
			time.Sleep(time.Duration(retryAfter) * time.Second)
			continue
		}
		
		return resp, nil
	}
	
	return nil, fmt.Errorf("max retries exceeded")
}

// GetPublishedEvents fetches published events from the API with rate limit handling
func GetPublishedEvents() ([]map[string]interface{}, error) {
	resp, err := MakeRequestWithRateLimitHandling("GET", BaseURL+"/events/published", nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var events []map[string]interface{}
	if err := json.NewDecoder(resp.Body).Decode(&events); err != nil {
		return nil, err
	}
	
	return events, nil
}

func main() {
	events, err := GetPublishedEvents()
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Found %d events\n", len(events))
}
```

## Complete Example: Event Manager

```go
package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

const (
	APIKey  = "your-api-key"
	BaseURL = "https://your-domain.com/api"
)

// Event represents an event from the API
type Event struct {
	ID          string    `json:"id,omitempty"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	StartDate   time.Time `json:"startDate"`
	EndDate     time.Time `json:"endDate"`
	Location    string    `json:"location"`
	Venue       string    `json:"venue"`
	Category    string    `json:"category"`
	EventType   string    `json:"eventType"`
}

// EventAPIClient is a client for the QuickTime Events API
type EventAPIClient struct {
	APIKey  string
	BaseURL string
	Client  *http.Client
}

// NewEventAPIClient creates a new API client
func NewEventAPIClient(apiKey string, baseURL string) *EventAPIClient {
	return &EventAPIClient{
		APIKey:  apiKey,
		BaseURL: baseURL,
		Client: &http.Client{
			Timeout: time.Second * 10,
		},
	}
}

// MakeRequest makes an API request with rate limit handling
func (c *EventAPIClient) MakeRequest(method, endpoint string, body []byte, params map[string]string) (*http.Response, error) {
	maxRetries := 5
	retries := 0
	
	for retries < maxRetries {
		// Build URL with query parameters
		reqURL, err := url.Parse(c.BaseURL + endpoint)
		if err != nil {
			return nil, err
		}
		
		if params != nil {
			query := reqURL.Query()
			for key, value := range params {
				query.Add(key, value)
			}
			reqURL.RawQuery = query.Encode()
		}
		
		// Create a new request
		req, err := http.NewRequest(method, reqURL.String(), bytes.NewBuffer(body))
		if err != nil {
			return nil, err
		}
		
		// Add headers
		req.Header.Add("X-API-Key", c.APIKey)
		req.Header.Add("Content-Type", "application/json")
		
		// Send the request
		resp, err := c.Client.Do(req)
		if err != nil {
			retries++
			if retries == maxRetries {
				return nil, err
			}
			
			// Exponential backoff
			waitTime := time.Duration(math.Pow(2, float64(retries))) * time.Second
			fmt.Printf("Request failed. Retrying in %v seconds...\n", waitTime.Seconds())
			time.Sleep(waitTime)
			continue
		}
		
		// Check for rate limit headers
		if limit := resp.Header.Get("X-RateLimit-Limit"); limit != "" {
			remaining := resp.Header.Get("X-RateLimit-Remaining")
			fmt.Printf("Rate limit: %s/%s requests remaining\n", remaining, limit)
		}
		
		// If rate limited, wait and retry
		if resp.StatusCode == http.StatusTooManyRequests {
			retries++
			
			// Get retry delay from headers or default to 60 seconds
			retryAfter := 60
			if retryHeader := resp.Header.Get("Retry-After"); retryHeader != "" {
				if seconds, err := strconv.Atoi(retryHeader); err == nil {
					retryAfter = seconds
				}
			}
			
			fmt.Printf("Rate limited. Waiting %d seconds...\n", retryAfter)
			time.Sleep(time.Duration(retryAfter) * time.Second)
			continue
		}
		
		return resp, nil
	}
	
	return nil, fmt.Errorf("max retries exceeded")
}

// GetPublishedEvents fetches published events from the API
func (c *EventAPIClient) GetPublishedEvents(page int, limit int) ([]Event, error) {
	params := map[string]string{
		"page":  strconv.Itoa(page),
		"limit": strconv.Itoa(limit),
	}
	
	resp, err := c.MakeRequest("GET", "/events/published", nil, params)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var events []Event
	if err := json.NewDecoder(resp.Body).Decode(&events); err != nil {
		return nil, err
	}
	
	return events, nil
}

// GetEventDetails fetches details for a specific event
func (c *EventAPIClient) GetEventDetails(eventID string) (*Event, error) {
	resp, err := c.MakeRequest("GET", fmt.Sprintf("/eventdetails/%s", eventID), nil, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var event Event
	if err := json.NewDecoder(resp.Body).Decode(&event); err != nil {
		return nil, err
	}
	
	return &event, nil
}

// CreateEvent creates a new event via the API
func (c *EventAPIClient) CreateEvent(event Event) (*Event, error) {
	// Convert event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}
	
	resp, err := c.MakeRequest("POST", "/events/create", eventJSON, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var createdEvent Event
	if err := json.NewDecoder(resp.Body).Decode(&createdEvent); err != nil {
		return nil, err
	}
	
	return &createdEvent, nil
}

// UpdateEvent updates an existing event via the API
func (c *EventAPIClient) UpdateEvent(eventID string, event Event) (*Event, error) {
	// Convert event to JSON
	eventJSON, err := json.Marshal(event)
	if err != nil {
		return nil, err
	}
	
	resp, err := c.MakeRequest("PUT", fmt.Sprintf("/events/%s", eventID), eventJSON, nil)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return nil, fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	// Parse the response
	var updatedEvent Event
	if err := json.NewDecoder(resp.Body).Decode(&updatedEvent); err != nil {
		return nil, err
	}
	
	return &updatedEvent, nil
}

// DeleteEvent deletes an event via the API
func (c *EventAPIClient) DeleteEvent(eventID string) error {
	resp, err := c.MakeRequest("DELETE", fmt.Sprintf("/events/%s", eventID), nil, nil)
	if err != nil {
		return err
	}
	defer resp.Body.Close()
	
	// Check response status
	if resp.StatusCode != http.StatusOK {
		body, _ := ioutil.ReadAll(resp.Body)
		return fmt.Errorf("API error: %d - %s", resp.StatusCode, string(body))
	}
	
	return nil
}

func main() {
	client := NewEventAPIClient(APIKey, BaseURL)
	
	// Get published events
	events, err := client.GetPublishedEvents(1, 10)
	if err != nil {
		fmt.Printf("Error: %v\n", err)
		return
	}
	
	fmt.Printf("Found %d events\n", len(events))
	
	// Show first 3 events
	for i := 0; i < len(events) && i < 3; i++ {
		fmt.Printf("- %s (%s)\n", events[i].Title, events[i].StartDate.Format(time.RFC3339))
	}
	
	// Create a new event
	tomorrow := time.Now().AddDate(0, 0, 1)
	dayAfter := tomorrow.AddDate(0, 0, 1)
	
	newEvent := Event{
		Title:       "Go API Workshop",
		Description: "Learn how to use APIs with Go",
		StartDate:   tomorrow,
		EndDate:     dayAfter,
		Location:    "Online",
		Venue:       "Zoom",
		Category:    "TECHNOLOGY",
		EventType:   "WORKSHOP",
	}
	
	createdEvent, err := client.CreateEvent(newEvent)
	if err != nil {
		fmt.Printf("Error creating event: %v\n", err)
		return
	}
	
	fmt.Printf("Created new event: %s (ID: %s)\n", createdEvent.Title, createdEvent.ID)
	
	// Get the event details
	eventDetails, err := client.GetEventDetails(createdEvent.ID)
	if err != nil {
		fmt.Printf("Error getting event details: %v\n", err)
		return
	}
	
	eventJSON, _ := json.MarshalIndent(eventDetails, "", "  ")
	fmt.Printf("Event details: %s\n", string(eventJSON))
	
	// Update the event
	updateEvent := *eventDetails
	updateEvent.Title = "Updated: Go API Workshop"
	updateEvent.Description = "Updated description: Learn how to use APIs with Go"
	
	updatedEvent, err := client.UpdateEvent(createdEvent.ID, updateEvent)
	if err != nil {
		fmt.Printf("Error updating event: %v\n", err)
		return
	}
	
	fmt.Printf("Updated event title to: %s\n", updatedEvent.Title)
	
	// Delete the event
	if err := client.DeleteEvent(createdEvent.ID); err != nil {
		fmt.Printf("Error deleting event: %v\n", err)
		return
	}
	
	fmt.Printf("Deleted event with ID: %s\n", createdEvent.ID)
}
```
