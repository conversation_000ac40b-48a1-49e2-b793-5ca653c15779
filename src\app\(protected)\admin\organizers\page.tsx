'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  Users, Search, Calendar, DollarSign, Ticket,
  ArrowUpDown, Eye, CreditCard, BarChart, UserCheck
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

// Interfaces
interface Organizer {
  id: string;
  name: string;
  email: string;
  phone: string;
  isVerified: boolean;
  accountBalance: number;
  totalEvents: number;
  totalSales: number;
  totalTicketsSold: number;
  joinedDate: string;
  events: Event[];
}

interface Event {
  id: string;
  title: string;
  date: string;
  status: string;
  ticketsSold: number;
  revenue: number;
}

export default function AdminOrganizersPage() {
  const router = useRouter();
  const role = useCurrentRole();
  const [organizers, setOrganizers] = useState<Organizer[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedOrganizer, setSelectedOrganizer] = useState<Organizer | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isUpdateBalanceDialogOpen, setIsUpdateBalanceDialogOpen] = useState(false);
  const [balanceAmount, setBalanceAmount] = useState('');
  const [balanceAction, setBalanceAction] = useState<'add' | 'subtract'>('add');

  // Fetch organizers
  useEffect(() => {
    const fetchOrganizers = async () => {
      setLoading(true);
      try {
        // Fetch real data from the API
        const searchParam = searchTerm ? `?search=${encodeURIComponent(searchTerm)}` : '';
        const response = await fetch(`/api/admin/organizers${searchParam}`);

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.organizers) {
          setOrganizers(data.organizers);
        } else {
          setOrganizers([]);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching organizers:', error);
        setLoading(false);
      }
    };

    fetchOrganizers();
  }, [searchTerm]);

  /* Mock data for reference
  const mockOrganizers: Organizer[] = [
          {
            id: '1',
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '(*************',
            isVerified: true,
            accountBalance: 1250.75,
            totalEvents: 5,
            totalSales: 12500,
            totalTicketsSold: 250,
            joinedDate: '2023-01-15T10:30:00Z',
            events: [
              {
                id: '101',
                title: 'Summer Music Festival',
                date: '2023-07-15T18:00:00Z',
                status: 'Completed',
                ticketsSold: 150,
                revenue: 7500
              },
              {
                id: '102',
                title: 'Tech Conference 2023',
                date: '2023-08-10T09:00:00Z',
                status: 'Upcoming',
                ticketsSold: 100,
                revenue: 5000
              }
            ]
          },
          // More mock data...
        ];
  */

  // Handle balance update
  const handleUpdateBalance = async () => {
    if (!selectedOrganizer) return;

    const amount = parseFloat(balanceAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: 'Invalid amount',
        description: 'Please enter a valid positive number',
        variant: 'destructive',
      });
      return;
    }

    try {
      // Call the API to update the balance
      const response = await fetch(`/api/admin/organizers/${selectedOrganizer.id}/balance`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount,
          action: balanceAction,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to update balance: ${response.status}`);
      }

      const data = await response.json();

      // Update the organizer in the local state
      setOrganizers(prev =>
        prev.map(org =>
          org.id === selectedOrganizer.id
            ? { ...org, accountBalance: data.organizer.accountBalance }
            : org
        )
      );

      // Update the selected organizer
      setSelectedOrganizer({
        ...selectedOrganizer,
        accountBalance: data.organizer.accountBalance
      });

      toast({
        title: 'Balance updated',
        description: `Successfully ${balanceAction === 'add' ? 'added' : 'subtracted'} $${amount.toFixed(2)} ${balanceAction === 'add' ? 'to' : 'from'} ${selectedOrganizer.name}'s account`,
      });

      // Close dialog and reset form
      setIsUpdateBalanceDialogOpen(false);
      setBalanceAmount('');
    } catch (error) {
      console.error('Error updating balance:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update balance. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Format date for display
  const formatDateDisplay = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  // View organizer details
  const viewOrganizerDetails = (organizer: Organizer) => {
    setSelectedOrganizer(organizer);
    setIsDialogOpen(true);
  };

  // Open update balance dialog
  const openUpdateBalanceDialog = (organizer: Organizer) => {
    setSelectedOrganizer(organizer);
    setBalanceAmount('');
    setBalanceAction('add');
    setIsUpdateBalanceDialogOpen(true);
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Manage Organizers</h1>
          <p className="text-gray-500 mt-1">
            View organizer details, events, sales, and manage account balances
          </p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
            <Input
              placeholder="Search organizers by name or email..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Organizers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Organizers</CardTitle>
          <CardDescription>
            {organizers.length} organizers found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : organizers.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">No organizers found</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Try adjusting your search or check back later.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => {/* Add sorting logic */}}
                      >
                        Name
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Contact</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Events</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Sales</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Balance</th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {organizers.map((organizer) => (
                    <tr key={organizer.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-4 text-sm font-medium text-gray-900">{organizer.name}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">
                        <div>{organizer.email}</div>
                        <div>{organizer.phone}</div>
                      </td>
                      <td className="px-4 py-4 text-sm">
                        <Badge className={organizer.isVerified
                          ? "bg-green-100 text-green-800 hover:bg-green-200"
                          : "bg-amber-100 text-amber-800 hover:bg-amber-200"}>
                          {organizer.isVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1 text-blue-500" />
                          {organizer.totalEvents}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <Ticket className="h-4 w-4 mr-1 text-purple-500" />
                          {organizer.totalTicketsSold} tickets
                        </div>
                        <div className="flex items-center mt-1">
                          <DollarSign className="h-4 w-4 mr-1 text-green-500" />
                          ${organizer.totalSales.toLocaleString()}
                        </div>
                      </td>
                      <td className="px-4 py-4 text-sm font-medium">
                        ${organizer.accountBalance.toFixed(2)}
                      </td>
                      <td className="px-4 py-4 text-sm text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => viewOrganizerDetails(organizer)}
                            className="h-8 w-8 p-0"
                          >
                            <span className="sr-only">View</span>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openUpdateBalanceDialog(organizer)}
                            className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                          >
                            <span className="sr-only">Update Balance</span>
                            <CreditCard className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Organizer Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedOrganizer && (
            <>
              <DialogHeader>
                <DialogTitle>Organizer Details</DialogTitle>
                <DialogDescription>
                  Detailed information about {selectedOrganizer.name}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-6 py-4">
                {/* Organizer Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-2">{selectedOrganizer.name}</h3>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Email:</span>
                        <span>{selectedOrganizer.email}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Phone:</span>
                        <span>{selectedOrganizer.phone}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Joined:</span>
                        <span>{formatDateDisplay(selectedOrganizer.joinedDate)}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Status:</span>
                        <Badge className={selectedOrganizer.isVerified
                          ? "bg-green-100 text-green-800 hover:bg-green-200"
                          : "bg-amber-100 text-amber-800 hover:bg-amber-200"}>
                          {selectedOrganizer.isVerified ? 'Verified' : 'Unverified'}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold mb-2">Financial Summary</h3>
                    <div className="space-y-2">
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Balance:</span>
                        <span className="font-medium">${selectedOrganizer.accountBalance.toFixed(2)}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Total Sales:</span>
                        <span>${selectedOrganizer.totalSales.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Tickets Sold:</span>
                        <span>{selectedOrganizer.totalTicketsSold} tickets</span>
                      </div>
                      <div className="flex items-center text-sm">
                        <span className="text-gray-500 w-24">Total Events:</span>
                        <span>{selectedOrganizer.totalEvents} events</span>
                      </div>
                    </div>
                    <div className="mt-4">
                      <Button
                        size="sm"
                        onClick={() => {
                          setIsDialogOpen(false);
                          openUpdateBalanceDialog(selectedOrganizer);
                        }}
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Update Balance
                      </Button>
                    </div>
                  </div>
                </div>

                {/* Events Tab */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-4">Events</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b">
                          <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Event</th>
                          <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Date</th>
                          <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Status</th>
                          <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Tickets Sold</th>
                          <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Revenue</th>
                          <th className="px-4 py-2 text-right text-sm font-medium text-gray-500">Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {selectedOrganizer.events && selectedOrganizer.events.length > 0 ? selectedOrganizer.events.map((event) => (
                          <tr key={event.id} className="border-b hover:bg-gray-50">
                            <td className="px-4 py-3 text-sm font-medium text-gray-900">{event.title}</td>
                            <td className="px-4 py-3 text-sm text-gray-500">{formatDateDisplay(event.date)}</td>
                            <td className="px-4 py-3 text-sm">
                              <Badge className={event.status === 'Completed'
                                ? "bg-green-100 text-green-800 hover:bg-green-200"
                                : "bg-blue-100 text-blue-800 hover:bg-blue-200"}>
                                {event.status}
                              </Badge>
                            </td>
                            <td className="px-4 py-3 text-sm text-gray-500">{event.ticketsSold}</td>
                            <td className="px-4 py-3 text-sm text-gray-500">${event.revenue.toLocaleString()}</td>
                            <td className="px-4 py-3 text-sm text-right">
                              <Button
                                variant="ghost"
                                size="sm"
                                asChild
                                className="h-8 px-2 py-0"
                              >
                                <Link href={`/admin/events/${event.id}`}>
                                  View Details
                                </Link>
                              </Button>
                            </td>
                          </tr>
                        )) : (
                          <tr>
                            <td colSpan={6} className="px-4 py-3 text-sm text-center text-gray-500">
                              No events found for this organizer.
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Update Balance Dialog */}
      <Dialog open={isUpdateBalanceDialogOpen} onOpenChange={setIsUpdateBalanceDialogOpen}>
        <DialogContent className="max-w-md">
          {selectedOrganizer && (
            <>
              <DialogHeader>
                <DialogTitle>Update Account Balance</DialogTitle>
                <DialogDescription>
                  Update the account balance for {selectedOrganizer.name}
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Current Balance:</span>
                  <span className="font-medium">${selectedOrganizer.accountBalance.toFixed(2)}</span>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">Action</label>
                  <div className="flex space-x-2">
                    <Button
                      variant={balanceAction === 'add' ? 'default' : 'outline'}
                      onClick={() => setBalanceAction('add')}
                      className="flex-1"
                    >
                      Add Funds
                    </Button>
                    <Button
                      variant={balanceAction === 'subtract' ? 'default' : 'outline'}
                      onClick={() => setBalanceAction('subtract')}
                      className="flex-1"
                    >
                      Subtract Funds
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="amount" className="text-sm font-medium">Amount</label>
                  <div className="relative">
                    <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                    <Input
                      id="amount"
                      type="number"
                      min="0.01"
                      step="0.01"
                      placeholder="0.00"
                      className="pl-8"
                      value={balanceAmount}
                      onChange={(e) => setBalanceAmount(e.target.value)}
                    />
                  </div>
                </div>

                <div className="pt-2">
                  <p className="text-sm text-gray-500">
                    {balanceAction === 'add'
                      ? 'This will increase the organizer\'s account balance.'
                      : 'This will decrease the organizer\'s account balance.'}
                  </p>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsUpdateBalanceDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleUpdateBalance}>
                  Update Balance
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
