import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import UserPartnersClient from '@/components/user/partners/partners-client';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
  };
};

export default async function UserPartnersPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only users can access this page
  if (session.user.role !== 'USER') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <Suspense fallback={<div className="animate-pulse">Loading partners...</div>}>
        <UserPartnersClient />
      </Suspense>
    </div>
  );
}
