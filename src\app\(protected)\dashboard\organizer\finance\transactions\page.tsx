'use client';

import React from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Download } from 'lucide-react';
import Link from 'next/link';
import { TransactionsList } from '@/components/finance/transactions-list';

export default function OrganizerTransactionsPage() {
  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <Button variant="ghost" asChild className="mb-4 -ml-4">
              <Link href="/dashboard/organizer/finance">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Finance Overview
              </Link>
            </Button>
            <h1 className="text-3xl font-bold mb-2">Transactions</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View and manage all your financial transactions
            </p>
          </div>
          <Button variant="outline" className="mt-4 md:mt-0">
            <Download className="mr-2 h-4 w-4" />
            Export Transactions
          </Button>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Transactions</CardTitle>
            <CardDescription>Complete transaction history for all your events</CardDescription>
          </CardHeader>
          <CardContent>
            <TransactionsList />
          </CardContent>
        </Card>
      </div>
    </RoleGate>
  );
}
