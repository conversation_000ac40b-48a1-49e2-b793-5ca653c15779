# NFC Vendor Payment System Documentation

## Overview

The NFC Vendor Payment System enables vendors at events to process contactless payments using NFC (Near Field Communication) technology. This system allows vendors to accept payments from attendees who have NFC cards or tags associated with their accounts, streamlining the payment process at events.

## System Architecture

### Components

1. **NFCCardReader Component**
   - Handles the detection and reading of NFC cards
   - Provides both automatic scanning and manual entry options
   - Located at: `src/components/vendor/nfc/NFCCardReader.tsx`

2. **NFC Terminal Page**
   - Main interface for vendors to process payments
   - Integrates product selection, custom amounts, and transaction processing
   - Located at: `src/app/(protected)/dashboard/vendor/nfc-terminal/[eventId]/page.tsx`

3. **Transaction History Component**
   - Displays recent transactions for a vendor at a specific event
   - Shows transaction details including products, amounts, and customer information
   - Located at: `src/components/vendor/nfc/TransactionHistory.tsx`

4. **NFC Utility Functions**
   - Helper functions for NFC card validation and formatting
   - Located at: `src/lib/nfc-utils.ts`

5. **API Routes**
   - `/api/vendors/nfc/transaction` - Processes NFC payment transactions
   - `/api/vendors/nfc/transactions/history` - Retrieves transaction history

### Database Schema

The system relies on the following database tables:

- `VendorProfile` - Stores vendor information
- `NFCCard` - Stores NFC card information linked to users
- `VendorNFCTransaction` - Records all NFC payment transactions
- `EventVendor` - Links vendors to events they're participating in

## How to Use

### For Vendors

1. **Access the NFC Terminal**
   - Navigate to the Vendor Dashboard
   - Select an event you're approved for
   - Click on "NFC Terminal" to open the payment interface

2. **Process a Payment**
   - Scan the customer's NFC card using the card reader
   - Add products to the cart or enter a custom amount
   - Click "Process Payment" to complete the transaction

3. **View Transaction History**
   - Transaction history is displayed on the right side of the terminal
   - Shows recent transactions with details and totals

### For Developers

#### Adding New Features

To extend the NFC payment system:

1. **Add New Card Types**
   - Update the `validateNfcCardId` function in `nfc-utils.ts` to support new card formats

2. **Enhance Transaction Processing**
   - Modify the transaction API route to add additional validation or processing steps

3. **Implement Real NFC Reading**
   - Replace the simulation code in `NFCCardReader.tsx` with actual Web NFC API implementation

## Security Considerations

- All NFC transactions are authenticated and require vendor approval at approved events
- Card IDs are validated before processing payments
- Transactions are recorded with detailed audit information
- User authentication is required for all vendor operations

## Limitations and Future Improvements

- Currently uses simulated NFC reading; should be replaced with actual Web NFC API when available
- Add support for offline transactions with synchronization
- Implement transaction receipt generation and sharing
- Add support for refunds and transaction adjustments
- Enhance analytics for vendor sales performance

## Troubleshooting

### Common Issues

1. **Card Not Detected**
   - Ensure the device supports NFC reading
   - Try manual entry if automatic scanning fails

2. **Transaction Failed**
   - Verify the customer has sufficient balance
   - Check vendor participation status for the event
   - Ensure network connectivity is stable

3. **Missing Transaction History**
   - Refresh the page to reload transaction data
   - Verify the vendor is properly approved for the event

## API Reference

### Process NFC Transaction

**Endpoint:** `POST /api/vendors/nfc/transaction`

**Request Body:**
```json
{
  "eventId": "string",
  "vendorId": "string",
  "cardUid": "string",
  "amount": "number",
  "products": [
    {
      "productId": "string",
      "name": "string",
      "price": "number",
      "quantity": "number"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "transactionId": "string",
  "amount": "number",
  "timestamp": "string"
}
```

### Get Transaction History

**Endpoint:** `GET /api/vendors/nfc/transactions/history?eventId={eventId}&limit={limit}&offset={offset}`

**Response:**
```json
{
  "transactions": [
    {
      "id": "string",
      "amount": "number",
      "createdAt": "string",
      "status": "string",
      "cardId": "string",
      "customerName": "string",
      "customerEmail": "string",
      "products": []
    }
  ],
  "pagination": {
    "total": "number",
    "limit": "number",
    "offset": "number",
    "hasMore": "boolean"
  },
  "summary": {
    "totalAmount": "number",
    "transactionCount": "number"
  }
}
```