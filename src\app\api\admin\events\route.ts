import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const category = searchParams.get('category') || '';
    const type = searchParams.get('type') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;
    const organizerId = searchParams.get('organizerId') || '';

    // Build the where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } },
        { venue: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (status) {
      where.status = status;
    }

    if (category) {
      where.category = category;
    }

    if (type) {
      where.eventType = type;
    }

    if (organizerId) {
      where.userId = organizerId;
    }

    console.log('Fetching events with where clause:', JSON.stringify(where));

    // Find all events with pagination
    const events = await db.event.findMany({
      where,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            emailVerified: true
          }
        },
        _count: {
          select: {
            orders: true,
            tickets: true
          }
        }
      },
      orderBy: {
        [sortBy]: sortOrder
      },
      skip,
      take: limit
    });

    // Get total count for pagination
    const totalCount = await db.event.count({ where });

    // Calculate additional metrics for each event
    const eventsWithMetrics = await Promise.all(
      events.map(async (event) => {
        // Get sales data
        const salesData = await db.order.aggregate({
          where: {
            eventId: event.id,
            status: 'Completed'
          },
          _sum: {
            totalPrice: true
          },
          _count: {
            id: true
          }
        });

        // Get ticket data
        const ticketData = await db.ticket.aggregate({
          where: {
            eventId: event.id
          },
          _sum: {
            quantity: true
          }
        });

        // Get attendee count
        const attendeeCount = await db.order.count({
          where: {
            eventId: event.id
          }
        });

        // Get engagement data
        const engagement = await db.engagement.findFirst({
          where: {
            eventId: event.id
          }
        }) || { views: 0, clicks: 0, shares: 0, likes: 0 };

        return {
          id: event.id,
          title: event.title,
          description: event.description || '',
          startDate: event.startDate.toISOString(),
          endDate: event.endDate.toISOString(),
          startTime: event.startTime,
          endTime: event.endTime,
          location: event.location,
          venue: event.venue,
          category: event.category,
          status: event.status,
          eventType: event.eventType,
          createdAt: event.createdAt.toISOString(),
          updatedAt: event.updatedAt.toISOString(),
          imagePath: event.imagePath || null,
          organizer: {
            id: event.user.id,
            name: event.user.name || 'Unknown',
            email: event.user.email || '',
            isVerified: event.user.emailVerified !== null
          },
          stats: {
            totalTicketsSold: ticketData._sum.quantity || 0,
            totalRevenue: salesData._sum.totalPrice || 0,
            totalOrders: salesData._count.id || 0,
            totalAttendees: attendeeCount,
            engagement: {
              views: engagement.views,
              clicks: engagement.clicks,
              shares: engagement.shares,
              likes: engagement.likes
            }
          }
        };
      })
    );

    // Get event categories for filters
    const categoriesResult = await db.$queryRaw`SELECT DISTINCT category FROM "Event"`;
    const categories = Array.isArray(categoriesResult) ? categoriesResult.map(row => row.category) : [];

    // Get event statuses for filters
    const statusesResult = await db.$queryRaw`SELECT DISTINCT status FROM "Event"`;
    const statuses = Array.isArray(statusesResult) ? statusesResult.map(row => row.status) : [];

    // Get event types for filters
    const eventTypesResult = await db.$queryRaw`SELECT DISTINCT "eventType" FROM "Event"`;
    const eventTypes = Array.isArray(eventTypesResult) ? eventTypesResult.map(row => row.eventType) : [];

    return NextResponse.json({
      events: eventsWithMetrics,
      pagination: {
        total: totalCount,
        page,
        limit,
        totalPages: Math.ceil(totalCount / limit)
      },
      filters: {
        categories,
        statuses,
        eventTypes
      }
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}
