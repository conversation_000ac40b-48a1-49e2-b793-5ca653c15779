import { currentUser } from '@/lib/auth';
import { db } from '@/lib/prisma';
import { redirect } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Star, Calendar, DollarSign, TrendingUp } from 'lucide-react';
import FeatureVendorButton from '@/components/vendor/feature-vendor-button';

export default async function VendorFeaturingPage() {
  const user = await currentUser();

  if (!user) {
    redirect('/auth/login');
  }

  if (user.role !== 'VENDOR') {
    redirect('/dashboard');
  }

  // Get vendor profile
  const vendorProfile = await db.vendorProfile.findUnique({
    where: { userId: user.id },
    include: {
      vendorFeaturings: {
        orderBy: { createdAt: 'desc' },
        take: 5,
      },
    },
  });

  if (!vendorProfile) {
    redirect('/dashboard/vendor/setup');
  }

  // Get current active featuring
  const now = new Date();
  const activeFeaturing = vendorProfile.vendorFeaturings.find(
    f => f.status === 'ACTIVE' && f.endDate > now
  );

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Business Featuring</h1>
          <p className="text-muted-foreground">
            Boost your visibility and attract more customers by featuring your business on the homepage.
          </p>
        </div>
        <FeatureVendorButton
          vendorId={vendorProfile.id}
          businessName={vendorProfile.businessName}
          size="lg"
        />
      </div>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5" />
            Current Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          {activeFeaturing ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-semibold text-lg">Currently Featured</h3>
                  <p className="text-muted-foreground">
                    Your business is featured with {activeFeaturing.tier.toLowerCase()} tier
                  </p>
                </div>
                <Badge className={`${
                  activeFeaturing.tier === 'ELITE' ? 'bg-yellow-500' :
                  activeFeaturing.tier === 'PREMIUM' ? 'bg-purple-500' : 'bg-blue-500'
                }`}>
                  {activeFeaturing.tier}
                </Badge>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Started</p>
                    <p className="font-medium">{activeFeaturing.startDate.toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Expires</p>
                    <p className="font-medium">{activeFeaturing.endDate.toLocaleDateString()}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm text-muted-foreground">Amount Paid</p>
                    <p className="font-medium">${activeFeaturing.paymentAmount.toString()}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="font-semibold text-lg mb-2">Not Currently Featured</h3>
              <p className="text-muted-foreground mb-4">
                Feature your business to increase visibility and attract more customers.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Benefits */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Featuring Benefits
          </CardTitle>
          <CardDescription>
            See what you get when you feature your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Star className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-2">Basic Featuring</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Featured on homepage</li>
                <li>• Basic placement</li>
                <li>• Standard design</li>
              </ul>
              <p className="font-bold text-lg mt-2">$19.99/week</p>
            </div>
            <div className="text-center">
              <div className="bg-purple-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Star className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold mb-2">Premium Featuring</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Priority placement</li>
                <li>• Highlighted design</li>
                <li>• Newsletter inclusion</li>
              </ul>
              <p className="font-bold text-lg mt-2">$49.99/week</p>
            </div>
            <div className="text-center">
              <div className="bg-yellow-100 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                <Star className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="font-semibold mb-2">Elite Featuring</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Top placement</li>
                <li>• Premium design</li>
                <li>• Social media promotion</li>
              </ul>
              <p className="font-bold text-lg mt-2">$99.99/week</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Featuring History */}
      {vendorProfile.vendorFeaturings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Featuring History</CardTitle>
            <CardDescription>
              Your past featuring campaigns
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {vendorProfile.vendorFeaturings.map((featuring) => (
                <div key={featuring.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-4">
                    <Badge className={`${
                      featuring.tier === 'ELITE' ? 'bg-yellow-500' :
                      featuring.tier === 'PREMIUM' ? 'bg-purple-500' : 'bg-blue-500'
                    }`}>
                      {featuring.tier}
                    </Badge>
                    <div>
                      <p className="font-medium">
                        {featuring.startDate.toLocaleDateString()} - {featuring.endDate.toLocaleDateString()}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        Status: {featuring.status}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-medium">${featuring.paymentAmount.toString()}</p>
                    <p className="text-sm text-muted-foreground">
                      {Math.ceil((featuring.endDate.getTime() - featuring.startDate.getTime()) / (1000 * 60 * 60 * 24))} days
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
