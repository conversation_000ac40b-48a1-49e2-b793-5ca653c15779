'use server';

import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { validatePhoneNumber } from '@/lib/validation/phone-validation';
import fs from 'fs/promises';
import crypto from 'crypto';
import { revalidatePath } from 'next/cache';
import { sendVerificationSubmissionEmail, sendAdminVerificationNotification } from '@/lib/mail';

export async function submitOrganizerVerification(formData: FormData) {
  try {
    // Get current user
    const user = await currentUser();
    if (!user?.id) {
      return { error: 'Unauthorized. Please log in.' };
    }

    // Check if user is already verified or has a pending verification
    const existingVerification = await db.organizerVerification.findUnique({
      where: { userId: user.id },
    });

    if (existingVerification) {
      if (existingVerification.status === 'APPROVED') {
        return { error: 'Your organizer account is already verified.' };
      }
      if (existingVerification.status === 'PENDING') {
        return { error: 'You already have a pending verification request.' };
      }
    }

    // Extract form data
    const businessName = formData.get('businessName') as string;
    const businessType = formData.get('businessType') as string;
    const registrationNumber = formData.get('registrationNumber') as string;
    const taxPayerIdNumber = formData.get('taxPayerIdNumber') as string;
    const phoneNumber = formData.get('phoneNumber') as string;
    const alternativeEmail = formData.get('alternativeEmail') as string;
    const website = formData.get('website') as string;
    const physicalAddress = formData.get('physicalAddress') as string;
    const city = formData.get('city') as string;
    const province = formData.get('province') as string;
    const postalCode = formData.get('postalCode') as string;
    const eventTypes = formData.get('eventTypes') as string;
    const experience = formData.get('experience') as string;
    const previousEvents = formData.get('previousEvents') as string;
    const idDocumentType = formData.get('idDocumentType') as string;

    // Basic validation
    if (!businessName || !businessType || !taxPayerIdNumber || !phoneNumber ||
        !physicalAddress || !city || !province || !eventTypes || !experience) {
      return { error: 'Please fill in all required fields.' };
    }

    // Validate phone number
    const phoneValidation = validatePhoneNumber(phoneNumber);
    if (!phoneValidation.isValid) {
      return { error: phoneValidation.error || 'Invalid phone number. Please include country code (e.g. +1, +44, +260)' };
    }

    // Handle file uploads
    const idDocument = formData.get('idDocument') as File;
    const businessLicense = formData.get('businessLicense') as File;
    const taxCertificate = formData.get('taxCertificate') as File;

    if (!idDocument) {
      return { error: 'ID document is required.' };
    }

    if (!taxCertificate) {
      return { error: 'Tax certificate is required.' };
    }

    // Process file uploads
    let idDocumentPath: string | undefined;
    let businessLicensePath: string | undefined;
    let taxCertificatePath: string | undefined;

    // Ensure directory exists
    try {
      await fs.mkdir('./public/uploads/verification', { recursive: true });
    } catch (error) {
      console.error('Error creating directory:', error);
      // Continue with the process even if directory creation fails
    }

    // Process ID document
    if (idDocument && idDocument.size > 0) {
      try {
        const bytes = await idDocument.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${idDocument.name}`;
        idDocumentPath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${idDocumentPath}`, buffer);
      } catch (error) {
        console.error('Error processing ID document:', error);
        idDocumentPath = undefined;
      }
    }

    // Process business license
    if (businessLicense && businessLicense.size > 0) {
      try {
        const bytes = await businessLicense.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${businessLicense.name}`;
        businessLicensePath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${businessLicensePath}`, buffer);
      } catch (error) {
        console.error('Error processing business license:', error);
        businessLicensePath = undefined;
      }
    }

    // Process tax certificate
    if (taxCertificate && taxCertificate.size > 0) {
      try {
        const bytes = await taxCertificate.arrayBuffer();
        const buffer = Buffer.from(bytes);
        const filename = `${crypto.randomUUID()}-${taxCertificate.name}`;
        taxCertificatePath = `/uploads/verification/${filename}`;
        await fs.writeFile(`./public${taxCertificatePath}`, buffer);
      } catch (error) {
        console.error('Error processing tax certificate:', error);
        taxCertificatePath = undefined;
      }
    }

    // Create or update verification record
    if (existingVerification) {
      // Update existing record if it was rejected before
      await db.organizerVerification.update({
        where: { id: existingVerification.id },
        data: {
          businessName,
          businessType,
          registrationNumber,
          taxPayerIdNumber,
          phoneNumber,
          alternativeEmail,
          website,
          physicalAddress,
          city,
          province,
          postalCode,
          idDocumentPath,
          idDocumentType,
          businessLicensePath,
          taxCertificatePath,
          eventTypes,
          experience,
          previousEvents,
          status: 'PENDING',
          rejectionReason: null,
          updatedAt: new Date(),
        },
      });
    } else {
      // Create new verification record
      await db.organizerVerification.create({
        data: {
          userId: user.id!,
          businessName,
          businessType,
          registrationNumber,
          taxPayerIdNumber,
          phoneNumber,
          alternativeEmail,
          website,
          physicalAddress,
          city,
          province,
          postalCode,
          idDocumentPath,
          idDocumentType,
          businessLicensePath,
          taxCertificatePath,
          eventTypes,
          experience,
          previousEvents,
          status: 'PENDING',
        },
      });
    }

    // Send confirmation email to user
    await sendVerificationSubmissionEmail(user.email as string, businessName);

    // Send notification to admin
    await sendAdminVerificationNotification(businessName, user.email as string);

    // Revalidate the path to update UI
    revalidatePath('/dashboard/organizer/profile');

    return { success: 'Verification submitted successfully!' };
  } catch (error) {
    console.error('Error submitting organizer verification:', error);

    // Check for specific Prisma errors
    if (error && typeof error === 'object' && 'code' in error) {
      const prismaError = error as { code: string, meta?: { field_name?: string } };

      // Foreign key constraint violation
      if (prismaError.code === 'P2003' && prismaError.meta?.field_name?.includes('userId')) {
        return {
          error: 'User account not properly configured. Please contact support.'
        };
      }
    }

    return { error: 'An unexpected error occurred. Please try again.' };
  }
}

export async function getOrganizerVerificationStatus() {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return { status: 'UNAUTHENTICATED' };
    }

    const verification = await db.organizerVerification.findUnique({
      where: { userId: user.id },
    });

    if (!verification) {
      return { status: 'NOT_SUBMITTED' };
    }

    return {
      status: verification.status,
      rejectionReason: verification.rejectionReason,
      submittedAt: verification.createdAt,
      verifiedAt: verification.verifiedAt
    };
  } catch (error) {
    console.error('Error getting verification status:', error);
    return { status: 'ERROR', error: 'Failed to fetch verification status' };
  }
}
