import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * POST /api/admin/setup-featured
 * Setup some featured content for testing (Admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Feature some vendors
    const featuredVendors = await db.vendorProfile.updateMany({
      where: {
        verificationStatus: 'APPROVED',
        featured: false,
      },
      data: {
        featured: true,
      },
      // Take first 6 approved vendors
    });

    // Feature some partners
    const featuredPartners = await db.partner.updateMany({
      where: {
        isVerified: true,
        featured: false,
      },
      data: {
        featured: true,
      },
      // Take first 6 verified partners
    });

    // Feature some events by creating EventFeaturing records
    const eventsToFeature = await db.event.findMany({
      where: {
        status: 'Published',
        endDate: { gte: new Date() },
        featuring: {
          none: {
            status: 'ACTIVE',
            endDate: {
              gte: new Date(),
            },
          },
        },
      },
      take: 6, // Feature first 6 eligible events
      select: { id: true },
    });

    // Create featuring records for selected events
    let featuredEventsCount = 0;
    for (const event of eventsToFeature) {
      await db.eventFeaturing.create({
        data: {
          eventId: event.id,
          tier: 'BASIC',
          startDate: new Date(),
          endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
          status: 'ACTIVE',
          paymentAmount: 0,
          metadata: {
            setupFeatured: true,
          },
        },
      });
      featuredEventsCount++;
    }

    // Get counts
    const [vendorCount, partnerCount, eventCount] = await Promise.all([
      db.vendorProfile.count({ where: { featured: true } }),
      db.partner.count({ where: { featured: true } }),
      db.event.count({
        where: {
          featuring: {
            some: {
              status: 'ACTIVE',
              endDate: {
                gte: new Date(),
              },
            },
          },
        },
      }),
    ]);

    return NextResponse.json({
      message: 'Featured content setup successfully',
      results: {
        featuredVendors: vendorCount,
        featuredPartners: partnerCount,
        featuredEvents: eventCount,
      },
      updates: {
        vendorsUpdated: featuredVendors.count,
        partnersUpdated: featuredPartners.count,
        eventsUpdated: featuredEventsCount,
      },
    });
  } catch (error) {
    console.error('Error setting up featured content:', error);
    return NextResponse.json(
      { error: 'Failed to setup featured content' },
      { status: 500 }
    );
  }
}
