import { redirect } from 'next/navigation';
import Link from 'next/link';
import { getSession } from '@/auth';
import { db } from '@/lib/prisma';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ChevronLeft, DollarSign, User, Calendar, Clock, FileText } from 'lucide-react';
import { formatCurrency } from '@/lib/utils/format';
import { format } from 'date-fns';
import { UserSession } from '@/types/session';
import { AdminPayoutActions } from '@/components/admin/payout-actions';

interface PageProps {
  params: Promise<{ id: string }>;
}

// This tells Next.js to not pre-render this page at build time
export const dynamic = 'force-dynamic';

// Return empty array to prevent static generation
export async function generateStaticParams() {
  return [];
}

export default async function AdminPayoutDetailsPage({ params }: PageProps) {
  const session = await getSession() as UserSession;
  const resolvedParams = await params;
  const payoutId = resolvedParams.id;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only admins can access this page
  if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN') {
    redirect('/dashboard');
  }

  try {
    // Get the payout details
    const payout = await db.eventPayout.findUnique({
      where: { id: payoutId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            accountBalance: true,
            bankAccounts: {
              where: { isDefault: true },
              take: 1,
            },
          },
        },
        event: {
          select: {
            id: true,
            title: true,
            endDate: true,
            status: true,
          },
        },
        withdrawals: {
          include: {
            bankAccount: true,
          },
          orderBy: { requestDate: 'desc' },
        },
      },
    });

    if (!payout) {
      redirect('/admin/finance/payouts');
    }

    // Get ticket sales data for the event
    const ticketSales = await db.ticketSales.findMany({
      where: { eventId: payout.eventId },
      select: {
        type: true,
        sold: true,
        revenue: true,
        price: true,
      },
    });

    const getStatusBadgeVariant = (status: string) => {
      switch (status) {
        case 'Pending':
          return 'outline';
        case 'Processing':
          return 'default';
        case 'Completed':
          return 'success';
        case 'Failed':
          return 'destructive';
        case 'Cancelled':
          return 'destructive';
        default:
          return 'outline';
      }
    };

    return (
      <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/admin/finance/payouts">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Payouts
          </Link>
        </Button>
      </div>

      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Payout Details</h1>
          <p className="text-gray-500 mt-1">
            Manage payout for {payout.event.title}
          </p>
        </div>
        <Badge variant={getStatusBadgeVariant(payout.status)} className="text-base py-1 px-3">
          {payout.status}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payout Information</CardTitle>
              <CardDescription>
                Details about this payout request
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-gray-500">Amount</p>
                  <p className="text-xl font-bold">{formatCurrency(payout.amount)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-gray-500">Request Date</p>
                  <p className="text-lg">{format(new Date(payout.requestDate), 'PPP')}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium">Event Details</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-1">
                    <p className="text-sm text-gray-500">Event</p>
                    <p className="text-base">
                      <Link href={`/admin/events/${payout.eventId}`} className="text-blue-600 hover:underline">
                        {payout.event.title}
                      </Link>
                    </p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-500">Event Status</p>
                    <p className="text-base">{payout.event.status}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-500">End Date</p>
                    <p className="text-base">{format(new Date(payout.event.endDate), 'PPP')}</p>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm text-gray-500">Total Ticket Sales</p>
                    <p className="text-base">{payout.totalTicketSales} tickets</p>
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="font-medium">Financial Details</h3>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Total Revenue</span>
                    <span className="font-medium">{formatCurrency(payout.totalRevenue)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Platform Commission ({(payout.commissionRate * 100).toFixed(0)}%)</span>
                    <span className="font-medium text-red-600">-{formatCurrency(payout.commissionAmount)}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Organizer Payout</span>
                    <span className="font-bold text-green-600">{formatCurrency(payout.amount)}</span>
                  </div>
                </div>
              </div>

              {payout.notes && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-medium">Organizer Notes</h3>
                    <p className="text-sm bg-gray-50 p-3 rounded">{payout.notes}</p>
                  </div>
                </>
              )}

              {payout.adminNotes && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-medium">Admin Notes</h3>
                    <p className="text-sm bg-gray-50 p-3 rounded whitespace-pre-line">{payout.adminNotes}</p>
                  </div>
                </>
              )}

              {(payout.reference || payout.transactionId) && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="font-medium">Transaction Details</h3>
                    {payout.reference && (
                      <div className="space-y-1">
                        <p className="text-sm text-gray-500">Reference</p>
                        <p className="text-base font-mono">{payout.reference}</p>
                      </div>
                    )}
                    {payout.transactionId && (
                      <div className="space-y-1">
                        <p className="text-sm text-gray-500">Transaction ID</p>
                        <p className="text-base font-mono">{payout.transactionId}</p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter className="flex justify-end space-x-2">
              <AdminPayoutActions
                payoutId={payout.id}
                currentStatus={payout.status}
              />
            </CardFooter>
          </Card>

          {payout.withdrawals.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Related Withdrawals</CardTitle>
                <CardDescription>
                  Withdrawals associated with this payout
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {payout.withdrawals.map((withdrawal) => (
                    <div key={withdrawal.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-center mb-2">
                        <h3 className="font-medium">Withdrawal #{withdrawal.id.substring(0, 8)}</h3>
                        <Badge variant={getStatusBadgeVariant(withdrawal.status)}>
                          {withdrawal.status}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">Amount</p>
                          <p className="font-medium">{formatCurrency(withdrawal.amount)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Request Date</p>
                          <p>{format(new Date(withdrawal.requestDate), 'PPP')}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Bank</p>
                          <p>{withdrawal.bankAccount.bankName}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">Account</p>
                          <p>{withdrawal.bankAccount.accountName} (****{withdrawal.bankAccount.accountNumber.slice(-4)})</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organizer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="bg-gray-100 p-2 rounded-full">
                  <User className="h-6 w-6 text-gray-600" />
                </div>
                <div>
                  <p className="font-medium">{payout.user.name}</p>
                  <p className="text-sm text-gray-500">{payout.user.email}</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h3 className="text-sm font-medium">Account Balance</h3>
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-primary mr-2" />
                  <span className="text-xl font-bold">{formatCurrency(payout.user.accountBalance)}</span>
                </div>
              </div>

              {payout.user.bankAccounts.length > 0 && (
                <>
                  <Separator />
                  <div className="space-y-2">
                    <h3 className="text-sm font-medium">Default Bank Account</h3>
                    <div className="bg-gray-50 p-3 rounded space-y-1">
                      <p className="font-medium">{payout.user.bankAccounts[0].bankName}</p>
                      <p className="text-sm">{payout.user.bankAccounts[0].accountName}</p>
                      <p className="text-sm">Account: ****{payout.user.bankAccounts[0].accountNumber.slice(-4)}</p>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
            <CardFooter>
              <Button variant="outline" className="w-full" asChild>
                <Link href={`/admin/organizers/${payout.userId}`}>
                  View Organizer Profile
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Ticket Sales</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {ticketSales.map((sale, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <div>
                      <p className="font-medium">{sale.type}</p>
                      <p className="text-sm text-gray-500">{sale.sold} tickets @ {formatCurrency(sale.price)}</p>
                    </div>
                    <p className="font-medium">{formatCurrency(sale.revenue)}</p>
                  </div>
                ))}

                <Separator />

                <div className="flex justify-between items-center font-bold">
                  <p>Total</p>
                  <p>{formatCurrency(payout.totalRevenue)}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Timeline</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex">
                  <div className="mr-3 flex flex-col items-center">
                    <div className="bg-blue-100 p-1.5 rounded-full">
                      <Clock className="h-4 w-4 text-blue-600" />
                    </div>
                    <div className="w-px h-full bg-gray-200 mt-1"></div>
                  </div>
                  <div>
                    <p className="font-medium">Payout Requested</p>
                    <p className="text-sm text-gray-500">{format(new Date(payout.requestDate), 'PPP p')}</p>
                  </div>
                </div>

                {payout.status !== 'Pending' && (
                  <div className="flex">
                    <div className="mr-3 flex flex-col items-center">
                      <div className="bg-blue-100 p-1.5 rounded-full">
                        <Clock className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="w-px h-full bg-gray-200 mt-1"></div>
                    </div>
                    <div>
                      <p className="font-medium">Status Changed to {payout.status}</p>
                      <p className="text-sm text-gray-500">{format(new Date(payout.updatedAt), 'PPP p')}</p>
                    </div>
                  </div>
                )}

                {payout.processedDate && (
                  <div className="flex">
                    <div className="mr-3 flex flex-col items-center">
                      <div className="bg-green-100 p-1.5 rounded-full">
                        <DollarSign className="h-4 w-4 text-green-600" />
                      </div>
                    </div>
                    <div>
                      <p className="font-medium">Payout Processed</p>
                      <p className="text-sm text-gray-500">{format(new Date(payout.processedDate), 'PPP p')}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    );
  } catch (error) {
    console.error('Error loading payout details:', error);
    redirect('/admin/finance/payouts');
  }
}
