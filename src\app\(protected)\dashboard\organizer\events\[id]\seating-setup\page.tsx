'use client';

import React, { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import StadiumSeatingToggle from '@/components/events/StadiumSeatingToggle';

export default function EventSeatingSetupPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const resolvedParams = use(params);
  const eventId = resolvedParams.id;

  const [event, setEvent] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch event details
  useEffect(() => {
    async function fetchEventDetails() {
      try {
        setIsLoading(true);
        console.log('Fetching event details for ID:', eventId);

        // Try to fetch from the events API
        const response = await fetch(`/api/events/${eventId}`);

        if (response.ok) {
          const data = await response.json();
          console.log('Event data received:', data);
          setEvent(data);
        } else {
          // If that fails, try the dashboard organizer API
          console.log('Failed to fetch from events API, trying dashboard API');
          const dashboardResponse = await fetch(`/api/dashboard/organiser/events/${eventId}`);

          if (dashboardResponse.ok) {
            const dashboardData = await dashboardResponse.json();
            console.log('Event data received from dashboard API:', dashboardData);
            setEvent(dashboardData);
          } else {
            console.error('Both API calls failed');
            setError('Failed to fetch event details');
          }
        }
      } catch (error) {
        console.error('Error fetching event details:', error);
        setError('An error occurred while fetching event details');
      } finally {
        setIsLoading(false);
      }
    }

    fetchEventDetails();
  }, [eventId]);

  const handleSeatingToggle = (hasStadiumSeating: boolean) => {
    if (hasStadiumSeating) {
      // If stadium seating was enabled, redirect to the seating management page
      router.push(`/dashboard/organizer/events/${eventId}/seating`);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-6 flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto py-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Event not found or you don't have permission to access it.
          </AlertDescription>
        </Alert>
        <Button
          className="mt-4"
          variant="outline"
          onClick={() => router.push('/dashboard/organizer/events/myEvents')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to My Events
        </Button>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="outline"
          size="sm"
          className="mr-4"
          onClick={() => router.push('/dashboard/organizer/events/myEvents')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold">{event.title}</h1>
          <p className="text-gray-500">{event.venue}</p>
        </div>
      </div>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="grid gap-6">
        <StadiumSeatingToggle
          eventId={eventId}
          initialValue={event.hasStadiumSeating || false}
          onToggle={handleSeatingToggle}
        />

        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h2 className="text-xl font-semibold mb-4">About Stadium Seating</h2>
          <div className="space-y-4">
            <p>
              When stadium seating is enabled, attendees will go through the following steps during checkout:
            </p>

            <ol className="list-decimal pl-5 space-y-2">
              <li>
                <span className="font-medium">Select ticket type and quantity</span> - Attendees choose their ticket type and how many tickets they want.
              </li>
              <li>
                <span className="font-medium">Select seats</span> - Attendees will see the seating layout and can select specific seats for each ticket.
              </li>
              <li>
                <span className="font-medium">Enter attendee information</span> - Provide details for each ticket holder.
              </li>
              <li>
                <span className="font-medium">Complete payment</span> - Pay for the selected seats.
              </li>
            </ol>

            <Alert className="bg-primary/5 border-primary/20">
              <AlertCircle className="h-4 w-4 text-primary" />
              <AlertTitle className="text-primary">Important</AlertTitle>
              <AlertDescription className="text-primary/90">
                Once an attendee selects a seat, it will be temporarily reserved for 10 minutes to allow them to complete their purchase.
              </AlertDescription>
            </Alert>
          </div>
        </div>
      </div>
    </div>
  );
}
