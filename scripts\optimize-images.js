/**
 * Image Optimization Script
 * 
 * This script optimizes images in the public directory to reduce their size
 * before deployment. It uses sharp to resize and compress images.
 * 
 * Usage:
 * node scripts/optimize-images.js
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

// Configuration
const MAX_WIDTH = 1200; // Maximum width for images
const QUALITY = 80; // JPEG quality (0-100)
const PUBLIC_DIR = path.join(process.cwd(), 'public');
const DIRS_TO_OPTIMIZE = [
  'images',
  'events',
  'uploads/events',
  'uploads/verification',
  'uploads/sponsors',
  'uploads/profiles'
];

// Create optimized directory if it doesn't exist
const OPTIMIZED_DIR = path.join(process.cwd(), 'public-optimized');
if (!fs.existsSync(OPTIMIZED_DIR)) {
  fs.mkdirSync(OPTIMIZED_DIR, { recursive: true });
}

// Function to optimize an image
async function optimizeImage(filePath, outputPath) {
  try {
    const image = sharp(filePath);
    const metadata = await image.metadata();
    
    // Only resize if the image is larger than MAX_WIDTH
    if (metadata.width > MAX_WIDTH) {
      await image
        .resize(MAX_WIDTH)
        .jpeg({ quality: QUALITY })
        .toFile(outputPath);
      
      console.log(`✅ Optimized: ${path.relative(PUBLIC_DIR, filePath)}`);
    } else {
      // Just compress without resizing
      await image
        .jpeg({ quality: QUALITY })
        .toFile(outputPath);
      
      console.log(`✅ Compressed: ${path.relative(PUBLIC_DIR, filePath)}`);
    }
    
    // Log size reduction
    const originalSize = fs.statSync(filePath).size;
    const optimizedSize = fs.statSync(outputPath).size;
    const reduction = ((originalSize - optimizedSize) / originalSize * 100).toFixed(2);
    
    console.log(`   Original: ${(originalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Optimized: ${(optimizedSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`   Reduction: ${reduction}%`);
  } catch (error) {
    console.error(`❌ Error optimizing ${filePath}:`, error.message);
  }
}

// Function to process a directory
async function processDirectory(directory) {
  const fullPath = path.join(PUBLIC_DIR, directory);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`Directory not found: ${fullPath}`);
    return;
  }
  
  // Create the corresponding directory in the optimized folder
  const outputDir = path.join(OPTIMIZED_DIR, directory);
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Process all files in the directory
  const files = fs.readdirSync(fullPath);
  
  for (const file of files) {
    const filePath = path.join(fullPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // Recursively process subdirectories
      await processDirectory(path.join(directory, file));
    } else if (/\.(jpg|jpeg|png)$/i.test(file)) {
      // Process image files
      const outputPath = path.join(outputDir, file);
      await optimizeImage(filePath, outputPath);
    } else {
      // Copy non-image files as-is
      const outputPath = path.join(outputDir, file);
      fs.copyFileSync(filePath, outputPath);
      console.log(`📄 Copied: ${path.relative(PUBLIC_DIR, filePath)}`);
    }
  }
}

// Main function
async function main() {
  console.log('🖼️ Starting image optimization...');
  
  // Process each directory
  for (const dir of DIRS_TO_OPTIMIZE) {
    console.log(`\n📁 Processing directory: ${dir}`);
    await processDirectory(dir);
  }
  
  console.log('\n✨ Image optimization complete!');
  console.log(`Optimized images are in: ${OPTIMIZED_DIR}`);
  console.log('You can now use these optimized images for deployment.');
}

// Run the script
main().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
