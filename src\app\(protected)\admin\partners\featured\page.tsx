'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from '@/components/ui/use-toast';
import {
  Crown,
  Star,
  MapPin,
  Search,
  Filter,
  CheckCircle2,
  Package,
  Users,
  Loader2
} from 'lucide-react';
import Image from 'next/image';

interface Partner {
  id: string;
  businessName: string;
  partnerType: string;
  tier: string;
  city: string;
  province: string;
  logo?: string;
  rating?: number;
  totalReviews: number;
  isVerified: boolean;
  featured: boolean;
  _count?: {
    products: number;
    reviews: number;
  };
}

export default function FeaturedPartnersManagement() {
  const [partners, setPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [updatingPartners, setUpdatingPartners] = useState<Set<string>>(new Set());

  useEffect(() => {
    fetchPartners();
  }, []);

  const fetchPartners = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/partners?limit=100');
      if (!response.ok) throw new Error('Failed to fetch partners');
      
      const data = await response.json();
      setPartners(data.partners || []);
    } catch (error) {
      console.error('Error fetching partners:', error);
      toast({
        title: 'Error',
        description: 'Failed to load partners',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const toggleFeatured = async (partnerId: string, featured: boolean) => {
    setUpdatingPartners(prev => new Set(prev).add(partnerId));
    
    try {
      const response = await fetch('/api/admin/partners/featured', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ partnerId, featured }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update partner');
      }

      const data = await response.json();
      
      // Update local state
      setPartners(prev => 
        prev.map(partner => 
          partner.id === partnerId 
            ? { ...partner, featured }
            : partner
        )
      );

      toast({
        title: 'Success',
        description: data.message,
      });
    } catch (error) {
      console.error('Error updating partner:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update partner',
        variant: 'destructive',
      });
    } finally {
      setUpdatingPartners(prev => {
        const newSet = new Set(prev);
        newSet.delete(partnerId);
        return newSet;
      });
    }
  };

  // Filter partners based on search and type
  const filteredPartners = partners.filter(partner => {
    const matchesSearch = partner.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         partner.city.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || partner.partnerType === filterType;
    return matchesSearch && matchesType;
  });

  const featuredCount = partners.filter(p => p.featured).length;
  const verifiedCount = partners.filter(p => p.isVerified).length;

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="mb-8">
          <Skeleton className="h-8 w-64 mb-2" />
          <Skeleton className="h-4 w-96" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-16 w-16 rounded-full mb-4" />
                <Skeleton className="h-6 w-3/4 mb-2" />
                <Skeleton className="h-4 w-full mb-4" />
                <Skeleton className="h-8 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Featured Partners Management</h1>
        <p className="text-gray-600">Manage which partners are featured on the platform</p>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Crown className="h-8 w-8 text-yellow-500 mr-3" />
              <div>
                <p className="text-2xl font-bold">{featuredCount}</p>
                <p className="text-sm text-gray-600">Featured Partners</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <CheckCircle2 className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <p className="text-2xl font-bold">{verifiedCount}</p>
                <p className="text-sm text-gray-600">Verified Partners</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <p className="text-2xl font-bold">{partners.length}</p>
                <p className="text-sm text-gray-600">Total Partners</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search partners..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={filterType}
          onChange={(e) => setFilterType(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Types</option>
          <option value="HOTEL">Hotels</option>
          <option value="RESTAURANT">Restaurants</option>
          <option value="BAR">Bars</option>
          <option value="NIGHTCLUB">Nightclubs</option>
          <option value="CAFE">Cafes</option>
          <option value="VENUE">Venues</option>
          <option value="SERVICE">Services</option>
          <option value="OTHER">Other</option>
        </select>
      </div>

      {/* Partners Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPartners.map((partner) => (
          <Card key={partner.id} className="overflow-hidden">
            <CardContent className="p-6">
              {/* Partner Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  {/* Logo */}
                  <div className="w-12 h-12 rounded-full overflow-hidden bg-gray-100">
                    {partner.logo ? (
                      <Image
                        src={partner.logo}
                        alt={partner.businessName}
                        width={48}
                        height={48}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white font-bold">
                        {partner.businessName.charAt(0)}
                      </div>
                    )}
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-gray-900 line-clamp-1">
                      {partner.businessName}
                    </h3>
                    <p className="text-sm text-gray-500 capitalize">
                      {partner.partnerType.toLowerCase()}
                    </p>
                  </div>
                </div>

                {/* Badges */}
                <div className="flex flex-col gap-1">
                  {partner.featured && (
                    <Badge className="bg-yellow-500 text-white text-xs">
                      <Crown className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                  {partner.isVerified && (
                    <Badge className="bg-green-500 text-white text-xs">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>
              </div>

              {/* Partner Info */}
              <div className="space-y-3 mb-4">
                <div className="flex items-center gap-2 text-sm text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span>{partner.city}, {partner.province}</span>
                </div>

                {partner.rating && partner.rating > 0 && (
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="text-sm font-medium">{partner.rating.toFixed(1)}</span>
                    <span className="text-xs text-gray-500">
                      ({partner.totalReviews} reviews)
                    </span>
                  </div>
                )}

                <div className="flex items-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <Package className="h-3 w-3" />
                    <span>{partner._count?.products || 0} products</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{partner.totalReviews} reviews</span>
                  </div>
                </div>
              </div>

              {/* Featured Toggle */}
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <p className="font-medium text-sm">Featured Partner</p>
                  <p className="text-xs text-gray-500">
                    {partner.featured ? 'Currently featured' : 'Not featured'}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {updatingPartners.has(partner.id) && (
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                  )}
                  <Switch
                    checked={partner.featured}
                    onCheckedChange={(checked) => toggleFeatured(partner.id, checked)}
                    disabled={!partner.isVerified || updatingPartners.has(partner.id)}
                  />
                </div>
              </div>

              {!partner.isVerified && (
                <p className="text-xs text-amber-600 mt-2 text-center">
                  ⚠️ Partner must be verified to be featured
                </p>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPartners.length === 0 && (
        <div className="text-center py-12">
          <Crown className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Partners Found</h3>
          <p className="text-gray-500">Try adjusting your search or filter criteria.</p>
        </div>
      )}
    </div>
  );
}
