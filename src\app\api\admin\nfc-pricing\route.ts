import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

// Create a direct instance for this API route
const prisma = new PrismaClient();

/**
 * GET /api/admin/nfc-pricing
 * Get all NFC product pricing
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    // Get all NFC product pricing
    const pricing = await prisma.nFCProductPricing.findMany({
      orderBy: {
        deviceType: 'asc'
      }
    });

    return NextResponse.json(pricing);

  } catch (error) {
    console.error('Error fetching NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to fetch NFC product pricing'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/nfc-pricing
 * Create a new NFC product pricing
 */
export async function POST(request: NextRequest) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({
        error: 'Not authenticated'
      }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN') {
      return NextResponse.json({
        error: 'Unauthorized. Admin access required'
      }, { status: 403 });
    }

    // Parse the request body
    const body = await request.json();
    const { deviceType, name, description, price, currency, isActive, imageUrl } = body;

    // Validate required fields
    if (!deviceType || !name || price === undefined) {
      return NextResponse.json({
        error: 'Missing required fields: deviceType, name, and price are required'
      }, { status: 400 });
    }

    // Check if pricing for this device type and currency already exists
    const existingPricingRecord = await prisma.nFCProductPricing.findFirst({
      where: {
        deviceType: deviceType as any, // Cast to handle enum type
        currency: currency || 'USD'
      }
    });

    if (existingPricingRecord) {
      return NextResponse.json({
        error: 'Pricing for this device type and currency already exists'
      }, { status: 400 });
    }

    // Create the pricing using Prisma
    const newPricingRecord = await prisma.nFCProductPricing.create({
      data: {
        deviceType: deviceType as any, // Cast to handle enum type
        name,
        description,
        price,
        currency: currency || 'USD',
        isActive: isActive !== undefined ? isActive : true,
        imageUrl
      }
    });

    return NextResponse.json(newPricingRecord, { status: 201 });

  } catch (error) {
    console.error('Error creating NFC product pricing:', error);
    return NextResponse.json({
      error: 'Failed to create NFC product pricing'
    }, { status: 500 });
  }
}
