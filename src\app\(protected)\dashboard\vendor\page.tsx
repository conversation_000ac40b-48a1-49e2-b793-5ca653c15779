'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  ShoppingBag,
  Store,
  Users,
  DollarSign,
  Package,
  Loader2,
  CreditCard,
  Calendar,
  BarChart3,
  Tag,
  History,
  Star,
  Building
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import Link from 'next/link';
import Image from 'next/image';

interface VendorProfile {
  id: string;
  businessName: string;
  logo?: string;
  bannerImage?: string;
  verificationStatus: 'PENDING' | 'APPROVED' | 'REJECTED';
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  totalCustomers: number;
  totalNfcTransactions?: number;
  nfcTransactionRevenue?: number;
  featured?: boolean;
}

interface EventParticipation {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  status: string;
  participationStatus: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
}

export default function VendorDashboardPage() {
  const router = useRouter();
  const [profile, setProfile] = useState<VendorProfile | null>(null);
  const [events, setEvents] = useState<EventParticipation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeEvents, setActiveEvents] = useState<EventParticipation[]>([]);
  const [eventsLoaded, setEventsLoaded] = useState(false);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);

        // Fetch vendor profile
        const profileResponse = await fetch('/api/vendors/profile');

        if (!profileResponse.ok) {
          if (profileResponse.status === 404) {
            // No vendor profile found, redirect to create profile
            router.push('/dashboard/vendor/create-profile');
            return;
          }
          throw new Error('Failed to fetch vendor profile');
        }

        const profileData = await profileResponse.json();
        setProfile(profileData);

        // Fetch vendor events if profile is verified
        if (profileData.verificationStatus === 'APPROVED') {
          try {
            const eventsResponse = await fetch('/api/vendors/events');

            if (eventsResponse.ok) {
              const eventsData = await eventsResponse.json();
              setEvents(eventsData);

              // Filter active events (approved participation and event is ongoing or upcoming)
              const now = new Date();
              const active = eventsData.filter((event: EventParticipation) =>
                event.participationStatus === 'APPROVED' &&
                new Date(event.endDate) >= now
              );
              setActiveEvents(active);
            }
          } catch (eventError) {
            console.error('Error fetching events:', eventError);
            // Continue without events data
          } finally {
            setEventsLoaded(true);
          }
        } else {
          setEventsLoaded(true);
        }
      } catch (err) {
        console.error('Error fetching vendor data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load vendor data',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [router]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2">Loading vendor dashboard...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center">
            <AlertCircle className="mr-2 h-5 w-5" />
            Error Loading Vendor Dashboard
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>{error}</p>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => router.push('/dashboard')}
          >
            Return to Main Dashboard
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (!profile) {
    return (
      <Card className="w-full max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>No Vendor Profile Found</CardTitle>
          <CardDescription>
            You need to create a vendor profile to access the vendor dashboard.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => router.push('/dashboard/vendor/create-profile')}
            className="mt-2"
          >
            Create Vendor Profile
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Banner Section */}
      {profile.bannerImage && (
        <div className="relative h-48 mb-8 rounded-lg overflow-hidden">
          <Image
            src={profile.bannerImage}
            alt={`${profile.businessName} banner`}
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-black/20" />
          <div className="absolute bottom-4 left-4 text-white">
            <h2 className="text-2xl font-bold">{profile.businessName}</h2>
          </div>
        </div>
      )}

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div className="flex items-center gap-4">
          {/* Business Logo */}
          <Avatar className="h-16 w-16 border-2 border-gray-200">
            <AvatarImage src={profile.logo || undefined} alt={`${profile.businessName} logo`} />
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold text-lg">
              {profile.businessName ? profile.businessName.split(' ').map(word => word.charAt(0)).join('').toUpperCase().slice(0, 2) : <Building className="h-8 w-8" />}
            </AvatarFallback>
          </Avatar>

          <div>
            <h1 className="text-3xl font-bold">{profile.businessName}</h1>
            <div className="flex items-center mt-2">
            {profile.verificationStatus === 'PENDING' && (
              <div className="flex items-center text-yellow-600">
                <Clock className="h-4 w-4 mr-1" />
                <span>Verification Pending</span>
                <Link href="/dashboard/vendor/verification/status" className="ml-2 text-blue-600 hover:underline text-sm">
                  Check Status
                </Link>
              </div>
            )}
            {profile.verificationStatus === 'APPROVED' && (
              <div className="flex items-center text-green-600">
                <CheckCircle className="h-4 w-4 mr-1" />
                <span>Verified Vendor</span>
              </div>
            )}
            {profile.verificationStatus === 'REJECTED' && (
              <div className="flex items-center text-red-600">
                <AlertCircle className="h-4 w-4 mr-1" />
                <span>Verification Rejected</span>
                <Link href="/dashboard/vendor/verification/status" className="ml-2 text-blue-600 hover:underline text-sm">
                  View Details
                </Link>
              </div>
            )}
            </div>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/vendor/profile')}
          >
            <Store className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/vendor/createproduct')}
            disabled={profile.verificationStatus !== 'APPROVED'}
          >
            <Package className="h-4 w-4 mr-2" />
            Products
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/vendor/events')}
            disabled={profile.verificationStatus !== 'APPROVED'}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Events
          </Button>
          <Button
            variant="outline"
            onClick={() => router.push('/dashboard/vendor/featuring')}
            disabled={profile.verificationStatus !== 'APPROVED'}
            className="bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200 hover:from-yellow-100 hover:to-orange-100"
          >
            <Star className="h-4 w-4 mr-2" />
            Feature Business
          </Button>
          {eventsLoaded && activeEvents.length > 0 && (
            <Button
              onClick={() => router.push(`/dashboard/vendor/events/${activeEvents[0].id}/nfc`)}
              disabled={profile.verificationStatus !== 'APPROVED'}
            >
              <CreditCard className="h-4 w-4 mr-2" />
              NFC Terminal
            </Button>
          )}
        </div>
      </div>

      {profile.verificationStatus !== 'APPROVED' ? (
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
              <div>
                <h3 className="font-medium">Verification Required</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {profile.verificationStatus === 'PENDING'
                    ? 'Your vendor account is pending verification. Some features will be limited until verification is complete.'
                    : 'Your verification was rejected. Please check the rejection reason and resubmit your verification.'}
                </p>
                <Button
                  variant="link"
                  className="p-0 h-auto text-blue-600"
                  onClick={() => router.push('/dashboard/vendor/verification/status')}
                >
                  {profile.verificationStatus === 'PENDING' ? 'Check verification status' : 'Resubmit verification'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (eventsLoaded && activeEvents.length > 0) ? (
        <Card className="mb-8 border-2 border-blue-200">
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex items-start">
                <CreditCard className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                <div>
                  <h3 className="font-medium">NFC Payment Terminal</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    You have {activeEvents.length} active event{activeEvents.length > 1 ? 's' : ''}. Process customer purchases using NFC cards.
                  </p>
                </div>
              </div>
              <Button
                onClick={() => router.push(`/dashboard/vendor/events/${activeEvents[0].id}/nfc`)}
                className="w-full md:w-auto"
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Open NFC Terminal
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : null}

      {/* Featuring Status Card */}
      {profile.verificationStatus === 'APPROVED' && (
        <Card className={`mb-8 border-2 ${
          profile.featured
            ? 'border-green-200 bg-gradient-to-r from-green-50 to-emerald-50'
            : 'border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50'
        }`}>
          <CardContent className="pt-6">
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
              <div className="flex items-start">
                <Star className={`h-5 w-5 mr-2 mt-0.5 ${
                  profile.featured ? 'text-green-500' : 'text-yellow-500'
                }`} />
                <div>
                  <h3 className="font-medium">
                    {profile.featured ? 'Business Featured!' : 'Feature Your Business'}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {profile.featured
                      ? 'Your business is currently featured on the homepage with increased visibility.'
                      : 'Boost your visibility and attract more customers by featuring your business on the homepage.'
                    }
                  </p>
                </div>
              </div>
              <Button
                onClick={() => router.push('/dashboard/vendor/featuring')}
                className={`w-full md:w-auto ${
                  profile.featured
                    ? 'bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600'
                    : 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600'
                }`}
              >
                <Star className="h-4 w-4 mr-2" />
                {profile.featured ? 'Manage Featuring' : 'Feature Business'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mb-8">
        <Card className="xl:col-span-1">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Products</p>
                <h3 className="text-2xl font-bold">{profile.totalProducts}</h3>
              </div>
              <div className="bg-blue-100 p-3 rounded-full">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="xl:col-span-1">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Orders</p>
                <h3 className="text-2xl font-bold">{profile.totalOrders}</h3>
              </div>
              <div className="bg-green-100 p-3 rounded-full">
                <ShoppingBag className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="xl:col-span-1">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Customers</p>
                <h3 className="text-2xl font-bold">{profile.totalCustomers}</h3>
              </div>
              <div className="bg-orange-100 p-3 rounded-full">
                <Users className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="xl:col-span-1">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">NFC Transactions</p>
                <h3 className="text-2xl font-bold">{profile.totalNfcTransactions || 0}</h3>
              </div>
              <div className="bg-indigo-100 p-3 rounded-full">
                <CreditCard className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="xl:col-span-2">
          <CardContent className="pt-6">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-500">Total Revenue</p>
                <h3 className="text-2xl font-bold">K{profile.totalRevenue.toLocaleString()}</h3>
                {profile.nfcTransactionRevenue ? (
                  <p className="text-xs text-gray-500 mt-1">
                    NFC Sales: K{profile.nfcTransactionRevenue.toLocaleString()}
                  </p>
                ) : null}
              </div>
              <div className="bg-purple-100 p-3 rounded-full">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="events" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="events">Active Events</TabsTrigger>
          <TabsTrigger value="nfc">NFC Transactions</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="orders">Orders</TabsTrigger>
        </TabsList>

        <TabsContent value="events">
          <Card>
            <CardHeader>
              <CardTitle>Active Events</CardTitle>
              <CardDescription>
                Events where you are currently participating as a vendor
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeEvents.length === 0 ? (
                <div className="text-center py-8">
                  <Calendar className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Active Events</h3>
                  <p className="text-gray-500 mb-4">You don't have any active event participations</p>
                  <Button
                    onClick={() => router.push('/dashboard/vendor/events')}
                    disabled={profile.verificationStatus !== 'APPROVED'}
                  >
                    View All Events
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeEvents.map(event => (
                    <Card key={event.id} className="overflow-hidden">
                      <CardContent className="p-4">
                        <div className="flex flex-col md:flex-row justify-between gap-4">
                          <div>
                            <h3 className="font-medium text-lg">{event.title}</h3>
                            <div className="flex items-center text-sm text-gray-500 mt-1">
                              <Calendar className="h-4 w-4 mr-1" />
                              <span>
                                {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => router.push(`/dashboard/vendor/events/${event.id}`)}
                            >
                              Details
                            </Button>
                            <Button
                              size="sm"
                              onClick={() => router.push(`/dashboard/vendor/events/${event.id}/nfc`)}
                            >
                              <CreditCard className="h-4 w-4 mr-1" />
                              NFC Terminal
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}

                  <div className="text-center pt-4">
                    <Button
                      variant="outline"
                      onClick={() => router.push('/dashboard/vendor/events')}
                    >
                      View All Events
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="nfc">
          <Card>
            <CardHeader>
              <CardTitle>NFC Transactions</CardTitle>
              <CardDescription>
                Process and manage NFC payments at events
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!eventsLoaded ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2">Loading events...</span>
                </div>
              ) : activeEvents.length === 0 ? (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Active Events</h3>
                  <p className="text-gray-500 mb-4">You need to participate in an event to process NFC transactions</p>
                  <Button
                    onClick={() => router.push('/dashboard/vendor/events')}
                    disabled={profile.verificationStatus !== 'APPROVED'}
                  >
                    View Events
                  </Button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="bg-blue-50 p-4 rounded-md">
                    <div className="flex items-start">
                      <CreditCard className="h-5 w-5 text-blue-500 mr-2 mt-0.5" />
                      <div>
                        <h4 className="font-medium text-blue-700">NFC Payment Terminal</h4>
                        <p className="text-blue-600 text-sm mt-1">
                          Process customer purchases using NFC cards at your events
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {activeEvents.slice(0, 2).map(event => (
                      <Card key={event.id} className="overflow-hidden">
                        <CardContent className="p-4">
                          <h3 className="font-medium">{event.title}</h3>
                          <p className="text-sm text-gray-500 mt-1">
                            {new Date(event.startDate).toLocaleDateString()} - {new Date(event.endDate).toLocaleDateString()}
                          </p>
                          <Button
                            className="w-full mt-3"
                            onClick={() => router.push(`/dashboard/vendor/events/${event.id}/nfc`)}
                          >
                            <CreditCard className="h-4 w-4 mr-1" />
                            Open NFC Terminal
                          </Button>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {profile.totalNfcTransactions ? (
                    <div className="text-center pt-2">
                      <Button
                        variant="outline"
                        onClick={() => router.push('/dashboard/vendor/nfc/transactions')}
                      >
                        View Transaction History
                      </Button>
                    </div>
                  ) : null}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Your Products</CardTitle>
              <CardDescription>
                Manage your product listings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profile.totalProducts === 0 ? (
                <div className="text-center py-8">
                  <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Products Yet</h3>
                  <p className="text-gray-500 mb-4">Start adding products to your store</p>
                  <Button
                    onClick={() => router.push('/vendor/createproduct')}
                    disabled={profile.verificationStatus !== 'APPROVED'}
                  >
                    Add Your First Product
                  </Button>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Button onClick={() => router.push('/vendor/products')}>
                    View All Products
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>
                Track and manage your recent orders
              </CardDescription>
            </CardHeader>
            <CardContent>
              {profile.totalOrders === 0 ? (
                <div className="text-center py-8">
                  <ShoppingBag className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Orders Yet</h3>
                  <p className="text-gray-500">Orders will appear here once customers make purchases</p>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Button onClick={() => router.push('/dashboard/vendor/orders')}>
                    View All Orders
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
