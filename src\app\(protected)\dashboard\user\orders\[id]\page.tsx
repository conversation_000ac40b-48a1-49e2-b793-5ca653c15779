'use client';

import React, { useState, useEffect, use } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronLeft, 
  Calendar, 
  MapPin, 
  Clock, 
  User, 
  Mail, 
  Phone, 
  Download, 
  Ticket, 
  ExternalLink,
  ShoppingBag
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';
import Image from 'next/image';

// Types
interface OrderDetails {
  id: string;
  event: {
    id: string;
    title: string;
    startDate: string;
    endDate: string;
    venue: string;
    location: string;
    imagePath: string;
  };
  createdAt: string;
  status: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  totalAmount: number;
  pricePaid: number;
  tickets: {
    id: string;
    type: string;
    quantity: number;
    price: number;
    description?: string;
  }[];
}

export default function OrderDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      try {
        setIsLoading(true);
        const response = await fetch(`/api/user/orders/${id}`);
        
        if (!response.ok) {
          throw new Error('Failed to fetch order details');
        }
        
        const data = await response.json();
        setOrder(data);
      } catch (error) {
        console.error('Error fetching order details:', error);
        setError('Failed to load order details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrderDetails();
  }, [id]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status?.toLowerCase()) {
      case 'paid':
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <Button variant="ghost" asChild className="-ml-4">
          <Link href="/dashboard/user/orders">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Orders
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">Order Details</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Order #{id.substring(0, 8)}
            </p>
          </div>
          <div className="mt-4 md:mt-0">
            <Button variant="outline" className="mr-2">
              <Download className="mr-2 h-4 w-4" />
              Download Receipt
            </Button>
            <Button asChild>
              <Link href={`/dashboard/user/tickets?orderId=${id}`}>
                <Ticket className="mr-2 h-4 w-4" />
                View Tickets
              </Link>
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-1/2 mt-2" />
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Skeleton className="h-40 w-full" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <Skeleton className="h-6 w-1/3" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-40 w-full" />
              </CardContent>
            </Card>
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <ShoppingBag className="h-8 w-8 text-red-500" />
              </div>
              <h3 className="text-lg font-medium mb-2">Error Loading Order</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">{error}</p>
              <Button asChild>
                <Link href="/dashboard/user/orders">Return to Orders</Link>
              </Button>
            </CardContent>
          </Card>
        ) : order ? (
          <div className="space-y-6">
            {/* Order Summary Card */}
            <Card>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-xl">{order.event.title}</CardTitle>
                    <CardDescription>
                      Ordered on {format(new Date(order.createdAt), 'PPP')}
                    </CardDescription>
                  </div>
                  {getStatusBadge(order.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Event Image and Details */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="relative h-48 md:h-full rounded-md overflow-hidden">
                    {order.event.imagePath ? (
                      <Image
                        src={order.event.imagePath}
                        alt={order.event.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                        <Calendar className="h-12 w-12 text-gray-400" />
                      </div>
                    )}
                  </div>
                  <div className="md:col-span-2 space-y-4">
                    <div>
                      <h3 className="font-medium text-lg">{order.event.title}</h3>
                      <div className="flex flex-col space-y-2 mt-2">
                        <div className="flex items-center text-gray-500">
                          <Calendar className="h-4 w-4 mr-2" />
                          <span>
                            {format(new Date(order.event.startDate), 'PPP')}
                            {order.event.endDate && order.event.startDate !== order.event.endDate && 
                              ` - ${format(new Date(order.event.endDate), 'PPP')}`}
                          </span>
                        </div>
                        {order.event.venue && (
                          <div className="flex items-center text-gray-500">
                            <MapPin className="h-4 w-4 mr-2" />
                            <span>{order.event.venue}, {order.event.location}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Customer Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        <div className="flex items-center text-gray-500">
                          <User className="h-4 w-4 mr-2" />
                          <span>{order.customerName}</span>
                        </div>
                        <div className="flex items-center text-gray-500">
                          <Mail className="h-4 w-4 mr-2" />
                          <span>{order.customerEmail}</span>
                        </div>
                        {order.customerPhone && (
                          <div className="flex items-center text-gray-500">
                            <Phone className="h-4 w-4 mr-2" />
                            <span>{order.customerPhone}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                {/* Tickets */}
                <div>
                  <h3 className="font-medium mb-4">Tickets</h3>
                  <div className="space-y-4">
                    {order.tickets.map((ticket, idx) => (
                      <div key={idx} className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <div>
                          <div className="font-medium">{ticket.type}</div>
                          {ticket.description && (
                            <div className="text-sm text-gray-500">{ticket.description}</div>
                          )}
                          <div className="text-sm mt-1">Quantity: {ticket.quantity}</div>
                        </div>
                        <div className="text-right">
                          <div>{formatCurrency(ticket.price)} each</div>
                          <div className="font-medium">{formatCurrency(ticket.price * ticket.quantity)} total</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
                
                <Separator />
                
                {/* Order Summary */}
                <div>
                  <h3 className="font-medium mb-4">Order Summary</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Fees</span>
                      <span>{formatCurrency(0)}</span>
                    </div>
                    <Separator className="my-2" />
                    <div className="flex justify-between font-medium">
                      <span>Total</span>
                      <span>{formatCurrency(order.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Amount Paid</span>
                      <span>{formatCurrency(order.pricePaid)}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-6">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/user/orders">
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    Back to Orders
                  </Link>
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" asChild>
                    <Link href={`/events/${order.event.id}`} target="_blank">
                      <ExternalLink className="mr-2 h-4 w-4" />
                      Event Page
                    </Link>
                  </Button>
                  <Button asChild>
                    <Link href={`/dashboard/user/tickets?orderId=${order.id}`}>
                      <Ticket className="mr-2 h-4 w-4" />
                      View Tickets
                    </Link>
                  </Button>
                </div>
              </CardFooter>
            </Card>
          </div>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                <ShoppingBag className="h-8 w-8 text-gray-500" />
              </div>
              <h3 className="text-lg font-medium mb-2">Order Not Found</h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                The order you're looking for doesn't exist or you don't have permission to view it.
              </p>
              <Button asChild>
                <Link href="/dashboard/user/orders">Return to Orders</Link>
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </RoleGate>
  );
}
