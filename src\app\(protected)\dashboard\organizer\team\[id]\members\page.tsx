'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { RoleGate } from '@/components/auth/role-gate';
import { TeamPermissionGate } from '@/components/auth/team-permission-gate';
import { 
  Users, 
  UserPlus, 
  ChevronLeft,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  AlertCircle,
  Check,
  X
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

// Team interface
interface Team {
  id: string;
  name: string;
  ownerId: string;
}

// Team member interface
interface TeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

export default function TeamMembersPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [team, setTeam] = useState<Team | null>(null);
  const [members, setMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [isOwner, setIsOwner] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('ORGANIZER_EDITOR');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [memberToEdit, setMemberToEdit] = useState<TeamMember | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState('');

  // Fetch team and members data
  useEffect(() => {
    const fetchTeamAndMembers = async () => {
      try {
        setLoading(true);
        
        // Fetch team data
        const teamResponse = await fetch(`/api/teams/${resolvedParams.id}`);

        if (!teamResponse.ok) {
          if (teamResponse.status === 404) {
            toast({
              title: 'Team not found',
              description: 'The team you are looking for does not exist.',
              variant: 'destructive',
            });
            router.push('/dashboard/organizer/team');
            return;
          }
          throw new Error(`Error: ${teamResponse.status}`);
        }

        const teamData = await teamResponse.json();
        setTeam(teamData.team);
        setIsOwner(teamData.isOwner);

        // Fetch members data
        const membersResponse = await fetch(`/api/teams/${resolvedParams.id}/members`);
        
        if (!membersResponse.ok) {
          throw new Error(`Error: ${membersResponse.status}`);
        }
        
        const membersData = await membersResponse.json();
        setMembers(membersData.members);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load team data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchTeamAndMembers();
  }, [resolvedParams.id, router]);

  // Filter members based on search query
  const filteredMembers = members.filter(member => {
    const searchLower = searchQuery.toLowerCase();
    return (
      member.user.name?.toLowerCase().includes(searchLower) ||
      member.user.email.toLowerCase().includes(searchLower) ||
      member.role.toLowerCase().includes(searchLower)
    );
  });

  // Handle invite member
  const handleInviteMember = async () => {
    if (!inviteEmail.trim()) {
      toast({
        title: 'Error',
        description: 'Email is required',
        variant: 'destructive',
      });
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/teams/${resolvedParams.id}/members`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: inviteEmail,
          role: inviteRole,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: data.message || 'Invitation sent successfully',
      });
      
      // Reset form and close dialog
      setInviteEmail('');
      setInviteRole('ORGANIZER_EDITOR');
      setIsInviteDialogOpen(false);
      
      // Refresh the page to show the new invitation
      router.refresh();
    } catch (error) {
      console.error('Error inviting member:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to send invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle remove member
  const handleRemoveMember = async () => {
    if (!memberToRemove) return;
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/teams/${resolvedParams.id}/members/${memberToRemove.id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: data.message || 'Member removed successfully',
      });
      
      // Update the members list
      setMembers(members.filter(member => member.id !== memberToRemove.id));
      
      // Reset state and close dialog
      setMemberToRemove(null);
      setIsRemoveDialogOpen(false);
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove member',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit member role
  const handleEditMemberRole = async () => {
    if (!memberToEdit || !newRole) return;
    
    try {
      setIsSubmitting(true);
      
      const response = await fetch(`/api/teams/${resolvedParams.id}/members/${memberToEdit.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newRole,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }
      
      const data = await response.json();
      
      toast({
        title: 'Success',
        description: 'Member role updated successfully',
      });
      
      // Update the members list
      setMembers(members.map(member => 
        member.id === memberToEdit.id 
          ? { ...member, role: newRole } 
          : member
      ));
      
      // Reset state and close dialog
      setMemberToEdit(null);
      setNewRole('');
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating member role:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update member role',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get role badge color
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Admin</Badge>;
      case 'ORGANIZER_MANAGER':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Manager</Badge>;
      case 'ORGANIZER_EDITOR':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Editor</Badge>;
      case 'ORGANIZER_ANALYST':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Analyst</Badge>;
      case 'ORGANIZER_SUPPORT':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Support</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Get role display name
  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return 'Admin';
      case 'ORGANIZER_MANAGER':
        return 'Manager';
      case 'ORGANIZER_EDITOR':
        return 'Editor';
      case 'ORGANIZER_ANALYST':
        return 'Analyst';
      case 'ORGANIZER_SUPPORT':
        return 'Support';
      default:
        return role;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Team Not Found</h2>
          <p className="text-gray-500 mb-6">The team you are looking for does not exist or you don't have permission to view it.</p>
          <Button asChild>
            <Link href="/dashboard/organizer/team">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Teams
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <RoleGate allowedRole={['ORGANIZER', 'ADMIN', 'SUPERADMIN']}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link href={`/dashboard/organizer/team/${resolvedParams.id}`} className="text-sm text-gray-500 hover:text-gray-700 flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Team
          </Link>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold">Team Members</h1>
            <p className="text-gray-500 mt-1">
              Manage members of {team.name}
            </p>
          </div>
          
          <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
            <div className="mt-4 md:mt-0">
              <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Invite Member
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Invite Team Member</DialogTitle>
                    <DialogDescription>
                      Send an invitation to join this team. The recipient will receive an email with instructions.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter email address"
                        value={inviteEmail}
                        onChange={(e) => setInviteEmail(e.target.value)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Select value={inviteRole} onValueChange={setInviteRole}>
                        <SelectTrigger id="role">
                          <SelectValue placeholder="Select a role" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="ORGANIZER_ADMIN">Admin</SelectItem>
                          <SelectItem value="ORGANIZER_MANAGER">Manager</SelectItem>
                          <SelectItem value="ORGANIZER_EDITOR">Editor</SelectItem>
                          <SelectItem value="ORGANIZER_ANALYST">Analyst</SelectItem>
                          <SelectItem value="ORGANIZER_SUPPORT">Support</SelectItem>
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-gray-500 mt-1">
                        {inviteRole === 'ORGANIZER_ADMIN' && 'Full access to manage the team, events, and members.'}
                        {inviteRole === 'ORGANIZER_MANAGER' && 'Can create and edit events, and invite members.'}
                        {inviteRole === 'ORGANIZER_EDITOR' && 'Can edit event details but cannot create new events.'}
                        {inviteRole === 'ORGANIZER_ANALYST' && 'View-only access to events and analytics.'}
                        {inviteRole === 'ORGANIZER_SUPPORT' && 'Can manage attendees and tickets.'}
                      </p>
                    </div>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setIsInviteDialogOpen(false)}
                      disabled={isSubmitting}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleInviteMember} disabled={isSubmitting}>
                      {isSubmitting ? 'Sending...' : 'Send Invitation'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </TeamPermissionGate>
        </div>

        <Card>
          <CardHeader>
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
              <div>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>
                  {members.length} {members.length === 1 ? 'member' : 'members'} in this team
                </CardDescription>
              </div>
              <div className="mt-4 md:mt-0 w-full md:w-64">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search members..."
                    className="pl-8"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {members.length === 0 ? (
              <div className="text-center py-6">
                <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Members Yet</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  This team doesn't have any members yet. Invite people to collaborate on events.
                </p>
                <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                  <Button onClick={() => setIsInviteDialogOpen(true)}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Invite Member
                  </Button>
                </TeamPermissionGate>
              </div>
            ) : filteredMembers.length === 0 ? (
              <div className="text-center py-6">
                <AlertCircle className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                <h3 className="text-lg font-medium mb-2">No Results Found</h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  No members match your search query. Try a different search term.
                </p>
                <Button variant="outline" onClick={() => setSearchQuery('')}>
                  Clear Search
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 rounded-lg border">
                    <div className="flex items-center">
                      <Avatar className="h-10 w-10 mr-3">
                        <AvatarImage src={member.user.image || ''} alt={member.user.name || 'Team member'} />
                        <AvatarFallback>{getUserInitials(member.user.name || '')}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.user.name || 'Unnamed User'}</div>
                        <div className="text-sm text-gray-500">{member.user.email}</div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      {getRoleBadge(member.role)}
                      
                      <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                        {member.user.id !== team.ownerId && (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => {
                                setMemberToEdit(member);
                                setNewRole(member.role);
                                setIsEditDialogOpen(true);
                              }}>
                                <Edit className="h-4 w-4 mr-2" />
                                Change Role
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => {
                                  setMemberToRemove(member);
                                  setIsRemoveDialogOpen(true);
                                }}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Remove
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        )}
                      </TeamPermissionGate>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Edit Member Role Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change Member Role</DialogTitle>
              <DialogDescription>
                Update the role for {memberToEdit?.user.name || memberToEdit?.user.email || 'this member'}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-role">Role</Label>
                <Select value={newRole} onValueChange={setNewRole}>
                  <SelectTrigger id="edit-role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ORGANIZER_ADMIN">Admin</SelectItem>
                    <SelectItem value="ORGANIZER_MANAGER">Manager</SelectItem>
                    <SelectItem value="ORGANIZER_EDITOR">Editor</SelectItem>
                    <SelectItem value="ORGANIZER_ANALYST">Analyst</SelectItem>
                    <SelectItem value="ORGANIZER_SUPPORT">Support</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500 mt-1">
                  {newRole === 'ORGANIZER_ADMIN' && 'Full access to manage the team, events, and members.'}
                  {newRole === 'ORGANIZER_MANAGER' && 'Can create and edit events, and invite members.'}
                  {newRole === 'ORGANIZER_EDITOR' && 'Can edit event details but cannot create new events.'}
                  {newRole === 'ORGANIZER_ANALYST' && 'View-only access to events and analytics.'}
                  {newRole === 'ORGANIZER_SUPPORT' && 'Can manage attendees and tickets.'}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => {
                  setMemberToEdit(null);
                  setNewRole('');
                  setIsEditDialogOpen(false);
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button onClick={handleEditMemberRole} disabled={isSubmitting}>
                {isSubmitting ? 'Updating...' : 'Update Role'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Remove Member Confirmation Dialog */}
        <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove {memberToRemove?.user.name || memberToRemove?.user.email} from this team?
                They will no longer have access to this team's events.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
              <AlertDialogAction 
                onClick={(e) => {
                  e.preventDefault();
                  handleRemoveMember();
                }}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'Removing...' : 'Remove'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </RoleGate>
  );
}
