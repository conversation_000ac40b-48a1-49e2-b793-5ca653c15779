import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { startOfMonth, endOfMonth, subMonths, format } from 'date-fns';

export async function GET() {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only organizers can access this endpoint
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const now = new Date();

    // Get user's account balance
    const userWithBalance = await db.user.findUnique({
      where: { id: user.id },
      select: { accountBalance: true }
    });

    // Get total revenue from all completed orders for this organizer's events
    const totalRevenueData = await db.order.aggregate({
      where: {
        event: {
          userId: user.id!
        },
        status: 'Completed'
      },
      _sum: {
        totalPrice: true
      }
    });

    const totalRevenue = totalRevenueData._sum.totalPrice || 0;

    // Calculate platform fees (6% of total revenue)
    const platformFees = totalRevenue * 0.06;

    // Calculate net earnings (total revenue minus platform fees)
    const netEarnings = totalRevenue - platformFees;

    // Get pending withdrawals amount
    const pendingWithdrawals = await db.withdrawal.aggregate({
      where: {
        userId: user.id!,
        status: 'Pending'
      },
      _sum: {
        amount: true
      }
    });

    const pendingBalance = pendingWithdrawals._sum.amount || 0;
    const availableBalance = userWithBalance?.accountBalance || 0;

    // Get monthly revenue for the last 12 months
    const monthlyRevenue = await Promise.all(
      Array.from({ length: 12 }, (_, i) => {
        const monthStart = startOfMonth(subMonths(now, i));
        const monthEnd = endOfMonth(subMonths(now, i));
        
        return db.order.aggregate({
          where: {
            event: {
              userId: user.id!
            },
            status: 'Completed',
            createdAt: {
              gte: monthStart,
              lte: monthEnd
            }
          },
          _sum: {
            totalPrice: true
          }
        }).then(result => ({
          month: format(monthStart, 'MMM'),
          revenue: result._sum.totalPrice || 0,
          fees: (result._sum.totalPrice || 0) * 0.06
        }));
      })
    );

    // Get transaction count
    const transactionCount = await db.order.count({
      where: {
        event: {
          userId: user.id!
        }
      }
    });

    // Get average ticket price
    const ticketData = await db.ticket.aggregate({
      where: {
        event: {
          userId: user.id!
        }
      },
      _avg: {
        price: true
      },
      _count: true
    });

    const averageTicketPrice = ticketData._avg.price || 0;

    // Calculate conversion rate (completed orders / total orders)
    const completedOrders = await db.order.count({
      where: {
        event: {
          userId: user.id!
        },
        status: 'Completed'
      }
    });

    const conversionRate = transactionCount > 0 
      ? (completedOrders / transactionCount) * 100 
      : 0;

    // Get top performing events
    const topEvents = await db.event.findMany({
      where: {
        userId: user.id!
      },
      select: {
        id: true,
        title: true,
        orders: {
          where: {
            status: 'Completed'
          },
          select: {
            totalPrice: true
          }
        },
        tickets: {
          select: {
            id: true
          }
        }
      },
      orderBy: {
        orders: {
          _count: 'desc'
        }
      },
      take: 5
    });

    const formattedTopEvents = topEvents.map(event => {
      const revenue = event.orders.reduce((sum, order) => sum + order.totalPrice, 0);
      return {
        id: event.id,
        title: event.title,
        revenue,
        tickets: event.tickets.length
      };
    }).sort((a, b) => b.revenue - a.revenue);

    // Get recent withdrawals
    const recentWithdrawals = await db.withdrawal.findMany({
      where: {
        userId: user.id!
      },
      select: {
        id: true,
        amount: true,
        status: true,
        requestDate: true
      },
      orderBy: {
        requestDate: 'desc'
      },
      take: 3
    });

    // Return the dashboard data
    return NextResponse.json({
      totalRevenue,
      platformFees,
      netEarnings,
      availableBalance,
      pendingBalance,
      transactionCount,
      averageTicketPrice,
      conversionRate,
      monthlyRevenue: monthlyRevenue.reverse(), // Most recent month first
      topEvents: formattedTopEvents,
      recentWithdrawals: recentWithdrawals.map(w => ({
        id: w.id,
        amount: w.amount,
        status: w.status,
        date: w.requestDate.toISOString()
      }))
    });
  } catch (error) {
    console.error('Error fetching organizer finance dashboard:', error);
    return NextResponse.json(
      { error: 'Failed to fetch finance dashboard data' },
      { status: 500 }
    );
  }
}
