'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

import { useCurrentRole } from '@/hooks/use-current-role';
import { Calendar, Search, CheckCircle, Eye, Trash2, ArrowUpDown, Send, Filter, X, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

// Event interface
interface Event {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  startTime?: string;
  endTime?: string;
  location: string;
  venue?: string;
  category: string;
  status: 'Draft' | 'Pending' | 'UnderReview' | 'Approved' | 'Published' | 'Rejected' | 'Cancelled' | 'Completed';
  eventType: 'PHYSICAL' | 'ONLINE' | 'HYBRID';
  createdAt: string;
  updatedAt: string;
  imagePath?: string | null;
  organizer: {
    id: string;
    name: string;
    email: string;
    isVerified?: boolean;
  };
  stats?: {
    totalTicketsSold?: number;
    totalRevenue?: number;
    totalOrders?: number;
    totalAttendees?: number;
    engagement?: {
      views: number;
      clicks: number;
      shares: number;
      likes: number;
    };
  };
}

export default function AdminEventsPage() {
  const router = useRouter();

  const role = useCurrentRole();
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<'view' | 'approve' | 'reject' | 'delete' | 'publish' | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Mock event categories from the schema
  const eventCategories = [
    'WEDDING', 'FUNERAL', 'BUSINESS', 'MUSIC', 'LIFESTYLE_EVENTS',
    'EDUCATIONAL_EVENTS', 'HOLIDAY_CELEBRATIONS', 'FASHION_SHOWS',
    'HEALTH_AND_WELLNESS', 'CULTURAL_FESTIVALS', 'GAMING_EVENTS',
    'ENVIRONMENTAL_EVENTS', 'TRADE_FAIR', 'AGRICULTURAL_AND_COMMECIAL_SHOW',
    'WEB_DEVELOPMENT', 'MARKETING', 'TECHNOLOGY', 'CONCERTS_AND_CHURCH',
    'CONFERENCES_AND_WORKSHOPS', 'SPORTS_AND_FITNESS', 'ARTS_AND_THEATER',
    'FAMILY_AND_KIDS', 'FOOD_AND_DRINK', 'CHARITY_AND_FUNDRAISERS',
    'COMEDY_SHOWS', 'NETWORKING_AND_SOCIAL_GATHERINGS', 'FILM_SCREENINGS'
  ];

  // Fetch events
  useEffect(() => {
    const fetchEvents = async () => {
      setLoading(true);
      try {
        // Build query parameters
        const queryParams = new URLSearchParams();

        if (searchTerm) {
          queryParams.append('search', searchTerm);
        }

        if (statusFilter !== 'all') {
          queryParams.append('status', statusFilter);
        }

        if (categoryFilter !== 'all') {
          queryParams.append('category', categoryFilter);
        }

        if (typeFilter !== 'all') {
          queryParams.append('type', typeFilter);
        }

        queryParams.append('page', currentPage.toString());
        queryParams.append('limit', '10'); // 10 items per page
        queryParams.append('sortBy', sortField);
        queryParams.append('sortOrder', sortDirection);

        // Fetch events from the API
        const response = await fetch(`/api/admin/events?${queryParams.toString()}`);

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.events) {
          setEvents(data.events);
          setTotalPages(data.pagination.totalPages);
        } else {
          setEvents([]);
          setTotalPages(1);
        }

        setLoading(false);
      } catch (error) {
        console.error('Error fetching events:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch events. Please try again.',
          variant: 'destructive',
        });
        setLoading(false);
      }
    };

    fetchEvents();
  }, [searchTerm, statusFilter, categoryFilter, typeFilter, currentPage, sortField, sortDirection]);

  /* Mock data for reference - commented out to use real API data
  const mockEvents: Event[] = [
    // Mock event data here
  ];
  */

  // Open action dialog
  const openActionDialog = (event: Event, action: 'view' | 'approve' | 'reject' | 'delete' | 'publish') => {
    setSelectedEvent(event);
    setDialogAction(action);
    setIsDialogOpen(true);
  };

  // Handle event actions
  const handleEventAction = async (action: 'approve' | 'reject' | 'delete' | 'publish') => {
    if (!selectedEvent) return;

    try {
      let successMessage = '';
      let endpoint = '';
      let method = 'PATCH';
      let body: any = {};

      if (action === 'approve') {
        // Update event status to Published
        endpoint = `/api/admin/events/review`;
        body = {
          eventId: selectedEvent.id,
          status: 'Published',
          reviewNotes: ''
        };
        successMessage = `Event "${selectedEvent.title}" has been approved`;
      } else if (action === 'reject') {
        if (rejectionReason && !rejectionReason.trim()) {
          toast({
            title: 'Error',
            description: 'Please provide a reason for rejection',
            variant: 'destructive',
          });
          return;
        }
        // Update event status to Rejected
        endpoint = `/api/admin/events/review`;
        body = {
          eventId: selectedEvent.id,
          status: 'Rejected',
          reviewNotes: rejectionReason || 'Rejected by admin'
        };
        successMessage = `Event "${selectedEvent.title}" has been rejected`;
      } else if (action === 'publish') {
        // Update event status to Published
        endpoint = `/api/events/${selectedEvent.id}/publish`;
        body = {};
        successMessage = `Event "${selectedEvent.title}" has been published`;
      } else if (action === 'delete') {
        // Delete event
        endpoint = `/api/admin/events/${selectedEvent.id}`;
        method = 'DELETE';
        successMessage = `Event "${selectedEvent.title}" has been deleted`;
      }

      // Make the API call
      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: method !== 'DELETE' ? JSON.stringify(body) : undefined,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${action} event: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: successMessage,
      });

      // Update local state
      if (action === 'delete') {
        setEvents(events.filter(event => event.id !== selectedEvent.id));
      } else {
        const newStatus = action === 'approve' ? 'Published' : action === 'publish' ? 'Published' : 'Rejected';
        setEvents(events.map(event =>
          event.id === selectedEvent.id
            ? { ...event, status: newStatus }
            : event
        ));
      }

      // Close dialog and reset state
      setIsDialogOpen(false);
      setSelectedEvent(null);
      setDialogAction(null);
      setRejectionReason('');
    } catch (error) {
      console.error(`Error ${action}ing event:`, error);
      toast({
        title: 'Error',
        description: `Failed to ${action} event. Please try again.`,
        variant: 'destructive',
      });
    }
  };

  // Format date for display
  const formatDateDisplay = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">{status}</Badge>;
      case 'Draft':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
      case 'Pending':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">{status}</Badge>;
      case 'UnderReview':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">{status}</Badge>;
      case 'Approved':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">{status}</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{status}</Badge>;
      case 'Cancelled':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{status}</Badge>;
      case 'Completed':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{status}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
    }
  };

  // Get event type badge
  const getEventTypeBadge = (type: string) => {
    switch (type) {
      case 'PHYSICAL':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{type}</Badge>;
      case 'ONLINE':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">{type}</Badge>;
      case 'HYBRID':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">{type}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{type}</Badge>;
    }
  };

  // Toggle sort direction
  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Manage Events</h1>
          <p className="text-gray-500 mt-1">
            Review, approve, and manage events on the platform
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild>
            <Link href="/admin/events/review">
              <CheckCircle className="mr-2 h-4 w-4" />
              Review Pending Events
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label htmlFor="search" className="text-sm font-medium text-gray-700 mb-1 block">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  id="search"
                  placeholder="Search events..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label htmlFor="status" className="text-sm font-medium text-gray-700 mb-1 block">
                Status
              </label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="Draft">Draft</SelectItem>
                  <SelectItem value="Published">Published</SelectItem>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label htmlFor="category" className="text-sm font-medium text-gray-700 mb-1 block">
                Category
              </label>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger id="category">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Categories</SelectItem>
                  {eventCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category.replace(/_/g, ' ')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label htmlFor="type" className="text-sm font-medium text-gray-700 mb-1 block">
                Event Type
              </label>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="PHYSICAL">Physical</SelectItem>
                  <SelectItem value="ONLINE">Online</SelectItem>
                  <SelectItem value="HYBRID">Hybrid</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <CardHeader>
          <CardTitle>Events</CardTitle>
          <CardDescription>
            {events.length} events found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : events.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">No events found</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Try adjusting your filters or check back later for new events.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('title')}
                      >
                        Title
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Organizer</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('startDate')}
                      >
                        Date
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Type</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('createdAt')}
                      >
                        Created
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {events.map((event) => (
                    <tr key={event.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-4 text-sm font-medium text-gray-900">{event.title}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">{event.organizer?.name || 'Unknown'}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">{formatDateDisplay(event.startDate)}</td>
                      <td className="px-4 py-4 text-sm">{getStatusBadge(event.status)}</td>
                      <td className="px-4 py-4 text-sm">{getEventTypeBadge(event.eventType)}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">{formatDateDisplay(event.createdAt)}</td>
                      <td className="px-4 py-4 text-sm text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openActionDialog(event, 'view')}
                            className="h-8 w-8 p-0"
                          >
                            <span className="sr-only">View</span>
                            <Eye className="h-4 w-4" />
                          </Button>
                          {(event.status === 'Pending' || event.status === 'UnderReview') && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openActionDialog(event, 'approve')}
                              className="h-8 w-8 p-0 text-green-600 hover:text-green-700 hover:bg-green-50"
                            >
                              <span className="sr-only">Approve</span>
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          {event.status === 'Approved' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openActionDialog(event, 'publish')}
                              className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <span className="sr-only">Publish</span>
                              <Send className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openActionDialog(event, 'delete')}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <span className="sr-only">Delete</span>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && events.length > 0 && (
            <div className="flex justify-between items-center mt-6">
              <div className="text-sm text-gray-500">
                Showing {Math.min(events.length, 10)} of {events.length} events
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Event Action Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          {selectedEvent && (
            <>
              <DialogHeader>
                <DialogTitle>
                  {dialogAction === 'view' && 'Event Details'}
                  {dialogAction === 'approve' && 'Approve Event'}
                  {dialogAction === 'reject' && 'Reject Event'}
                  {dialogAction === 'delete' && 'Delete Event'}
                  {dialogAction === 'publish' && 'Publish Event'}
                </DialogTitle>
                <DialogDescription>
                  {dialogAction === 'view' && 'View details for this event'}
                  {dialogAction === 'approve' && 'Approve this event for publication'}
                  {dialogAction === 'reject' && 'Reject this event with a reason'}
                  {dialogAction === 'delete' && 'Are you sure you want to delete this event?'}
                  {dialogAction === 'publish' && 'Make this event visible to users'}
                </DialogDescription>
              </DialogHeader>

              {dialogAction === 'view' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold">{selectedEvent.title}</h3>
                    <div className="flex items-center mt-2 space-x-2">
                      {getStatusBadge(selectedEvent.status)}
                      {getEventTypeBadge(selectedEvent.eventType)}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Description</h4>
                      <p className="text-sm">{selectedEvent.description}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Organizer</h4>
                      <p className="text-sm">{selectedEvent.organizer?.name || 'Unknown'}</p>
                      <p className="text-sm text-gray-500">{selectedEvent.organizer?.email || 'No email provided'}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Start Date</h4>
                      <p className="text-sm">{formatDateDisplay(selectedEvent.startDate)}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">End Date</h4>
                      <p className="text-sm">{formatDateDisplay(selectedEvent.endDate)}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Location</h4>
                      <p className="text-sm">{selectedEvent.location}</p>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-1">Category</h4>
                    <p className="text-sm">{selectedEvent.category.replace(/_/g, ' ')}</p>
                  </div>
                </div>
              )}

              {dialogAction === 'reject' && (
                <div className="space-y-4 py-4">
                  <div>
                    <label htmlFor="rejection-reason" className="text-sm font-medium text-gray-700 mb-1 block">
                      Rejection Reason
                    </label>
                    <textarea
                      id="rejection-reason"
                      className="w-full p-2 border border-gray-300 rounded-md"
                      rows={4}
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Provide a reason for rejecting this event..."
                    />
                  </div>
                </div>
              )}

              {dialogAction === 'delete' && (
                <div className="py-4">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete the event &quot;{selectedEvent.title}&quot;? This action cannot be undone.
                  </p>
                </div>
              )}

              {dialogAction === 'publish' && (
                <div className="py-4">
                  <p className="text-sm text-gray-500">
                    This will make the event &quot;{selectedEvent.title}&quot; visible to all users. The event will appear in search results and on the homepage.
                  </p>
                </div>
              )}

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                {dialogAction === 'approve' && (
                  <Button onClick={() => handleEventAction('approve')}>
                    Approve Event
                  </Button>
                )}
                {dialogAction === 'reject' && (
                  <Button
                    onClick={() => handleEventAction('reject')}
                    disabled={!rejectionReason.trim()}
                  >
                    Reject Event
                  </Button>
                )}
                {dialogAction === 'delete' && (
                  <Button
                    variant="destructive"
                    onClick={() => handleEventAction('delete')}
                  >
                    Delete Event
                  </Button>
                )}
                {dialogAction === 'publish' && (
                  <Button onClick={() => handleEventAction('publish')}>
                    Publish Event
                  </Button>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
