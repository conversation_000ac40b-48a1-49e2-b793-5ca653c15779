import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/revenue-comparison
 * Get revenue comparison data (current period vs previous period)
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date(new Date().setFullYear(new Date().getFullYear() - 1));
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();
    
    // Calculate the previous period (same duration, but one year earlier)
    const periodDuration = endDate.getTime() - startDate.getTime();
    const previousPeriodEndDate = new Date(startDate.getTime() - 1); // One day before current period start
    const previousPeriodStartDate = new Date(previousPeriodEndDate.getTime() - periodDuration);

    // Get all months between start and end date for current period
    const currentMonths = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      currentMonths.push(new Date(currentDate));
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Get all months between start and end date for previous period
    const previousMonths = [];
    const previousDate = new Date(previousPeriodStartDate);
    while (previousDate <= previousPeriodEndDate) {
      previousMonths.push(new Date(previousDate));
      previousDate.setMonth(previousDate.getMonth() + 1);
    }

    // Get revenue data for current period
    const currentPeriodData = await Promise.all(currentMonths.map(async (month) => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);

      // Get ticket sales for the month
      const ticketSales = await db.order.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalPrice: true
        }
      });

      // Get vendor sales for the month
      const vendorSales = await db.vendorOrder.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalAmount: true
        }
      });

      // Get NFC transactions for the month
      const nfcTransactions = await db.vendorNFCTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          amount: true
        }
      });

      const totalRevenue = 
        (ticketSales._sum.totalPrice || 0) + 
        (vendorSales._sum.totalAmount || 0) + 
        (nfcTransactions._sum.amount || 0);

      return {
        name: month.toLocaleString('default', { month: 'short' }) + ' ' + month.getFullYear(),
        revenue: totalRevenue,
        month: month.getMonth(),
        year: month.getFullYear()
      };
    }));

    // Get revenue data for previous period
    const previousPeriodData = await Promise.all(previousMonths.map(async (month) => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);

      // Get ticket sales for the month
      const ticketSales = await db.order.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalPrice: true
        }
      });

      // Get vendor sales for the month
      const vendorSales = await db.vendorOrder.aggregate({
        where: {
          status: 'Completed',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalAmount: true
        }
      });

      // Get NFC transactions for the month
      const nfcTransactions = await db.vendorNFCTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          amount: true
        }
      });

      const totalRevenue = 
        (ticketSales._sum.totalPrice || 0) + 
        (vendorSales._sum.totalAmount || 0) + 
        (nfcTransactions._sum.amount || 0);

      return {
        month: month.getMonth(),
        year: month.getFullYear(),
        revenue: totalRevenue
      };
    }));

    // Combine the data
    const combinedData = currentPeriodData.map((currentMonth, index) => {
      // Find the corresponding month in the previous period
      const previousMonth = previousPeriodData[index] || { revenue: 0 };
      
      // Calculate growth rate
      let growth = 0;
      if (previousMonth.revenue > 0) {
        growth = ((currentMonth.revenue - previousMonth.revenue) / previousMonth.revenue) * 100;
      }
      
      return {
        name: currentMonth.name,
        revenue: currentMonth.revenue,
        previousYearRevenue: previousMonth.revenue,
        growth
      };
    });

    return NextResponse.json(combinedData);
  } catch (error) {
    console.error('Error fetching revenue comparison data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch revenue comparison data' },
      { status: 500 }
    );
  }
}
