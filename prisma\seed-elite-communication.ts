import { PrismaClient } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

/**
 * Dedicated seed script for the Elite Communication System
 * This script creates comprehensive sample data for testing and development
 */
async function main() {
  console.log('Starting Elite Communication System seeding...');

  try {
    // Sample user data for Elite Communication System
    const sampleUsers = [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        company: 'TechCorp Solutions',
        role: 'Senior Software Engineer',
        industry: 'Technology',
        bio: 'Passionate about AI and machine learning. Looking to connect with fellow tech enthusiasts and explore collaboration opportunities.',
        interests: ['Artificial Intelligence', 'Machine Learning', 'Cloud Computing', 'Open Source'],
        networkingGoals: 'Connect with AI researchers and potential collaborators for open source projects',
        linkedinUrl: 'https://linkedin.com/in/sarahchen',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE_PRO'
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        company: 'FinancePlus',
        role: 'Investment Director',
        industry: 'Finance',
        bio: 'Experienced investment professional specializing in fintech startups. Always interested in innovative financial solutions.',
        interests: ['Fintech', 'Blockchain', 'Investment Strategy', 'Startups'],
        networkingGoals: 'Discover promising fintech startups and connect with entrepreneurs',
        linkedinUrl: 'https://linkedin.com/in/marcusjohnson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Dr. Emily Rodriguez',
        email: '<EMAIL>',
        company: 'HealthTech Innovations',
        role: 'Chief Medical Officer',
        industry: 'Healthcare',
        bio: 'Medical doctor turned healthcare technology executive. Focused on digital health solutions that improve patient outcomes.',
        interests: ['Digital Health', 'Telemedicine', 'Medical AI', 'Patient Care'],
        networkingGoals: 'Connect with healthcare innovators and discuss digital transformation in medicine',
        linkedinUrl: 'https://linkedin.com/in/emilyrodriguez',
        timezone: 'America/Chicago',
        tier: 'ELITE'
      },
      {
        name: 'David Kim',
        email: '<EMAIL>',
        company: 'MarketingPro Agency',
        role: 'Creative Director',
        industry: 'Marketing',
        bio: 'Award-winning creative director with 10+ years in digital marketing. Passionate about brand storytelling and user experience.',
        interests: ['Brand Strategy', 'Digital Marketing', 'UX Design', 'Content Creation'],
        networkingGoals: 'Meet potential clients and collaborate with other creative professionals',
        linkedinUrl: 'https://linkedin.com/in/davidkim',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Lisa Thompson',
        email: '<EMAIL>',
        company: 'Independent Consultant',
        role: 'Business Strategy Consultant',
        industry: 'Consulting',
        bio: 'Independent consultant helping startups and SMEs develop growth strategies. Former McKinsey consultant.',
        interests: ['Business Strategy', 'Startups', 'Growth Hacking', 'Leadership'],
        networkingGoals: 'Find new clients and stay updated on industry trends',
        linkedinUrl: 'https://linkedin.com/in/lisathompson',
        timezone: 'America/New_York',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Ahmed Hassan',
        email: '<EMAIL>',
        company: 'RetailTech Solutions',
        role: 'Product Manager',
        industry: 'Retail Technology',
        bio: 'Product manager focused on e-commerce and retail technology solutions. Experienced in agile development and user research.',
        interests: ['E-commerce', 'Product Management', 'User Research', 'Agile'],
        networkingGoals: 'Connect with other product managers and learn about emerging retail trends',
        linkedinUrl: 'https://linkedin.com/in/ahmedhassan',
        timezone: 'Europe/London',
        tier: 'ELITE'
      },
      {
        name: 'Jennifer Wu',
        email: '<EMAIL>',
        company: 'DataAnalytics Corp',
        role: 'Data Scientist',
        industry: 'Data Analytics',
        bio: 'Data scientist with expertise in predictive modeling and business intelligence. PhD in Statistics from Stanford.',
        interests: ['Data Science', 'Machine Learning', 'Statistics', 'Business Intelligence'],
        networkingGoals: 'Share knowledge and learn about new data science applications',
        linkedinUrl: 'https://linkedin.com/in/jenniferwu',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Robert Brown',
        email: '<EMAIL>',
        company: 'Advanced Manufacturing Inc',
        role: 'Operations Director',
        industry: 'Manufacturing',
        bio: 'Operations expert with 15+ years in manufacturing. Specializing in lean processes and automation.',
        interests: ['Lean Manufacturing', 'Automation', 'Supply Chain', 'Quality Control'],
        networkingGoals: 'Learn about new manufacturing technologies and best practices',
        linkedinUrl: 'https://linkedin.com/in/robertbrown',
        timezone: 'America/Chicago',
        tier: 'BASIC'
      },
      {
        name: 'Maria Garcia',
        email: '<EMAIL>',
        company: 'Global Impact Foundation',
        role: 'Program Director',
        industry: 'Non-Profit',
        bio: 'Dedicated to social impact and sustainable development. Leading programs that address global challenges.',
        interests: ['Social Impact', 'Sustainability', 'Community Development', 'Fundraising'],
        networkingGoals: 'Connect with like-minded professionals and potential partners',
        linkedinUrl: 'https://linkedin.com/in/mariagarcia',
        timezone: 'America/New_York',
        tier: 'ELITE'
      },
      {
        name: 'James Wilson',
        email: '<EMAIL>',
        company: 'Premier Real Estate',
        role: 'Senior Broker',
        industry: 'Real Estate',
        bio: 'Commercial real estate broker with expertise in tech company relocations and office space optimization.',
        interests: ['Commercial Real Estate', 'Property Investment', 'Urban Planning', 'Architecture'],
        networkingGoals: 'Meet potential clients and stay informed about market trends',
        linkedinUrl: 'https://linkedin.com/in/jameswilson',
        timezone: 'America/Los_Angeles',
        tier: 'ELITE'
      },
      {
        name: 'Anna Petrov',
        email: '<EMAIL>',
        company: 'CyberSecure Systems',
        role: 'Security Architect',
        industry: 'Cybersecurity',
        bio: 'Cybersecurity expert with focus on enterprise security architecture and threat intelligence.',
        interests: ['Cybersecurity', 'Threat Intelligence', 'Network Security', 'Compliance'],
        networkingGoals: 'Stay updated on security threats and connect with security professionals',
        linkedinUrl: 'https://linkedin.com/in/annapetrov',
        timezone: 'Europe/Berlin',
        tier: 'ELITE_PRO'
      },
      {
        name: 'Michael Davis',
        email: '<EMAIL>',
        company: 'Global Logistics Solutions',
        role: 'Supply Chain Manager',
        industry: 'Logistics',
        bio: 'Supply chain professional with expertise in international logistics and warehouse optimization.',
        interests: ['Supply Chain', 'Logistics', 'International Trade', 'Warehouse Management'],
        networkingGoals: 'Learn about supply chain innovations and connect with industry peers',
        linkedinUrl: 'https://linkedin.com/in/michaeldavis',
        timezone: 'America/Chicago',
        tier: 'BASIC'
      },
      {
        name: 'Sophie Martin',
        email: '<EMAIL>',
        company: 'Creative Design Studio',
        role: 'UX Designer',
        industry: 'Design',
        bio: 'UX designer passionate about creating intuitive and accessible digital experiences.',
        interests: ['UX Design', 'Accessibility', 'Design Systems', 'User Research'],
        networkingGoals: 'Connect with other designers and learn about design trends',
        linkedinUrl: 'https://linkedin.com/in/sophiemartin',
        timezone: 'Europe/Paris',
        tier: 'ELITE'
      },
      {
        name: 'Carlos Rodriguez',
        email: '<EMAIL>',
        company: 'Renewable Energy Corp',
        role: 'Project Engineer',
        industry: 'Energy',
        bio: 'Renewable energy engineer working on solar and wind projects. Committed to sustainable energy solutions.',
        interests: ['Renewable Energy', 'Solar Power', 'Wind Energy', 'Sustainability'],
        networkingGoals: 'Connect with other renewable energy professionals and potential partners',
        linkedinUrl: 'https://linkedin.com/in/carlosrodriguez',
        timezone: 'America/Denver',
        tier: 'ELITE'
      },
      {
        name: 'Rachel Green',
        email: '<EMAIL>',
        company: 'EduTech Innovations',
        role: 'Learning Experience Designer',
        industry: 'Education Technology',
        bio: 'Designing engaging learning experiences using technology. Former teacher with passion for educational innovation.',
        interests: ['Educational Technology', 'Learning Design', 'Online Education', 'Student Engagement'],
        networkingGoals: 'Connect with educators and learn about new learning technologies',
        linkedinUrl: 'https://linkedin.com/in/rachelgreen',
        timezone: 'America/New_York',
        tier: 'BASIC'
      }
    ];

    // Check if Elite Communication users already exist
    const existingEliteUsers = await prisma.user.count({
      where: {
        email: {
          in: sampleUsers.map(u => u.email)
        }
      }
    });

    if (existingEliteUsers > 0) {
      console.log(`Elite Communication users already exist (${existingEliteUsers} found). Skipping Elite Communication seeding.`);
      return;
    }

    console.log('Creating sample users for Elite Communication System...');

    // Create users
    const createdUsers = [];
    for (const userData of sampleUsers) {
      const hashedPassword = await hash('EliteUser123!', 12);
      const user = await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: 'USER',
          emailVerified: new Date(),
          image: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userData.name.replace(' ', '')}`
        }
      });
      createdUsers.push({ ...user, userData });
      console.log(`Created user: ${userData.name} (${userData.email})`);
    }

    // Create sample events for Elite Communication
    console.log('Creating sample events...');

    // Get an organizer to create events
    let organizer = await prisma.user.findFirst({
      where: { role: 'ORGANIZER' }
    });

    if (!organizer) {
      console.log('No organizer found. Creating a sample organizer...');
      const hashedPassword = await hash('Organizer123!', 12);
      organizer = await prisma.user.create({
        data: {
          name: 'Elite Event Organizer',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'ORGANIZER',
          emailVerified: new Date(),
          subscriptionTier: 'PREMIUM'
        }
      });
      console.log(`Created organizer: ${organizer.email}`);
    }

    const sampleEvents = [
      {
        title: 'Tech Innovation Summit 2024',
        description: 'Join industry leaders and innovators for a day of cutting-edge technology discussions, networking, and collaboration opportunities.',
        location: 'San Francisco Convention Center',
        venue: 'Main Auditorium',
        startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000), // 8 hours later
        startTime: '09:00',
        endTime: '17:00',
        eventType: 'PHYSICAL' as any,
        category: 'TECHNOLOGY' as any,
        status: 'Published' as any,
        hasStadiumSeating: false
      },
      {
        title: 'Global Finance & Fintech Conference',
        description: 'Explore the future of finance with blockchain, AI, and digital transformation. Network with financial professionals and fintech entrepreneurs.',
        location: 'New York Financial District',
        venue: 'Finance Tower Conference Center',
        startDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000), // 45 days from now
        endDate: new Date(Date.now() + 47 * 24 * 60 * 60 * 1000), // 2 days later
        startTime: '08:00',
        endTime: '18:00',
        eventType: 'PHYSICAL' as any,
        category: 'BUSINESS' as any,
        status: 'Published' as any,
        hasStadiumSeating: false
      },
      {
        title: 'Healthcare Innovation Symposium',
        description: 'Discover the latest in digital health, telemedicine, and medical AI. Connect with healthcare professionals and technology innovators.',
        location: 'Chicago Medical Center',
        venue: 'Innovation Hub',
        startDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        endDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000), // 6 hours later
        startTime: '10:00',
        endTime: '16:00',
        eventType: 'HYBRID' as any,
        category: 'HEALTH_AND_WELLNESS' as any,
        status: 'Published' as any,
        hasStadiumSeating: false
      }
    ];

    const createdEvents = [];
    for (const eventData of sampleEvents) {
      const event = await prisma.event.create({
        data: {
          ...eventData,
          userId: organizer.id
        }
      });
      createdEvents.push(event);
      console.log(`Created event: ${eventData.title}`);
    }

    // Create Elite Communication subscriptions and attendee profiles
    console.log('Creating Elite Communication subscriptions and attendee profiles...');

    const createdProfiles = [];
    for (let i = 0; i < createdUsers.length; i++) {
      const user = createdUsers[i];
      const userData = user.userData;

      // Create Elite Communication subscription for each event
      for (const event of createdEvents) {
        const eliteCommunication = await prisma.eliteCommunication.create({
          data: {
            userId: user.id,
            eventId: event.id,
            tier: userData.tier as any,
            subscriptionType: 'PER_EVENT',
            isActive: true,
            expiresAt: new Date(event.endDate.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days after event
            purchasePrice: userData.tier === 'BASIC' ? 0 : userData.tier === 'ELITE' ? 29.99 : 49.99
          }
        });

        // Create attendee profile
        const attendeeProfile = await prisma.attendeeProfile.create({
          data: {
            userId: user.id,
            eventId: event.id,
            displayName: userData.name,
            bio: userData.bio,
            company: userData.company,
            role: userData.role,
            industry: userData.industry,
            interests: userData.interests,
            networkingGoals: userData.networkingGoals,
            profilePhoto: user.image,
            linkedinUrl: userData.linkedinUrl,
            isDiscoverable: userData.tier !== 'BASIC',
            privacyLevel: userData.tier === 'BASIC' ? 'HIDDEN' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'PUBLIC',
            allowMessages: userData.tier === 'BASIC' ? 'NONE' : userData.tier === 'ELITE' ? 'ELITE_ONLY' : 'EVERYONE',
            allowMeetings: userData.tier !== 'BASIC',
            timezone: userData.timezone,
            availableHours: {
              monday: { start: '09:00', end: '17:00', available: true },
              tuesday: { start: '09:00', end: '17:00', available: true },
              wednesday: { start: '09:00', end: '17:00', available: true },
              thursday: { start: '09:00', end: '17:00', available: true },
              friday: { start: '09:00', end: '17:00', available: true },
              saturday: { start: '10:00', end: '14:00', available: false },
              sunday: { start: '10:00', end: '14:00', available: false }
            }
          }
        });

        createdProfiles.push({ profile: attendeeProfile, user, event, tier: userData.tier });
      }
    }

    console.log(`Created ${createdProfiles.length} attendee profiles with Elite Communication subscriptions`);

    // Create chat rooms for each event
    console.log('Creating Elite chat rooms...');

    const createdChatRooms = [];
    for (const event of createdEvents) {
      // Create Elite exclusive chat room
      const eliteRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Networking - ${event.title}`,
          description: 'Exclusive networking space for Elite and Elite Pro members',
          roomType: 'ELITE_EXCLUSIVE',
          isActive: true,
          maxMembers: 100,
          createdById: organizer.id
        }
      });

      // Create Elite Pro exclusive chat room
      const eliteProRoom = await prisma.chatRoom.create({
        data: {
          eventId: event.id,
          name: `Elite Pro VIP Lounge - ${event.title}`,
          description: 'VIP networking space exclusively for Elite Pro members',
          roomType: 'ELITE_PRO_EXCLUSIVE',
          isActive: true,
          maxMembers: 50,
          createdById: organizer.id
        }
      });

      createdChatRooms.push({ elite: eliteRoom, elitePro: eliteProRoom, event });
      console.log(`Created chat rooms for event: ${event.title}`);
    }

    // Add members to chat rooms based on their tier
    console.log('Adding members to chat rooms...');

    for (const roomData of createdChatRooms) {
      const eventProfiles = createdProfiles.filter(p => p.event.id === roomData.event.id);

      // Add Elite and Elite Pro members to Elite room
      const eliteMembers = eventProfiles.filter(p => p.tier === 'ELITE' || p.tier === 'ELITE_PRO');
      for (const member of eliteMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elite.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }

      // Add only Elite Pro members to Elite Pro room
      const eliteProMembers = eventProfiles.filter(p => p.tier === 'ELITE_PRO');
      for (const member of eliteProMembers) {
        await prisma.chatRoomMember.create({
          data: {
            chatRoomId: roomData.elitePro.id,
            userId: member.user.id,
            joinedAt: new Date(),
            isModerator: false,
            isMuted: false
          }
        });
      }
    }

    // Create sample messages between users
    console.log('Creating sample messages...');

    const sampleMessages = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Marcus! I noticed your interest in fintech startups. I\'m working on an AI-powered financial analytics platform and would love to discuss potential collaboration opportunities.',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Hi Sarah! That sounds fascinating. I\'d definitely be interested in learning more about your platform. Are you available for a quick call this week?',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Jennifer, your background in data science is impressive! We\'re looking to implement predictive analytics in our healthcare platform. Would you be open to discussing a potential consulting opportunity?',
        eventIndex: 2
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Lisa, I saw your presentation on growth strategies. Our agency is working with several startups that could benefit from your expertise. Let\'s connect!',
        eventIndex: 1
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        content: 'Sarah, with AI becoming more prevalent, cybersecurity is crucial. I\'d love to discuss how we can help secure your AI platform. Are you free for a meeting?',
        eventIndex: 0
      }
    ];

    for (const messageData of sampleMessages) {
      const sender = createdUsers.find(u => u.email === messageData.senderEmail);
      const receiver = createdUsers.find(u => u.email === messageData.receiverEmail);
      const event = createdEvents[messageData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          await prisma.message.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              content: messageData.content,
              messageType: 'TEXT',
              isRead: Math.random() > 0.5,
              readAt: Math.random() > 0.5 ? new Date() : null
            }
          });
        }
      }
    }

    // Create sample meeting requests
    console.log('Creating sample meeting requests...');

    const sampleMeetingRequests = [
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'AI Fintech Collaboration Discussion',
        description: 'Let\'s discuss potential collaboration opportunities between our AI platform and your fintech investments.',
        meetingType: 'VIRTUAL',
        eventIndex: 0
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'Healthcare Analytics Consulting',
        description: 'Exploring data science consulting opportunities for our healthcare platform.',
        meetingType: 'VIRTUAL',
        eventIndex: 2
      },
      {
        senderEmail: '<EMAIL>',
        receiverEmail: '<EMAIL>',
        title: 'Renewable Energy Strategy Session',
        description: 'Strategic planning session for renewable energy business growth.',
        meetingType: 'IN_PERSON',
        eventIndex: 1
      }
    ];

    for (const meetingData of sampleMeetingRequests) {
      const sender = createdUsers.find(u => u.email === meetingData.senderEmail);
      const receiver = createdUsers.find(u => u.email === meetingData.receiverEmail);
      const event = createdEvents[meetingData.eventIndex];

      if (sender && receiver && event) {
        const senderProfile = createdProfiles.find(p => p.user.id === sender.id && p.event.id === event.id);
        const receiverProfile = createdProfiles.find(p => p.user.id === receiver.id && p.event.id === event.id);

        if (senderProfile && receiverProfile) {
          const proposedStartTime = new Date(event.startDate.getTime() + Math.random() * 7 * 24 * 60 * 60 * 1000);
          const proposedEndTime = new Date(proposedStartTime.getTime() + 60 * 60 * 1000); // 1 hour meeting

          await prisma.meetingRequest.create({
            data: {
              senderId: senderProfile.profile.id,
              receiverId: receiverProfile.profile.id,
              eventId: event.id,
              title: meetingData.title,
              description: meetingData.description,
              proposedStartTime,
              proposedEndTime,
              timezone: senderProfile.user.userData.timezone,
              meetingType: meetingData.meetingType as any,
              meetingUrl: meetingData.meetingType === 'VIRTUAL' ? 'https://meet.google.com/sample-meeting' : null,
              location: meetingData.meetingType === 'IN_PERSON' ? event.location : null,
              status: Math.random() > 0.5 ? 'PENDING' : 'ACCEPTED'
            }
          });
        }
      }
    }

    // Add some chat room messages
    console.log('Creating chat room messages...');

    const chatRoomMessages = [
      'Welcome to the Elite networking space! Looking forward to connecting with everyone.',
      'Excited to be here! Anyone interested in discussing the latest AI trends?',
      'Great event so far! The keynote was particularly insightful.',
      'Looking for potential collaboration partners in the fintech space.',
      'Happy to share insights about digital transformation in healthcare.',
      'Anyone attending the networking session later today?'
    ];

    for (const roomData of createdChatRooms) {
      const eventProfiles = createdProfiles.filter(p => p.event.id === roomData.event.id);
      const eliteMembers = eventProfiles.filter(p => p.tier === 'ELITE' || p.tier === 'ELITE_PRO');

      // Add a few messages to each Elite room
      for (let i = 0; i < Math.min(3, eliteMembers.length); i++) {
        const randomMember = eliteMembers[Math.floor(Math.random() * eliteMembers.length)];
        const randomMessage = chatRoomMessages[Math.floor(Math.random() * chatRoomMessages.length)];

        await prisma.message.create({
          data: {
            senderId: randomMember.profile.id,
            receiverId: randomMember.profile.id, // For direct messages between members
            eventId: roomData.event.id,
            content: randomMessage,
            messageType: 'TEXT',
            isRead: true
          }
        });
      }
    }

    console.log('Elite Communication System seeding completed successfully!');
    console.log(`Created:
    - ${createdUsers.length} sample users
    - ${createdEvents.length} sample events
    - ${createdProfiles.length} attendee profiles with Elite subscriptions
    - ${createdChatRooms.length * 2} chat rooms (Elite and Elite Pro)
    - ${sampleMessages.length} direct messages
    - ${sampleMeetingRequests.length} meeting requests
    - Multiple chat room messages`);

  } catch (error) {
    console.error('Error seeding Elite Communication System:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Error during Elite Communication seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

