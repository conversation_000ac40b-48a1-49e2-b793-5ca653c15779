import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { FeaturingStatus, FeaturingTier } from '@prisma/client';

/**
 * GET /api/admin/featuring
 * Get featured events data for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const skip = (page - 1) * pageSize;

    // Build where clause based on status filter
    const where: any = {};
    
    if (status === 'active') {
      where.status = 'ACTIVE';
    } else if (status === 'pending') {
      where.status = 'PENDING';
    } else if (status === 'expired') {
      where.status = 'EXPIRED';
    }

    // Get featured events with pagination
    const featurings = await db.eventFeaturing.findMany({
      where,
      include: {
        event: {
          select: {
            id: true,
            title: true,
            startDate: true,
            endDate: true,
            imagePath: true,
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
        },
      },
      orderBy: [
        { status: 'asc' },
        { endDate: 'asc' },
      ],
      skip,
      take: pageSize,
    });

    // Get total counts for each status
    const [activeCount, pendingCount, expiredCount, totalCount] = await Promise.all([
      db.eventFeaturing.count({ where: { status: 'ACTIVE' } }),
      db.eventFeaturing.count({ where: { status: 'PENDING' } }),
      db.eventFeaturing.count({ where: { status: 'EXPIRED' } }),
      db.eventFeaturing.count({}),
    ]);

    // Calculate total revenue from featuring
    const currentMonth = new Date();
    const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
    const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);

    const monthlyRevenue = await db.eventFeaturing.aggregate({
      where: {
        createdAt: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
        status: {
          in: ['ACTIVE', 'EXPIRED'],
        },
      },
      _sum: {
        paymentAmount: true,
      },
    });

    // Calculate average conversion rate
    // For this example, we'll use a placeholder value
    // In a real implementation, you would calculate this based on actual data
    const averageConversionRate = 8.7;

    // Format the response
    const formattedFeaturings = featurings.map(featuring => {
      // Calculate impressions (placeholder for demo)
      const daysSinceFeatured = Math.max(
        1, 
        Math.floor((new Date().getTime() - featuring.startDate.getTime()) / (1000 * 60 * 60 * 24))
      );
      const impressions = featuring.status === 'ACTIVE' ? 
        Math.floor(daysSinceFeatured * 100 + Math.random() * 1000) : 
        Math.floor(Math.random() * 10000);

      return {
        id: featuring.id,
        eventId: featuring.eventId,
        eventTitle: featuring.event.title,
        tier: featuring.tier,
        startDate: featuring.startDate.toISOString(),
        endDate: featuring.endDate.toISOString(),
        status: featuring.status,
        paymentAmount: featuring.paymentAmount.toString(),
        organizer: featuring.event.user.name,
        organizerId: featuring.event.user.id,
        impressions,
        imageUrl: featuring.event.imagePath ? 
          `${process.env.NEXT_PUBLIC_APP_URL}${featuring.event.imagePath}` : 
          null,
      };
    });

    return NextResponse.json({
      featurings: formattedFeaturings,
      stats: {
        activeCount,
        pendingCount,
        expiredCount,
        totalCount,
        monthlyRevenue: monthlyRevenue._sum.paymentAmount?.toString() || '0',
        averageConversionRate,
      },
      pagination: {
        page,
        pageSize,
        totalPages: Math.ceil(
          status === 'active' ? activeCount : 
          status === 'pending' ? pendingCount :
          status === 'expired' ? expiredCount :
          totalCount
        / pageSize),
      },
    });
  } catch (error) {
    console.error('Error fetching admin featuring data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featuring data' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/featuring
 * Update featuring status (approve/reject)
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { featuringId, action } = body;

    if (!featuringId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get the featuring record
    const featuring = await db.eventFeaturing.findUnique({
      where: { id: featuringId },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            userId: true,
          },
        },
      },
    });

    if (!featuring) {
      return NextResponse.json(
        { error: 'Featuring record not found' },
        { status: 404 }
      );
    }

    // Update the featuring status based on the action
    if (action === 'approve') {
      // Update featuring status to ACTIVE
      await db.eventFeaturing.update({
        where: { id: featuringId },
        data: { status: 'ACTIVE' as FeaturingStatus },
      });

      // Update event metadata
      await db.event.update({
        where: { id: featuring.eventId },
        data: {
          metadata: {
            isFeatured: true,
            featuredUntil: featuring.endDate.toISOString(),
            featuredTier: featuring.tier,
          },
        },
      });

      // Create notification for the event owner
      await db.notification.create({
        data: {
          userId: featuring.event.userId,
          message: `Your event "${featuring.event.title}" has been approved for featuring and is now live on the platform.`,
          type: 'FEATURING_APPROVED',
          isRead: false,
        },
      });
    } else if (action === 'reject') {
      // Update featuring status to CANCELLED
      await db.eventFeaturing.update({
        where: { id: featuringId },
        data: { status: 'CANCELLED' as FeaturingStatus },
      });

      // Create notification for the event owner
      await db.notification.create({
        data: {
          userId: featuring.event.userId,
          message: `Your request to feature "${featuring.event.title}" has been rejected. Please contact support for more information.`,
          type: 'FEATURING_REJECTED',
          isRead: false,
        },
      });
    } else {
      return NextResponse.json(
        { error: 'Invalid action' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: `Featuring ${action === 'approve' ? 'approved' : 'rejected'} successfully`,
    });
  } catch (error) {
    console.error('Error updating featuring status:', error);
    return NextResponse.json(
      { error: 'Failed to update featuring status' },
      { status: 500 }
    );
  }
}
