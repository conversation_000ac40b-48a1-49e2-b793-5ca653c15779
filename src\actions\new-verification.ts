'use server'

import { db } from "@/lib/prisma"
import { getUserByEmail } from "@/data/user"
import { getVerificationTokenByToken } from "@/data/verification-token"
import crypto from 'crypto'

export async function newVerification(token: string) {
  const existingToken = await getVerificationTokenByToken(token)

  if (!existingToken) {
    return { error: 'Token does not exists!' }
  }

  const tokenHasExpired = new Date(existingToken.expires) < new Date()

  if (tokenHasExpired) {
    return { error: 'Token has expired!' }
  }

  const existingUser = await getUserByEmail(existingToken.email)

  if (!existingUser) {
    return { error: 'Email does not exists!' }
  }

  // Generate a random access token
  const accessToken = crypto.randomBytes(32).toString('hex');

  // Update user with email verification and access token
  await db.user.update({
    where: { id: existingUser.id },
    data: {
      emailVerified: new Date(),
      email: existingToken.email,
      accessToken: accessToken
    }
  })

  console.log(`Email verified and access token generated for user ${existingUser.id}`);

  await db.verificationToken.delete({
    where: { id: existingToken.id }
  })

  return { success: 'Email verified! Your account is now fully activated with API access.' }
}