import { NextRequest, NextResponse } from 'next/server';
import { FeeType } from '@prisma/client';
import { currentUser } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';

// Create a new PrismaClient instance for this route
const prisma = new PrismaClient();

/**
 * GET /api/admin/fees
 * Get all fee configurations
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For now, return default fee configurations based on the business model
    const defaultFees = [
      {
        id: 'platform-commission-fee',
        name: 'Platform Commission',
        description: 'Platform commission fee for ticket sales',
        feeType: 'PLATFORM_COMMISSION',
        value: 6.0,
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE'],
        effectiveFrom: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'organizer-processing-fee',
        name: 'Organizer Processing Fee',
        description: 'Payment processing fee for organizers',
        feeType: 'PROCESSING_FEE',
        value: 3.5,
        isPercentage: true,
        isActive: true,
        appliesTo: ['ORGANIZER'],
        transactionType: ['TICKET_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'vendor-processing-fee',
        name: 'Vendor Processing Fee',
        description: 'Payment processing fee for vendors',
        feeType: 'PROCESSING_FEE',
        value: 3.5,
        isPercentage: true,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['VENDOR_SALE', 'PROCESSING_FEE'],
        effectiveFrom: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'pos-rental-fee',
        name: 'POS Rental Fee',
        description: 'Flat fee for POS device rental',
        feeType: 'POS_RENTAL_FEE',
        value: 1000,
        isPercentage: false,
        isActive: true,
        appliesTo: ['VENDOR'],
        transactionType: ['POS_RENTAL_FEE'],
        effectiveFrom: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    return NextResponse.json(defaultFees);
  } catch (error) {
    console.error('Error getting fee configurations:', error);
    return NextResponse.json(
      { error: 'Failed to get fee configurations' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * POST /api/admin/fees
 * Create a new fee configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      name,
      description,
      feeType,
      value,
      isPercentage,
      isActive,
      appliesTo,
      transactionType,
      minAmount,
      maxAmount,
      effectiveFrom,
      effectiveUntil
    } = body;

    // Validate required fields
    if (!name || !feeType || value === undefined || !appliesTo || !transactionType) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create a mock fee configuration response
    const feeConfig = {
      id: `fee-${Date.now()}`,
      name,
      description,
      feeType,
      value: parseFloat(value.toString()),
      isPercentage: isPercentage !== undefined ? isPercentage : true,
      isActive: isActive !== undefined ? isActive : true,
      appliesTo,
      transactionType,
      minAmount: minAmount ? parseFloat(minAmount.toString()) : null,
      maxAmount: maxAmount ? parseFloat(maxAmount.toString()) : null,
      effectiveFrom: effectiveFrom ? new Date(effectiveFrom).toISOString() : new Date().toISOString(),
      effectiveUntil: effectiveUntil ? new Date(effectiveUntil).toISOString() : null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      createdBy: user.id
    };

    return NextResponse.json(feeConfig, { status: 201 });
  } catch (error) {
    console.error('Error creating fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create fee configuration' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * PATCH /api/admin/fees?id=:id
 * Update a fee configuration
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Fee ID is required' }, { status: 400 });
    }

    // Parse request body
    const body = await request.json();
    const {
      name,
      description,
      value,
      isPercentage,
      isActive,
      appliesTo,
      transactionType,
      minAmount,
      maxAmount,
      effectiveFrom,
      effectiveUntil
    } = body;

    // Create a mock updated fee configuration
    const feeConfig = {
      id,
      name: name || 'Updated Fee',
      description: description || 'Updated description',
      feeType: 'PLATFORM_COMMISSION',
      value: value !== undefined ? parseFloat(value.toString()) : 6.0,
      isPercentage: isPercentage !== undefined ? isPercentage : true,
      isActive: isActive !== undefined ? isActive : true,
      appliesTo: appliesTo || ['ORGANIZER'],
      transactionType: transactionType || ['TICKET_SALE'],
      minAmount: minAmount ? parseFloat(minAmount.toString()) : null,
      maxAmount: maxAmount ? parseFloat(maxAmount.toString()) : null,
      effectiveFrom: effectiveFrom ? new Date(effectiveFrom).toISOString() : new Date().toISOString(),
      effectiveUntil: effectiveUntil ? new Date(effectiveUntil).toISOString() : null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(feeConfig);
  } catch (error) {
    console.error('Error updating fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to update fee configuration' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * DELETE /api/admin/fees?id=:id
 * Delete a fee configuration
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Fee ID is required' }, { status: 400 });
    }

    // Mock successful deletion
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting fee configuration:', error);
    return NextResponse.json(
      { error: 'Failed to delete fee configuration' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}
