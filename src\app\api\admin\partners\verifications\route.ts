import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/partners/verifications
 * Get partner verification requests for admin review
 */
export async function GET(req: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can access verification data
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get URL parameters
    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search');

    // Build the where clause
    const where: any = {};

    if (search) {
      where.OR = [
        {
          businessName: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ];
    }

    // Get partners that need verification (unverified partners)
    const verifications = await db.partner.findMany({
      where: {
        ...where,
        isVerified: false
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    // Format verifications for response
    const formattedVerifications = verifications.map(partner => ({
      id: partner.id,
      businessName: partner.businessName,
      partnerType: partner.partnerType,
      submittedAt: partner.createdAt.toISOString(),
      status: 'PENDING' as const,
      documents: {
        // These fields don't exist in the current schema, so we'll use empty objects for now
        businessLicense: undefined,
        taxCertificate: undefined,
        ownershipProof: undefined
      },
      user: {
        id: partner.user.id,
        name: partner.user.name,
        email: partner.user.email
      },
      notes: undefined // This field doesn't exist in current schema
    }));

    return NextResponse.json({
      verifications: formattedVerifications
    });

  } catch (error) {
    console.error('Error fetching verifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
