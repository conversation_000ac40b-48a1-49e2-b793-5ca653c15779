/**
 * API Usage Data Inserter
 * 
 * This script inserts the generated API usage data into the database.
 * It reads the data from the api-usage-log.json file and inserts it into the ApiKeyUsage table.
 * 
 * Usage:
 * node insert-api-data.js
 */

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();
const LOG_FILE = path.join(__dirname, 'api-usage-log.json');

// Function to read logs from file
function readLogs() {
  try {
    const data = fs.readFileSync(LOG_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading logs:', error);
    return [];
  }
}

// Function to create API keys in the database
async function createApiKeys(logs) {
  console.log('Creating API keys in the database...');
  
  // Get unique API keys from logs
  const uniqueApiKeys = {};
  logs.forEach(log => {
    if (log.apiKeyId && log.apiKeyName) {
      uniqueApiKeys[log.apiKeyId] = {
        id: log.apiKeyId,
        name: log.apiKeyName,
        key: `key_${Math.random().toString(36).substring(2, 15)}`,
        rateLimit: parseInt(log.rateLimit) || 100,
        permissions: ['read:events', 'read:tickets', 'read:orders'],
      };
    }
  });
  
  const apiKeys = Object.values(uniqueApiKeys);
  
  // Get a user ID from the database
  const user = await prisma.user.findFirst({
    select: { id: true },
  });
  
  if (!user) {
    console.error('No users found in the database');
    return [];
  }
  
  // Create API keys in the database
  const createdKeys = [];
  
  for (const apiKey of apiKeys) {
    try {
      const created = await prisma.apiKey.create({
        data: {
          id: apiKey.id,
          name: apiKey.name,
          key: apiKey.key,
          rateLimit: apiKey.rateLimit,
          permissions: apiKey.permissions,
          userId: user.id,
          expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        },
      });
      
      createdKeys.push(created);
      console.log(`Created API key: ${created.name}`);
    } catch (error) {
      console.error(`Error creating API key ${apiKey.name}:`, error);
    }
  }
  
  return createdKeys;
}

// Function to insert logs into the database
async function insertLogs(logs, apiKeys) {
  console.log('Inserting logs into the database...');
  
  // Create a map of API key IDs to database IDs
  const apiKeyMap = {};
  apiKeys.forEach(key => {
    apiKeyMap[key.id] = key.id;
  });
  
  // Insert logs in batches
  const BATCH_SIZE = 100;
  let inserted = 0;
  
  for (let i = 0; i < logs.length; i += BATCH_SIZE) {
    const batch = logs.slice(i, i + BATCH_SIZE);
    const data = batch.map(log => ({
      apiKeyId: apiKeyMap[log.apiKeyId] || apiKeys[0].id,
      endpoint: log.endpoint,
      method: log.method,
      status: log.status,
      timestamp: new Date(log.timestamp),
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
    }));
    
    try {
      const result = await prisma.apiKeyUsage.createMany({
        data,
        skipDuplicates: true,
      });
      
      inserted += result.count;
      console.log(`Inserted ${result.count} logs (${inserted}/${logs.length})`);
    } catch (error) {
      console.error('Error inserting logs:', error);
    }
  }
  
  return inserted;
}

// Main function
async function main() {
  try {
    // Read logs from file
    const logs = readLogs();
    
    if (logs.length === 0) {
      console.error('No logs found');
      return;
    }
    
    console.log(`Read ${logs.length} logs from ${LOG_FILE}`);
    
    // Create API keys in the database
    const apiKeys = await createApiKeys(logs);
    
    if (apiKeys.length === 0) {
      console.error('No API keys created');
      return;
    }
    
    // Insert logs into the database
    const inserted = await insertLogs(logs, apiKeys);
    
    console.log(`Inserted ${inserted} logs into the database`);
    console.log('Done!');
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main();
