import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/stats
 * Get NFC system statistics for admin dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get total cards count
    const totalCards = await db.nFCCard.count();

    // Get active cards count
    const activeCards = await db.nFCCard.count({
      where: { isActive: true }
    });

    // Get total transactions count
    const totalTransactions = await db.vendorNFCTransaction.count();

    // Calculate total revenue
    const revenueResult = await db.vendorNFCTransaction.aggregate({
      _sum: {
        amount: true
      },
      where: {
        status: 'COMPLETED'
      }
    });
    const totalRevenue = revenueResult._sum.amount || 0;

    // Get active events with NFC system
    // First get all events with NFC system settings
    const nfcSettings = await db.nFCSystemSettings.findMany({
      select: {
        eventId: true
      }
    });

    // Then count published events that have NFC settings
    const activeEvents = await db.event.count({
      where: {
        id: {
          in: nfcSettings.map(setting => setting.eventId)
        },
        status: 'Published'
      }
    });

    // Get active vendors count
    const activeVendors = await db.vendorProfile.count({
      where: {
        verificationStatus: 'APPROVED'
      }
    });

    // Get recent transactions
    const recentTransactions = await db.vendorNFCTransaction.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        },
        vendor: {
          select: {
            id: true,
            businessName: true
          }
        },
        user: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    // Get top events by revenue
    // Get event IDs that have NFC system settings
    const eventsWithNfc = await db.nFCSystemSettings.findMany({
      select: {
        eventId: true
      }
    });

    const topEvents = await db.event.findMany({
      take: 5,
      where: {
        id: {
          in: eventsWithNfc.map(setting => setting.eventId)
        }
      },
      select: {
        id: true,
        title: true,
        _count: {
          select: {
            nfcTransactions: true
          }
        }
      },
      orderBy: {
        nfcTransactions: {
          _count: 'desc'
        }
      }
    });

    // Get top vendors by revenue
    const topVendors = await db.vendorProfile.findMany({
      take: 5,
      select: {
        id: true,
        businessName: true,
        _count: {
          select: {
            nfcTransactions: true
          }
        }
      },
      orderBy: {
        nfcTransactions: {
          _count: 'desc'
        }
      }
    });

    // Format the data for the frontend
    const formattedTransactions = recentTransactions.map(tx => ({
      id: tx.id,
      cardId: tx.cardId,
      amount: tx.amount,
      status: tx.status,
      timestamp: tx.createdAt.toISOString(),
      event: {
        id: tx.event.id,
        name: tx.event.title
      },
      vendor: {
        id: tx.vendor.id,
        name: tx.vendor.businessName
      },
      user: {
        id: tx.user.id,
        name: tx.user.name
      }
    }));

    const formattedEvents = topEvents.map(event => ({
      id: event.id,
      name: event.title,
      transactions: event._count.nfcTransactions,
      // We'll need to calculate this in a more complex query in a real implementation
      revenue: event._count.nfcTransactions * 15 // Placeholder average transaction value
    }));

    const formattedVendors = topVendors.map(vendor => ({
      id: vendor.id,
      name: vendor.businessName,
      transactions: vendor._count.nfcTransactions,
      // We'll need to calculate this in a more complex query in a real implementation
      revenue: vendor._count.nfcTransactions * 15 // Placeholder average transaction value
    }));

    return NextResponse.json({
      totalCards,
      activeCards,
      totalTransactions,
      totalRevenue,
      activeEvents,
      activeVendors,
      recentTransactions: formattedTransactions,
      topEvents: formattedEvents,
      topVendors: formattedVendors
    });
  } catch (error) {
    console.error('Error fetching NFC stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC system statistics' },
      { status: 500 }
    );
  }
}
