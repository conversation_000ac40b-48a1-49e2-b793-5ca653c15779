-- CreateEnum for Partner Product Types
CREATE TYPE "PartnerProductType" AS ENUM ('PRODUCT', 'SERVICE', 'PACKAGE', 'EXPERIENCE');

-- CreateEnum for Partner Product Status
CREATE TYPE "PartnerProductStatus" AS ENUM ('DRAFT', 'ACTIVE', 'INACTIVE', 'OUT_OF_STOCK');

-- CreateTable for Partner Products/Services
CREATE TABLE "PartnerProduct" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "shortDescription" TEXT,
    "type" "PartnerProductType" NOT NULL DEFAULT 'PRODUCT',
    "category" TEXT NOT NULL,
    "subcategory" TEXT,
    "price" DOUBLE PRECISION NOT NULL,
    "originalPrice" DOUBLE PRECISION,
    "currency" TEXT NOT NULL DEFAULT 'ZMW',
    "imageUrl" TEXT,
    "galleryImages" JSONB,
    "status" "PartnerProductStatus" NOT NULL DEFAULT 'DRAFT',
    "isAvailable" BOOLEAN NOT NULL DEFAULT true,
    "isFeatured" BOOLEAN NOT NULL DEFAULT false,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "stockQuantity" INTEGER,
    "minOrderQuantity" INTEGER DEFAULT 1,
    "maxOrderQuantity" INTEGER,
    "duration" INTEGER,
    "durationUnit" TEXT,
    "location" TEXT,
    "inclusions" TEXT[],
    "exclusions" TEXT[],
    "requirements" TEXT[],
    "tags" TEXT[],
    "seoTitle" TEXT,
    "seoDescription" TEXT,
    "seoKeywords" TEXT[],
    "rating" DOUBLE PRECISION DEFAULT 0,
    "totalReviews" INTEGER NOT NULL DEFAULT 0,
    "totalSales" INTEGER NOT NULL DEFAULT 0,
    "viewCount" INTEGER NOT NULL DEFAULT 0,
    "sortOrder" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerProduct_pkey" PRIMARY KEY ("id")
);

-- CreateTable for Partner Product Reviews
CREATE TABLE "PartnerProductReview" (
    "id" TEXT NOT NULL,
    "productId" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "rating" DOUBLE PRECISION NOT NULL,
    "title" TEXT,
    "comment" TEXT,
    "isPublished" BOOLEAN NOT NULL DEFAULT true,
    "isVerified" BOOLEAN NOT NULL DEFAULT false,
    "helpfulCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerProductReview_pkey" PRIMARY KEY ("id")
);

-- CreateTable for Partner Product Categories
CREATE TABLE "PartnerProductCategory" (
    "id" TEXT NOT NULL,
    "partnerId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "imageUrl" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "sortOrder" INTEGER DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PartnerProductCategory_pkey" PRIMARY KEY ("id")
);

-- Add indexes for better performance
CREATE INDEX "PartnerProduct_partnerId_idx" ON "PartnerProduct"("partnerId");
CREATE INDEX "PartnerProduct_type_idx" ON "PartnerProduct"("type");
CREATE INDEX "PartnerProduct_category_idx" ON "PartnerProduct"("category");
CREATE INDEX "PartnerProduct_status_idx" ON "PartnerProduct"("status");
CREATE INDEX "PartnerProduct_isFeatured_idx" ON "PartnerProduct"("isFeatured");
CREATE INDEX "PartnerProduct_isPopular_idx" ON "PartnerProduct"("isPopular");
CREATE INDEX "PartnerProduct_price_idx" ON "PartnerProduct"("price");
CREATE INDEX "PartnerProduct_rating_idx" ON "PartnerProduct"("rating");
CREATE INDEX "PartnerProduct_createdAt_idx" ON "PartnerProduct"("createdAt");

CREATE INDEX "PartnerProductReview_productId_idx" ON "PartnerProductReview"("productId");
CREATE INDEX "PartnerProductReview_userId_idx" ON "PartnerProductReview"("userId");
CREATE INDEX "PartnerProductReview_rating_idx" ON "PartnerProductReview"("rating");
CREATE INDEX "PartnerProductReview_isPublished_idx" ON "PartnerProductReview"("isPublished");

CREATE INDEX "PartnerProductCategory_partnerId_idx" ON "PartnerProductCategory"("partnerId");
CREATE INDEX "PartnerProductCategory_isActive_idx" ON "PartnerProductCategory"("isActive");

-- Add foreign key constraints
ALTER TABLE "PartnerProduct" ADD CONSTRAINT "PartnerProduct_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "PartnerProductReview" ADD CONSTRAINT "PartnerProductReview_productId_fkey" FOREIGN KEY ("productId") REFERENCES "PartnerProduct"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "PartnerProductReview" ADD CONSTRAINT "PartnerProductReview_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "PartnerProductCategory" ADD CONSTRAINT "PartnerProductCategory_partnerId_fkey" FOREIGN KEY ("partnerId") REFERENCES "Partner"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Add unique constraints
ALTER TABLE "PartnerProductReview" ADD CONSTRAINT "PartnerProductReview_productId_userId_key" UNIQUE ("productId", "userId");
ALTER TABLE "PartnerProductCategory" ADD CONSTRAINT "PartnerProductCategory_partnerId_name_key" UNIQUE ("partnerId", "name");
