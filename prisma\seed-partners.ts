import { PrismaClient, PartnerType, PartnershipTier } from '@prisma/client';
import { hash } from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed partners...');

  // Create test users for partners if they don't exist
  const users = [
    {
      name: 'Grand Hotel',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Tasty Restaurant',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Downtown Bar',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
    {
      name: 'Nightlife Club',
      email: '<EMAIL>',
      password: 'Password123!',
      role: 'USER',
    },
  ];

  for (const userData of users) {
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email },
    });

    if (!existingUser) {
      const hashedPassword = await hash(userData.password, 10);
      await prisma.user.create({
        data: {
          name: userData.name,
          email: userData.email,
          password: hashedPassword,
          role: userData.role as any,
        },
      });
      console.log(`Created user: ${userData.email}`);
    } else {
      console.log(`User already exists: ${userData.email}`);
    }
  }

  // Get the created users
  const hotelUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const restaurantUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const barUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });
  const nightclubUser = await prisma.user.findUnique({ where: { email: '<EMAIL>' } });

  if (!hotelUser || !restaurantUser || !barUser || !nightclubUser) {
    throw new Error('Failed to create or find users');
  }

  // Create partners if they don't exist
  const partners = [
    {
      userId: hotelUser.id,
      businessName: 'Grand Hotel',
      partnerType: PartnerType.HOTEL,
      tier: PartnershipTier.PREMIUM,
      description: 'A luxurious hotel in the heart of the city with top-notch amenities and services.',
      address: '123 Main Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10101',
      country: 'Zambia',
      contactName: 'John Manager',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 1234567',
      website: 'https://grandhotel.example.com',
      amenities: ['Free Wi-Fi', 'Swimming Pool', 'Restaurant', 'Gym', 'Conference Rooms'],
      priceRange: '$$$',
      isVerified: true,
      featured: true,
      acceptsNfcPayments: true,
      commissionRate: 5.0,
    },
    {
      userId: restaurantUser.id,
      businessName: 'Tasty Restaurant',
      partnerType: PartnerType.RESTAURANT,
      tier: PartnershipTier.BASIC,
      description: 'A family-friendly restaurant serving delicious local and international cuisine.',
      address: '456 Food Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10102',
      country: 'Zambia',
      contactName: 'Sarah Chef',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 2345678',
      website: 'https://tastyrestaurant.example.com',
      amenities: ['Outdoor Seating', 'Private Dining', 'Takeaway', 'Delivery'],
      priceRange: '$$',
      isVerified: true,
      featured: false,
      acceptsNfcPayments: true,
      commissionRate: 4.5,
    },
    {
      userId: barUser.id,
      businessName: 'Downtown Bar',
      partnerType: PartnerType.BAR,
      tier: PartnershipTier.ELITE,
      description: 'A trendy bar with a wide selection of drinks and a vibrant atmosphere.',
      address: '789 Drink Avenue',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10103',
      country: 'Zambia',
      contactName: 'Mike Bartender',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 3456789',
      website: 'https://downtownbar.example.com',
      amenities: ['Live Music', 'Happy Hour', 'Cocktails', 'Snacks'],
      priceRange: '$$',
      isVerified: true,
      featured: true,
      acceptsNfcPayments: true,
      commissionRate: 6.0,
    },
    {
      userId: nightclubUser.id,
      businessName: 'Nightlife Club',
      partnerType: PartnerType.NIGHTCLUB,
      tier: PartnershipTier.PREMIUM,
      description: 'The hottest nightclub in town with top DJs and an amazing dance floor.',
      address: '101 Party Street',
      city: 'Lusaka',
      province: 'Lusaka',
      postalCode: '10104',
      country: 'Zambia',
      contactName: 'David DJ',
      contactEmail: '<EMAIL>',
      contactPhone: '+260 97 4567890',
      website: 'https://nightlifeclub.example.com',
      amenities: ['VIP Tables', 'Dance Floor', 'Premium Drinks', 'Security'],
      priceRange: '$$$',
      isVerified: false,
      featured: false,
      acceptsNfcPayments: false,
      commissionRate: 7.0,
    },
  ];

  for (const partnerData of partners) {
    const existingPartner = await prisma.partner.findUnique({
      where: { userId: partnerData.userId },
    });

    if (!existingPartner) {
      await prisma.partner.create({
        data: {
          userId: partnerData.userId,
          businessName: partnerData.businessName,
          partnerType: partnerData.partnerType,
          tier: partnerData.tier,
          description: partnerData.description,
          address: partnerData.address,
          city: partnerData.city,
          province: partnerData.province,
          postalCode: partnerData.postalCode,
          country: partnerData.country,
          contactName: partnerData.contactName,
          contactEmail: partnerData.contactEmail,
          contactPhone: partnerData.contactPhone,
          website: partnerData.website,
          amenities: partnerData.amenities,
          priceRange: partnerData.priceRange,
          isVerified: partnerData.isVerified,
          verifiedAt: partnerData.isVerified ? new Date() : null,
          featured: partnerData.featured,
          acceptsNfcPayments: partnerData.acceptsNfcPayments,
          commissionRate: partnerData.commissionRate,
        },
      });
      console.log(`Created partner: ${partnerData.businessName}`);
    } else {
      console.log(`Partner already exists: ${partnerData.businessName}`);
    }
  }

  // Get the created partners
  const hotelPartner = await prisma.partner.findUnique({ where: { userId: hotelUser.id } });
  const restaurantPartner = await prisma.partner.findUnique({ where: { userId: restaurantUser.id } });
  const barPartner = await prisma.partner.findUnique({ where: { userId: barUser.id } });
  const nightclubPartner = await prisma.partner.findUnique({ where: { userId: nightclubUser.id } });

  if (!hotelPartner || !restaurantPartner || !barPartner || !nightclubPartner) {
    throw new Error('Failed to create or find partners');
  }

  // Create promotions if they don't exist
  const now = new Date();
  const oneMonthAgo = new Date(now);
  oneMonthAgo.setMonth(now.getMonth() - 1);
  
  const oneMonthFromNow = new Date(now);
  oneMonthFromNow.setMonth(now.getMonth() + 1);
  
  const twoMonthsFromNow = new Date(now);
  twoMonthsFromNow.setMonth(now.getMonth() + 2);

  const promotions = [
    {
      partnerId: hotelPartner.id,
      title: '20% Off All Rooms',
      description: 'Get 20% off all room bookings for the weekend. Valid for bookings made through our website or app.',
      startDate: now,
      endDate: oneMonthFromNow,
      discountValue: 20,
      discountType: 'percentage',
      promoCode: 'WEEKEND20',
      isActive: true,
      maxUses: 100,
      currentUses: 45,
    },
    {
      partnerId: restaurantPartner.id,
      title: 'Free Drink with Dinner',
      description: 'Get a free drink with any dinner purchase. Valid for dine-in only.',
      startDate: oneMonthAgo,
      endDate: now,
      isActive: false,
      currentUses: 78,
    },
    {
      partnerId: barPartner.id,
      title: 'Happy Hour Extended',
      description: 'Happy hour extended to 8pm every weekday. Enjoy discounted drinks and appetizers.',
      startDate: now,
      endDate: twoMonthsFromNow,
      isActive: true,
      currentUses: 120,
    },
    {
      partnerId: nightclubPartner.id,
      title: 'VIP Entry Package',
      description: 'Get VIP entry, a reserved table, and a complimentary bottle for groups of 6 or more.',
      startDate: oneMonthFromNow,
      endDate: twoMonthsFromNow,
      discountValue: 15,
      discountType: 'percentage',
      promoCode: 'VIPGROUP',
      isActive: true,
      maxUses: 50,
      currentUses: 0,
    },
  ];

  for (const promotionData of promotions) {
    const existingPromotion = await prisma.partnerPromotion.findFirst({
      where: {
        partnerId: promotionData.partnerId,
        title: promotionData.title,
      },
    });

    if (!existingPromotion) {
      await prisma.partnerPromotion.create({
        data: {
          partnerId: promotionData.partnerId,
          title: promotionData.title,
          description: promotionData.description,
          startDate: promotionData.startDate,
          endDate: promotionData.endDate,
          discountValue: promotionData.discountValue,
          discountType: promotionData.discountType as string,
          promoCode: promotionData.promoCode,
          isActive: promotionData.isActive,
          maxUses: promotionData.maxUses,
          currentUses: promotionData.currentUses,
        },
      });
      console.log(`Created promotion: ${promotionData.title}`);
    } else {
      console.log(`Promotion already exists: ${promotionData.title}`);
    }
  }

  console.log('Seeding partners completed successfully!');
}

main()
  .catch((e) => {
    console.error('Error seeding partners:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
