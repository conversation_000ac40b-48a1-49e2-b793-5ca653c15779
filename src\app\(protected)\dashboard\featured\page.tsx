'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Calendar, Clock, MapPin, Star, AlertCircle, Info, TrendingUp, BarChart3, Users, Ticket } from 'lucide-react';
import { formatDate, timeRemaining } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';

interface FeaturedEvent {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  venue: string;
  location: string;
  imageUrl?: string;
  featuredUntil: string;
  promotionId: string;
  promotionType: string;
  impressions?: number;
  clicks?: number;
  conversions?: number;
}

export default function FeaturedEventsPage() {
  const [featuredEvents, setFeaturedEvents] = useState<FeaturedEvent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFeaturedEvents = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/dashboard/featured-events');

        if (!response.ok) {
          throw new Error('Failed to fetch featured events');
        }

        const data = await response.json();

        // Add mock analytics data for demonstration
        const eventsWithAnalytics = (data.events || []).map((event: FeaturedEvent) => ({
          ...event,
          impressions: Math.floor(Math.random() * 1000) + 100,
          clicks: Math.floor(Math.random() * 200) + 20,
          conversions: Math.floor(Math.random() * 50) + 5,
        }));

        setFeaturedEvents(eventsWithAnalytics);
      } catch (err) {
        console.error('Error fetching featured events:', err);
        setError('Unable to load featured events');
      } finally {
        setLoading(false);
      }
    };

    fetchFeaturedEvents();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto py-4">
        <h1 className="text-3xl font-bold mb-6">Featured Events</h1>
        <div className="grid grid-cols-1 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-4">
        <h1 className="text-3xl font-bold mb-6">Featured Events</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (featuredEvents.length === 0) {
    return (
      <div className="container mx-auto py-4">
        <h1 className="text-3xl font-bold mb-6">Featured Events</h1>
        <Card>
          <CardContent className="p-8">
            <div className="flex flex-col items-center justify-center text-center">
              <div className="bg-yellow-50 p-4 rounded-full mb-4">
                <Star className="h-10 w-10 text-yellow-500" />
              </div>
              <h3 className="text-xl font-medium mb-2">No Featured Events</h3>
              <p className="text-gray-500 mb-6 max-w-md">
                Promote your events to increase visibility and attract more attendees
              </p>
              <Link href="/dashboard/events">
                <Button className="bg-yellow-500 hover:bg-yellow-600 text-white">
                  Promote an Event
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate total analytics
  const totalImpressions = featuredEvents.reduce((sum, event) => sum + (event.impressions || 0), 0);
  const totalClicks = featuredEvents.reduce((sum, event) => sum + (event.clicks || 0), 0);
  const totalConversions = featuredEvents.reduce((sum, event) => sum + (event.conversions || 0), 0);
  const averageCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0;
  const averageConversionRate = totalClicks > 0 ? (totalConversions / totalClicks) * 100 : 0;

  return (
    <div className="container mx-auto py-4">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
        <h1 className="text-3xl font-bold">Featured Events</h1>
        <Link href="/dashboard/events">
          <Button className="mt-4 md:mt-0 bg-yellow-500 hover:bg-yellow-600 text-white">
            <Star className="mr-2 h-4 w-4 fill-white" />
            Promote New Event
          </Button>
        </Link>
      </div>

      {/* Analytics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <TrendingUp className="h-4 w-4 mr-2 text-blue-500" />
              Impressions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalImpressions.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Users className="h-4 w-4 mr-2 text-green-500" />
              Clicks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalClicks.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <Ticket className="h-4 w-4 mr-2 text-purple-500" />
              Conversions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalConversions.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BarChart3 className="h-4 w-4 mr-2 text-orange-500" />
              Click Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageCTR.toFixed(2)}%</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center">
              <BarChart3 className="h-4 w-4 mr-2 text-red-500" />
              Conversion Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageConversionRate.toFixed(2)}%</div>
          </CardContent>
        </Card>
      </div>

      {/* Featured Events List */}
      <div className="space-y-6">
        {featuredEvents.map((event) => {
          const now = new Date();
          const featuredUntil = new Date(event.featuredUntil);
          const startDate = new Date(event.startDate);

          // Calculate percentage of promotion time remaining
          const totalDuration = featuredUntil.getTime() - startDate.getTime();
          const elapsed = now.getTime() - startDate.getTime();
          const percentComplete = Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));

          // Calculate time remaining
          const remaining = timeRemaining(event.featuredUntil);

          // Calculate CTR and conversion rate
          const ctr = event.impressions ? (event.clicks! / event.impressions) * 100 : 0;
          const conversionRate = event.clicks ? (event.conversions! / event.clicks) * 100 : 0;

          return (
            <Card key={event.id} className="overflow-hidden border-yellow-100">
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4">
                {/* Event Info */}
                <div className="md:col-span-1 p-4 flex flex-col md:border-r border-yellow-100">
                  <div className="relative h-40 mb-4 rounded-md overflow-hidden">
                    {event.imageUrl ? (
                      <Image
                        src={event.imageUrl}
                        alt={event.title}
                        fill
                        className="object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-yellow-100">
                        <Calendar className="h-12 w-12 text-yellow-500" />
                      </div>
                    )}
                    <div className="absolute top-2 right-2">
                      <Badge className="bg-yellow-500 text-white">
                        {event.promotionType}
                      </Badge>
                    </div>
                  </div>

                  <h3 className="font-semibold text-lg mb-2">{event.title}</h3>

                  <div className="space-y-2 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-yellow-600" />
                      <span>{formatDate(event.startDate)}</span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2 text-yellow-600" />
                      <span className="line-clamp-1">{event.venue}</span>
                    </div>
                  </div>

                  <div className="mt-auto">
                    <Link href={`/events/${event.id}`}>
                      <Button variant="outline" size="sm" className="w-full">
                        View Event
                      </Button>
                    </Link>
                  </div>
                </div>

                {/* Promotion Status */}
                <div className="md:col-span-2 lg:col-span-3 p-4 bg-gray-50">
                  <div className="flex flex-col h-full">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Promotion Status</h4>
                      <Badge variant="outline" className="text-yellow-700 border-yellow-200 bg-yellow-50">
                        {remaining}
                      </Badge>
                    </div>

                    <div className="mb-4">
                      <div className="flex justify-between items-center mb-1">
                        <span className="text-xs font-medium text-gray-700">Progress</span>
                        <span className="text-xs font-medium text-yellow-700">{percentComplete.toFixed(0)}%</span>
                      </div>
                      <div className="relative h-2 w-full overflow-hidden rounded-full bg-yellow-100">
                        <div
                          className="h-full bg-yellow-500 transition-all"
                          style={{ width: `${percentComplete}%` }}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
                      <Card className="bg-white border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <TrendingUp className="h-4 w-4 mr-2 text-blue-500" />
                              <span className="text-sm font-medium">Impressions</span>
                            </div>
                            <span className="text-lg font-bold">{event.impressions?.toLocaleString()}</span>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-white border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Users className="h-4 w-4 mr-2 text-green-500" />
                              <span className="text-sm font-medium">Clicks</span>
                            </div>
                            <div className="text-right">
                              <span className="text-lg font-bold">{event.clicks?.toLocaleString()}</span>
                              <div className="text-xs text-gray-500">CTR: {ctr.toFixed(1)}%</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card className="bg-white border-gray-200">
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center">
                              <Ticket className="h-4 w-4 mr-2 text-purple-500" />
                              <span className="text-sm font-medium">Conversions</span>
                            </div>
                            <div className="text-right">
                              <span className="text-lg font-bold">{event.conversions?.toLocaleString()}</span>
                              <div className="text-xs text-gray-500">Rate: {conversionRate.toFixed(1)}%</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>

                    <div className="mt-auto flex justify-end gap-2">
                      <Link href={`/dashboard/events/${event.id}/analytics`}>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Detailed Analytics
                        </Button>
                      </Link>
                      <Link href={`/dashboard/events/${event.id}/promote`}>
                        <Button size="sm" className="bg-yellow-500 hover:bg-yellow-600 text-white">
                          <Star className="mr-2 h-4 w-4 fill-white" />
                          Extend Promotion
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          );
        })}
      </div>
    </div>
  );
}
