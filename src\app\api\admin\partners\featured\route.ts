import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/partners/featured
 * Get all featured partners
 */
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const [partners, totalCount] = await Promise.all([
      db.partner.findMany({
        where: { 
          featured: true,
          isVerified: true,
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          products: {
            where: {
              status: 'ACTIVE',
              isAvailable: true,
            },
            take: 3,
            orderBy: { createdAt: 'desc' },
          },
          _count: {
            select: {
              products: true,
              reviews: true,
              promotions: true,
            },
          },
        },
      }),
      db.partner.count({ 
        where: { 
          featured: true,
          isVerified: true,
        },
      }),
    ]);

    return NextResponse.json({
      partners,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: Math.floor(offset / limit) + 1,
        hasNextPage: offset + limit < totalCount,
        hasPrevPage: offset > 0,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching featured partners:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured partners' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/partners/featured
 * Add/remove partners from featured list
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { partnerIds, action } = body; // action: 'add' or 'remove'

    if (!partnerIds || !Array.isArray(partnerIds) || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: partnerIds (array) and action' },
        { status: 400 }
      );
    }

    if (!['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "add" or "remove"' },
        { status: 400 }
      );
    }

    // Update partners
    const updatedPartners = await db.partner.updateMany({
      where: {
        id: { in: partnerIds },
        isVerified: true, // Only verified partners can be featured
      },
      data: {
        featured: action === 'add',
      },
    });

    return NextResponse.json({
      message: `Successfully ${action === 'add' ? 'featured' : 'unfeatured'} ${updatedPartners.count} partner(s)`,
      updatedCount: updatedPartners.count,
    });
  } catch (error) {
    console.error('Error updating featured partners:', error);
    return NextResponse.json(
      { error: 'Failed to update featured partners' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/partners/featured
 * Toggle featured status for a single partner
 */
export async function PATCH(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { partnerId, featured } = body;

    if (!partnerId || typeof featured !== 'boolean') {
      return NextResponse.json(
        { error: 'Missing required fields: partnerId and featured (boolean)' },
        { status: 400 }
      );
    }

    // Check if partner exists and is verified
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
      select: { id: true, isVerified: true, businessName: true },
    });

    if (!partner) {
      return NextResponse.json({ error: 'Partner not found' }, { status: 404 });
    }

    if (!partner.isVerified && featured) {
      return NextResponse.json(
        { error: 'Only verified partners can be featured' },
        { status: 400 }
      );
    }

    // Update partner
    const updatedPartner = await db.partner.update({
      where: { id: partnerId },
      data: { featured },
      select: {
        id: true,
        businessName: true,
        featured: true,
        isVerified: true,
      },
    });

    return NextResponse.json({
      message: `Partner "${updatedPartner.businessName}" ${featured ? 'featured' : 'unfeatured'} successfully`,
      partner: updatedPartner,
    });
  } catch (error) {
    console.error('Error toggling partner featured status:', error);
    return NextResponse.json(
      { error: 'Failed to update partner featured status' },
      { status: 500 }
    );
  }
}
