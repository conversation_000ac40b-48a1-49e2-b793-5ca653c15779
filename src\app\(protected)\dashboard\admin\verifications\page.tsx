import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { 
  CheckCircle, 
  Clock, 
  XCircle, 
  Briefcase, 
  Hotel, 
  Store,
  Users,
  ArrowRight
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
  };
};

export default async function AdminVerificationsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only admins can access this page
  if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN') {
    redirect('/dashboard');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Verification Management</h1>
          <p className="text-gray-500 mt-1">
            Review and manage verification requests from organizers, partners, and vendors
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Organizer Verifications */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-blue-100 p-2 rounded-lg">
                  <Briefcase className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Organizer Verifications</CardTitle>
                  <CardDescription>Review organizer verification requests</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending Reviews</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  <Clock className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Approved</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <Button asChild className="w-full mt-4">
                <Link href="/dashboard/admin/verifications/organizers">
                  <Users className="h-4 w-4 mr-2" />
                  Manage Organizers
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Partner Verifications */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-purple-100 p-2 rounded-lg">
                  <Hotel className="h-6 w-6 text-purple-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Partner Verifications</CardTitle>
                  <CardDescription>Review partner verification requests</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending Reviews</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  <Clock className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Approved</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <Button asChild className="w-full mt-4">
                <Link href="/dashboard/admin/verifications/partners">
                  <Hotel className="h-4 w-4 mr-2" />
                  Manage Partners
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Vendor Verifications */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="bg-green-100 p-2 rounded-lg">
                  <Store className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <CardTitle className="text-lg">Vendor Verifications</CardTitle>
                  <CardDescription>Review vendor verification requests</CardDescription>
                </div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending Reviews</span>
                <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                  <Clock className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Total Approved</span>
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Loading...
                </Badge>
              </div>
              <Button asChild className="w-full mt-4">
                <Link href="/dashboard/admin/verifications/vendors">
                  <Store className="h-4 w-4 mr-2" />
                  Manage Vendors
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common verification management tasks</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button variant="outline" asChild>
              <Link href="/dashboard/admin/verifications/organizers?status=pending">
                <Clock className="h-4 w-4 mr-2" />
                Pending Organizers
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/admin/verifications/partners?status=pending">
                <Clock className="h-4 w-4 mr-2" />
                Pending Partners
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/admin/verifications/vendors?status=pending">
                <Clock className="h-4 w-4 mr-2" />
                Pending Vendors
              </Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/dashboard/admin/reports/verifications">
                <CheckCircle className="h-4 w-4 mr-2" />
                Verification Reports
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
