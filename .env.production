# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# IMPORTANT: Update all placeholder values with your actual production values

# =============================================================================
# DATABASE CONFIGURATION (Production)
# =============================================================================
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
DIRECT_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require&channel_binding=require"
SHADOW_DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb_shadow?sslmode=require&channel_binding=require"

# =============================================================================
# AUTHENTICATION & SECURITY (Production)
# =============================================================================
AUTH_SECRET="12Pj+kQlA+CE+tjWw5AU6csRAymyWZugpEKH4LBE1uo="
NEXTAUTH_SECRET="12Pj+kQlA+CE+tjWw5AU6csRAymyWZugpEKH4LBE1uo="
NEXTAUTH_URL="https://event-six-pied.vercel.app"
JWT_SECRET="kueygeiugetuergiegkergeyewvjhbriu"

# =============================================================================
# APPLICATION URLS (Production)
# =============================================================================
NEXT_PUBLIC_API_URL="https://event-six-pied.vercel.app/api"
NEXT_PUBLIC_BASE_URL="https://event-six-pied.vercel.app"
NEXT_PUBLIC_APP_URL="https://event-six-pied.vercel.app"
NEXT_PUBLIC_SITE_URL="https://event-six-pied.vercel.app"

# =============================================================================
# EMAIL CONFIGURATION (Production)
# =============================================================================
RESEND_API_KEY="re_BbuTsyYA_83XwukiC8sxpVQzTT7L9q6EF"
EMAIL_FROM="<EMAIL>"
EMAIL_TESTING="false"  # Set to false in production

# =============================================================================
# OAUTH PROVIDERS (Production)
# =============================================================================
GOOGLE_CLIENT_ID="486126258377-m8crhd4fsee9g44f73kht29ms9nailch.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="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"
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""

# =============================================================================
# EXTERNAL APIS (Production)
# =============================================================================
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="AIzaSyD4QGWFoVWBeDgGpd95DsAwDjeRTAQFyuo"
NEXT_PUBLIC_EXCHANGE_RATE_API_KEY="ad2e5f6056e367228a90c741"
LLAMA_API_KEY="LA-fe435753a54249f48dea66e67f3c2030fd95a32c0bb84f778908e369b83706c2"
OPENAI_API_KEY="************************************************************************************************"

# =============================================================================
# PAYMENT PROCESSING (Production)
# =============================================================================
# UPDATE THESE WITH YOUR PRODUCTION STRIPE KEYS
STRIPE_SECRET_KEY="sk_live_YOUR_PRODUCTION_SECRET_KEY"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_live_YOUR_PRODUCTION_PUBLISHABLE_KEY"
STRIPE_WEBHOOK_SECRET="whsec_YOUR_PRODUCTION_WEBHOOK_SECRET"

# =============================================================================
# PRODUCTION SETTINGS
# =============================================================================
NODE_ENV="production"
TRUST_HOST="true"
NEXT_PUBLIC_AUTO_SEED="false"  # Disable auto-seeding in production

# =============================================================================
# ADMIN CREDENTIALS (Production)
# =============================================================================
# UPDATE THESE WITH SECURE PRODUCTION CREDENTIALS
DEFAULT_SUPERADMIN_EMAIL="<EMAIL>"
DEFAULT_SUPERADMIN_PASSWORD="YourSecureProductionPassword123!"
DEFAULT_SUPERADMIN_NAME="System Super Administrator"
DEFAULT_ADMIN_EMAIL="<EMAIL>"
DEFAULT_ADMIN_PASSWORD="YourSecureAdminPassword123!"
DEFAULT_ADMIN_NAME="System Administrator"
