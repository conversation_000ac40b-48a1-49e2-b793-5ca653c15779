'use server';

import { db } from "@/lib/prisma";
import crypto from 'crypto';

/**
 * Generates and assigns an access token to a user
 * @param userId The ID of the user to update
 * @returns Object with success or error message
 */
export async function generateUserAccessToken(userId: string) {
  try {
    // Check if user exists
    const user = await db.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      console.error(`User ${userId} not found`);
      return { error: 'User not found' };
    }

    // Check if user already has an access token
    if (user.accessToken) {
      console.log(`User ${userId} already has an access token`);
      return { success: true, message: 'User already has an access token' };
    }

    // Generate a random access token
    const accessToken = crypto.randomBytes(32).toString('hex');

    // Update the user with the new access token
    await db.user.update({
      where: { id: userId },
      data: { accessToken },
    });

    console.log(`Access token generated for user ${userId}`);
    return { success: true, message: 'Access token generated successfully' };
  } catch (error) {
    console.error('Error generating access token:', error);
    return { 
      error: 'Failed to generate access token',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}

/**
 * Ensures all users have access tokens
 * @returns Object with success or error message and count of updated users
 */
export async function ensureAllUsersHaveAccessTokens() {
  try {
    // Find all users without access tokens
    const usersWithoutTokens = await db.user.findMany({
      where: { accessToken: null },
      select: { id: true },
    });

    if (usersWithoutTokens.length === 0) {
      return { success: true, message: 'All users already have access tokens', count: 0 };
    }

    // Update each user with a new access token
    let updatedCount = 0;
    for (const user of usersWithoutTokens) {
      try {
        // Generate a random access token
        const accessToken = crypto.randomBytes(32).toString('hex');

        // Update the user
        await db.user.update({
          where: { id: user.id },
          data: { accessToken },
        });

        updatedCount++;
      } catch (updateError) {
        console.error(`Error updating user ${user.id}:`, updateError);
        // Continue with other users even if one fails
      }
    }

    return { 
      success: true, 
      message: `Updated ${updatedCount} of ${usersWithoutTokens.length} users with new access tokens`,
      count: updatedCount
    };
  } catch (error) {
    console.error('Error ensuring all users have access tokens:', error);
    return { 
      error: 'Failed to update users with access tokens',
      details: error instanceof Error ? error.message : String(error)
    };
  }
}
