import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { TransactionsList } from '@/components/wallet/transactions-list';

export const metadata = {
  title: 'Transaction History | QuickTimeEvents',
  description: 'View your complete transaction history',
};

export default async function TransactionsPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>
      </div>

      <div className="mb-6">
        <h1 className="text-3xl font-bold">Transaction History</h1>
        <p className="text-gray-500 mt-1">
          View all your financial transactions
        </p>
      </div>

      <TransactionsList />
    </div>
  );
}
