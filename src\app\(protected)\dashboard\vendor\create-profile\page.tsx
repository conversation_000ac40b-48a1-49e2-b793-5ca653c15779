'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Store } from 'lucide-react';

const vendorProfileSchema = z.object({
  // Basic Business Information
  businessName: z.string().min(2, 'Business name is required'),
  businessType: z.string().min(1, 'Business type is required'),
  registrationNumber: z.string().optional(),
  taxPayerIdNumber: z.string().optional(),
  yearEstablished: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  description: z.string().min(10, 'Description must be at least 10 characters'),

  // Business Categories
  productCategories: z.string().optional(),
  serviceCategories: z.string().optional(),
  specializations: z.string().optional(),

  // Contact Information
  email: z.string().email().optional().or(z.literal('')),
  phoneNumber: z.string().optional(),
  alternativePhoneNumber: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),

  // Physical Location
  physicalAddress: z.string().optional(),
  city: z.string().optional(),
  province: z.string().optional(),
  postalCode: z.string().optional(),

  // Business Details
  acceptedPaymentMethods: z.string().optional(),
  employeeCount: z.string().optional().transform(val => val ? parseInt(val) : undefined),
  businessSize: z.string().optional(),
});

export default function CreateVendorProfilePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm({
    resolver: zodResolver(vendorProfileSchema),
    defaultValues: {
      // Basic Business Information
      businessName: '',
      businessType: 'Company',
      registrationNumber: '',
      taxPayerIdNumber: '',
      yearEstablished: '',
      description: '',

      // Business Categories
      productCategories: '',
      serviceCategories: '',
      specializations: '',

      // Contact Information
      email: '',
      phoneNumber: '',
      alternativePhoneNumber: '',
      website: '',

      // Physical Location
      physicalAddress: '',
      city: '',
      province: '',
      postalCode: '',

      // Business Details
      acceptedPaymentMethods: '',
      employeeCount: '',
      businessSize: '',
    },
  });

  async function onSubmit(data: any) {
    setIsLoading(true);

    try {
      // Convert yearEstablished from string to number
      const processedData = {
        ...data,
        yearEstablished: data.yearEstablished ? Number(data.yearEstablished) : undefined,
      };

      const response = await fetch('/api/vendors/profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(processedData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create vendor profile');
      }

      toast({
        title: 'Success',
        description: 'Your vendor profile has been created. Please complete verification.',
        variant: 'default',
      });

      // Redirect to verification page
      router.push('/dashboard/vendor/verification');
    } catch (error) {
      console.error('Error creating vendor profile:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'An error occurred while creating your profile',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Create Vendor Profile</h1>

      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle>Vendor Information</CardTitle>
          <CardDescription>
            Enter your business details to create your vendor profile. After creating your profile,
            you'll need to complete the verification process.
          </CardDescription>
        </CardHeader>

        <CardContent>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            {/* Basic Business Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">Basic Business Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name *</Label>
                  <Input
                    id="businessName"
                    {...form.register('businessName')}
                    placeholder="Your business name"
                  />
                  {form.formState.errors.businessName && (
                    <p className="text-sm text-red-500">{form.formState.errors.businessName.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessType">Business Type *</Label>
                  <Select
                    onValueChange={(value) => form.setValue('businessType', value)}
                    defaultValue={form.getValues('businessType')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select business type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Company">Company</SelectItem>
                      <SelectItem value="Sole Proprietorship">Sole Proprietorship</SelectItem>
                      <SelectItem value="Partnership">Partnership</SelectItem>
                      <SelectItem value="Non-profit">Non-profit</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  {form.formState.errors.businessType && (
                    <p className="text-sm text-red-500">{form.formState.errors.businessType.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="registrationNumber">Business Registration Number</Label>
                  <Input
                    id="registrationNumber"
                    {...form.register('registrationNumber')}
                    placeholder="Registration number"
                  />
                  {form.formState.errors.registrationNumber && (
                    <p className="text-sm text-red-500">{form.formState.errors.registrationNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="taxPayerIdNumber">Tax Payer ID Number (TPIN)</Label>
                  <Input
                    id="taxPayerIdNumber"
                    {...form.register('taxPayerIdNumber')}
                    placeholder="Tax ID number"
                  />
                  {form.formState.errors.taxPayerIdNumber && (
                    <p className="text-sm text-red-500">{form.formState.errors.taxPayerIdNumber.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="yearEstablished">Year Established</Label>
                  <Input
                    id="yearEstablished"
                    {...form.register('yearEstablished')}
                    placeholder="e.g., 2015"
                  />
                  {form.formState.errors.yearEstablished && (
                    <p className="text-sm text-red-500">{form.formState.errors.yearEstablished.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessSize">Business Size</Label>
                  <Select
                    onValueChange={(value) => form.setValue('businessSize', value)}
                    defaultValue={form.getValues('businessSize')}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select business size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Micro">Micro (1-5 employees)</SelectItem>
                      <SelectItem value="Small">Small (6-20 employees)</SelectItem>
                      <SelectItem value="Medium">Medium (21-100 employees)</SelectItem>
                      <SelectItem value="Large">Large (100+ employees)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2 mt-4">
                <Label htmlFor="description">Business Description *</Label>
                <Textarea
                  id="description"
                  {...form.register('description')}
                  placeholder="Describe your business, its mission, and what products or services you offer"
                  rows={4}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                )}
              </div>
            </div>

            {/* Business Categories */}
            <div>
              <h3 className="text-lg font-medium mb-4">Business Categories & Specializations</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="productCategories">Product Categories</Label>
                  <Input
                    id="productCategories"
                    {...form.register('productCategories')}
                    placeholder="e.g., Electronics, Food, Clothing, Crafts"
                  />
                  <p className="text-xs text-gray-500">Separate categories with commas</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="serviceCategories">Service Categories</Label>
                  <Input
                    id="serviceCategories"
                    {...form.register('serviceCategories')}
                    placeholder="e.g., Consulting, Catering, Entertainment"
                  />
                  <p className="text-xs text-gray-500">Separate categories with commas</p>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="specializations">Specializations</Label>
                  <Input
                    id="specializations"
                    {...form.register('specializations')}
                    placeholder="e.g., Organic Food, Custom Jewelry, Mobile Repairs"
                  />
                  <p className="text-xs text-gray-500">Separate specializations with commas</p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h3 className="text-lg font-medium mb-4">Contact Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Business Email</Label>
                  <Input
                    id="email"
                    {...form.register('email')}
                    placeholder="<EMAIL>"
                    type="email"
                  />
                  {form.formState.errors.email && (
                    <p className="text-sm text-red-500">{form.formState.errors.email.message}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phoneNumber">Phone Number</Label>
                  <Input
                    id="phoneNumber"
                    {...form.register('phoneNumber')}
                    placeholder="+260 97 1234567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="alternativePhoneNumber">Alternative Phone</Label>
                  <Input
                    id="alternativePhoneNumber"
                    {...form.register('alternativePhoneNumber')}
                    placeholder="+260 97 1234567"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website">Website</Label>
                  <Input
                    id="website"
                    {...form.register('website')}
                    placeholder="https://yourbusiness.com"
                  />
                  {form.formState.errors.website && (
                    <p className="text-sm text-red-500">{form.formState.errors.website.message}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Physical Location */}
            <div>
              <h3 className="text-lg font-medium mb-4">Physical Location</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="physicalAddress">Street Address</Label>
                  <Input
                    id="physicalAddress"
                    {...form.register('physicalAddress')}
                    placeholder="123 Business Street"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    {...form.register('city')}
                    placeholder="Lusaka"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="province">Province</Label>
                  <Input
                    id="province"
                    {...form.register('province')}
                    placeholder="Lusaka Province"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="postalCode">Postal Code</Label>
                  <Input
                    id="postalCode"
                    {...form.register('postalCode')}
                    placeholder="10101"
                  />
                </div>
              </div>
            </div>

            {/* Business Details */}
            <div>
              <h3 className="text-lg font-medium mb-4">Business Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="acceptedPaymentMethods">Accepted Payment Methods</Label>
                  <Input
                    id="acceptedPaymentMethods"
                    {...form.register('acceptedPaymentMethods')}
                    placeholder="e.g., Cash, Mobile Money, Credit Card, Bank Transfer"
                  />
                  <p className="text-xs text-gray-500">Separate payment methods with commas</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="employeeCount">Number of Employees</Label>
                  <Input
                    id="employeeCount"
                    {...form.register('employeeCount')}
                    placeholder="e.g., 5"
                    type="number"
                  />
                </div>
              </div>
            </div>
          </form>
        </CardContent>

        <CardFooter className="flex justify-between">
          <Button
            variant="outline"
            onClick={() => router.back()}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Store className="mr-2 h-4 w-4" />
                Create Vendor Profile
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
