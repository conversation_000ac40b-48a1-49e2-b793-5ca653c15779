import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/events/featured
 * Get all featured events for admin management
 */
export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = parseInt(searchParams.get('offset') || '0');

    const [events, totalCount] = await Promise.all([
      db.event.findMany({
        where: {
          status: 'Published',
          featuring: {
            some: {
              status: 'ACTIVE',
              endDate: {
                gte: new Date(), // Only active featuring that hasn't expired
              },
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          featuring: {
            where: {
              status: 'ACTIVE',
              endDate: {
                gte: new Date(),
              },
            },
            select: {
              id: true,
              tier: true,
              startDate: true,
              endDate: true,
              paymentAmount: true,
            },
          },
          _count: {
            select: {
              tickets: true,
              orders: true,
            },
          },
        },
      }),
      db.event.count({
        where: {
          status: 'Published',
          featuring: {
            some: {
              status: 'ACTIVE',
              endDate: {
                gte: new Date(),
              },
            },
          },
        },
      }),
    ]);

    return NextResponse.json({
      events,
      pagination: {
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: Math.floor(offset / limit) + 1,
        hasNextPage: offset + limit < totalCount,
        hasPrevPage: offset > 0,
        limit,
        offset,
      },
    });
  } catch (error) {
    console.error('Error fetching featured events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured events' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/events/featured
 * Add/remove events from featured list
 */
export async function POST(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { eventIds, action } = body; // action: 'add' or 'remove'

    if (!eventIds || !Array.isArray(eventIds) || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: eventIds (array) and action' },
        { status: 400 }
      );
    }

    if (!['add', 'remove'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "add" or "remove"' },
        { status: 400 }
      );
    }

    // Handle featuring/unfeaturing events
    let updatedCount = 0;

    if (action === 'add') {
      // Add featuring for events that don't already have active featuring
      const eventsToFeature = await db.event.findMany({
        where: {
          id: { in: eventIds },
          status: 'Published',
          featuring: {
            none: {
              status: 'ACTIVE',
              endDate: {
                gte: new Date(),
              },
            },
          },
        },
        select: { id: true },
      });

      // Create featuring records for eligible events
      for (const event of eventsToFeature) {
        await db.eventFeaturing.create({
          data: {
            eventId: event.id,
            tier: 'BASIC', // Default tier for admin featuring
            startDate: new Date(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
            status: 'ACTIVE',
            paymentAmount: 0, // Admin featuring is free
            metadata: {
              adminFeatured: true,
              featuredBy: user.id,
            },
          },
        });
        updatedCount++;
      }
    } else {
      // Remove active featuring
      const result = await db.eventFeaturing.updateMany({
        where: {
          eventId: { in: eventIds },
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
          },
        },
        data: {
          status: 'CANCELLED',
        },
      });
      updatedCount = result.count;
    }

    return NextResponse.json({
      message: `Successfully ${action === 'add' ? 'featured' : 'unfeatured'} ${updatedCount} event(s)`,
      updatedCount: updatedCount,
    });
  } catch (error) {
    console.error('Error updating featured events:', error);
    return NextResponse.json(
      { error: 'Failed to update featured events' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/events/featured
 * Toggle featured status for a single event
 */
export async function PATCH(request: NextRequest) {
  try {
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { eventId, featured } = body;

    if (!eventId || typeof featured !== 'boolean') {
      return NextResponse.json(
        { error: 'Missing required fields: eventId and featured (boolean)' },
        { status: 400 }
      );
    }

    // Check if event exists and is published
    const event = await db.event.findUnique({
      where: { id: eventId },
      select: { id: true, status: true, title: true },
    });

    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    if (event.status !== 'Published' && featured) {
      return NextResponse.json(
        { error: 'Only published events can be featured' },
        { status: 400 }
      );
    }

    // Handle featuring/unfeaturing the event
    if (featured) {
      // Check if event already has active featuring
      const existingFeaturing = await db.eventFeaturing.findFirst({
        where: {
          eventId,
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
          },
        },
      });

      if (!existingFeaturing) {
        // Create new featuring record
        await db.eventFeaturing.create({
          data: {
            eventId,
            tier: 'BASIC',
            startDate: new Date(),
            endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
            status: 'ACTIVE',
            paymentAmount: 0,
            metadata: {
              adminFeatured: true,
              featuredBy: user.id,
            },
          },
        });
      }
    } else {
      // Remove active featuring
      await db.eventFeaturing.updateMany({
        where: {
          eventId,
          status: 'ACTIVE',
          endDate: {
            gte: new Date(),
          },
        },
        data: {
          status: 'CANCELLED',
        },
      });
    }

    // Get updated event with featuring info
    const updatedEvent = await db.event.findUnique({
      where: { id: eventId },
      select: {
        id: true,
        title: true,
        status: true,
        featuring: {
          where: {
            status: 'ACTIVE',
            endDate: {
              gte: new Date(),
            },
          },
          select: {
            id: true,
            tier: true,
            endDate: true,
          },
        },
      },
    });

    return NextResponse.json({
      message: `Event "${updatedEvent?.title}" ${featured ? 'featured' : 'unfeatured'} successfully`,
      event: {
        ...updatedEvent,
        featured: updatedEvent?.featuring && updatedEvent.featuring.length > 0,
      },
    });
  } catch (error) {
    console.error('Error toggling event featured status:', error);
    return NextResponse.json(
      { error: 'Failed to update event featured status' },
      { status: 500 }
    );
  }
}
