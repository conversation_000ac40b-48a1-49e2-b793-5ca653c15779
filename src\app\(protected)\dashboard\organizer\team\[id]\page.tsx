'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { RoleGate } from '@/components/auth/role-gate';
import { TeamPermissionGate } from '@/components/auth/team-permission-gate';
import {
  Users,
  Calendar,
  Settings,
  UserPlus,
  Mail,
  Clock,
  Building2,
  CalendarDays,
  ChevronLeft,
  MoreHorizontal,
  Edit,
  Trash2
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

// Team interface
interface Team {
  id: string;
  name: string;
  description?: string;
  ownerId: string;
  createdAt: string;
  updatedAt: string;
  owner: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
  members: TeamMember[];
  events: Event[];
  invitations: Invitation[];
  _count: {
    members: number;
    events: number;
    invitations: number;
  };
}

// Team member interface
interface TeamMember {
  id: string;
  teamId: string;
  userId: string;
  role: string;
  createdAt: string;
  updatedAt: string;
  user: {
    id: string;
    name: string;
    email: string;
    image: string;
  };
}

// Event interface
interface Event {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  status: string;
  imagePath?: string;
}

// Invitation interface
interface Invitation {
  id: string;
  email: string;
  role: string;
  status: string;
  createdAt: string;
  expiresAt: string;
  token?: string;
}

export default function TeamPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const router = useRouter();
  const [team, setTeam] = useState<Team | null>(null);
  const [loading, setLoading] = useState(true);
  const [isOwner, setIsOwner] = useState(false);
  const [userRole, setUserRole] = useState<string | null>(null);
  const [invitationToCancel, setInvitationToCancel] = useState<Invitation | null>(null);
  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [memberToRemove, setMemberToRemove] = useState<TeamMember | null>(null);
  const [isRemoveDialogOpen, setIsRemoveDialogOpen] = useState(false);
  const [memberToEdit, setMemberToEdit] = useState<TeamMember | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [newRole, setNewRole] = useState('');

  // Fetch team data
  useEffect(() => {
    const fetchTeam = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/teams/${id}`);

        if (!response.ok) {
          if (response.status === 404) {
            toast({
              title: 'Team not found',
              description: 'The team you are looking for does not exist.',
              variant: 'destructive',
            });
            router.push('/dashboard/organizer/team');
            return;
          }
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setTeam(data.team);
        setIsOwner(data.isOwner);
        setUserRole(data.userRole);
      } catch (error) {
        console.error('Error fetching team:', error);
        toast({
          title: 'Error',
          description: 'Failed to load team data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchTeam();
  }, [id, router]);

  // Format date for display
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  // Get role badge color
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'ORGANIZER_ADMIN':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Admin</Badge>;
      case 'ORGANIZER_MANAGER':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Manager</Badge>;
      case 'ORGANIZER_EDITOR':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Editor</Badge>;
      case 'ORGANIZER_ANALYST':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Analyst</Badge>;
      case 'ORGANIZER_SUPPORT':
        return <Badge className="bg-orange-100 text-orange-800 border-orange-200">Support</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Published':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Published</Badge>;
      case 'Draft':
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Draft</Badge>;
      case 'Approved':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Approved</Badge>;
      case 'UnderReview':
        return <Badge className="bg-purple-100 text-purple-800 border-purple-200">Under Review</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  // Get user initials for avatar
  const getUserInitials = (name: string) => {
    if (!name) return '??';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle cancel invitation
  const handleCancelInvitation = async () => {
    if (!invitationToCancel) return;

    try {
      setIsSubmitting(true);

      const response = await fetch(`/api/teams/invitations/${invitationToCancel.id}/cancel`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'Invitation cancelled successfully',
      });

      // Update the team data to reflect the cancelled invitation
      if (team) {
        setTeam({
          ...team,
          invitations: team.invitations.filter(inv => inv.id !== invitationToCancel.id),
          _count: {
            ...team._count,
            invitations: team._count.invitations - 1
          }
        });
      }

      // Reset state and close dialog
      setInvitationToCancel(null);
      setIsCancelDialogOpen(false);
    } catch (error) {
      console.error('Error cancelling invitation:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to cancel invitation',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle remove member
  const handleRemoveMember = async () => {
    if (!memberToRemove) return;

    try {
      setIsSubmitting(true);
      console.log('Removing member:', memberToRemove);

      const response = await fetch(`/api/teams/${team?.id}/members/${memberToRemove.id}`, {
        method: 'DELETE',
      });

      console.log('Remove member response status:', response.status);

      let data;
      try {
        data = await response.json();
        console.log('Remove member response data:', data);
      } catch (e) {
        console.error('Error parsing JSON response:', e);
      }

      if (!response.ok) {
        console.error('Remove member error:', data);
        throw new Error(data?.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: data.message || 'Member removed successfully',
      });

      // Update the team data to reflect the removed member
      if (team) {
        console.log('Updating team data after member removal');
        setTeam({
          ...team,
          members: team.members?.filter(member => member.id !== memberToRemove.id) || [],
          _count: {
            ...team._count,
            members: (team._count?.members || 1) - 1
          }
        });
      }

      // Reset state and close dialog
      setMemberToRemove(null);
      setIsRemoveDialogOpen(false);
    } catch (error) {
      console.error('Error removing member:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to remove member',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle edit member role
  const handleEditMemberRole = async () => {
    if (!memberToEdit || !newRole) return;

    try {
      setIsSubmitting(true);
      console.log('Updating member role:', memberToEdit, 'New role:', newRole);

      const response = await fetch(`/api/teams/${team?.id}/members/${memberToEdit.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          role: newRole,
        }),
      });

      console.log('Update member role response status:', response.status);

      let data;
      try {
        data = await response.json();
        console.log('Update member role response data:', data);
      } catch (e) {
        console.error('Error parsing JSON response:', e);
      }

      if (!response.ok) {
        console.error('Update member role error:', data);
        throw new Error(data?.error || `Error: ${response.status}`);
      }

      toast({
        title: 'Success',
        description: 'Member role updated successfully',
      });

      // Update the team data to reflect the updated member role
      if (team) {
        console.log('Updating team data after role change');
        setTeam({
          ...team,
          members: team.members?.map(member =>
            member.id === memberToEdit.id
              ? { ...member, role: newRole }
              : member
          ) || []
        });
      }

      // Reset state and close dialog
      setMemberToEdit(null);
      setNewRole('');
      setIsEditDialogOpen(false);
    } catch (error) {
      console.error('Error updating member role:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update member role',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  if (!team) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Team Not Found</h2>
          <p className="text-gray-500 mb-6">The team you are looking for does not exist or you don't have permission to view it.</p>
          <Button asChild>
            <Link href="/dashboard/organizer/team">
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back to Teams
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <RoleGate allowedRole={['ORGANIZER', 'ADMIN', 'SUPERADMIN']}>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link href="/dashboard/organizer/team" className="text-sm text-gray-500 hover:text-gray-700 flex items-center">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Back to Teams
          </Link>
        </div>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <div>
            <div className="flex items-center">
              <h1 className="text-3xl font-bold">{team.name}</h1>
              {isOwner && (
                <Badge className="ml-3 bg-purple-100 text-purple-800 border-purple-200">Owner</Badge>
              )}
              {!isOwner && userRole && (
                <div className="ml-3">{getRoleBadge(userRole)}</div>
              )}
            </div>
            {team.description && (
              <p className="text-gray-500 mt-1">{team.description}</p>
            )}
          </div>

          <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
            <div className="mt-4 md:mt-0 flex space-x-2">
              <Button variant="outline" asChild>
                <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Invite Members
                </Link>
              </Button>
              <Button variant="outline" asChild>
                <Link href={`/dashboard/organizer/team/${team.id}/settings`}>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </Link>
              </Button>
            </div>
          </TeamPermissionGate>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Users className="h-5 w-5 mr-2 text-gray-400" />
                Team Members
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{team._count?.members || 0}</div>
              <p className="text-sm text-gray-500">Active members</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                  View All Members
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-gray-400" />
                Team Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{team._count?.events || 0}</div>
              <p className="text-sm text-gray-500">Managed events</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href={`/dashboard/organizer/team/${team.id}/events`}>
                  View All Events
                </Link>
              </Button>
            </CardFooter>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg flex items-center">
                <Mail className="h-5 w-5 mr-2 text-gray-400" />
                Pending Invitations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold">{team._count?.invitations || 0}</div>
              <p className="text-sm text-gray-500">Awaiting response</p>
            </CardContent>
            <CardFooter>
              <Button variant="outline" size="sm" className="w-full" asChild>
                <Link href={`/dashboard/organizer/team/${team.id}/invitations`}>
                  Manage Invitations
                </Link>
              </Button>
            </CardFooter>
          </Card>
        </div>

        <Tabs defaultValue="members" className="mt-6">
          <TabsList className="mb-6">
            <TabsTrigger value="members">Members</TabsTrigger>
            <TabsTrigger value="events">Events</TabsTrigger>
            <TabsTrigger value="invitations">Invitations</TabsTrigger>
          </TabsList>

          <TabsContent value="members">
            <Card>
              <CardHeader>
                <CardTitle>Team Members</CardTitle>
                <CardDescription>
                  People who have access to this team's events
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!team.members || team.members.length === 0 ? (
                  <div className="text-center py-6">
                    <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Members Yet</h3>
                    <p className="text-gray-500 mb-6 max-w-md mx-auto">
                      This team doesn't have any members yet. Invite people to collaborate on events.
                    </p>
                    <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                      <Button asChild>
                        <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Invite Members
                        </Link>
                      </Button>
                    </TeamPermissionGate>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {team.members?.map((member) => (
                      <div key={member.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10 mr-3">
                            <AvatarImage src={member.user.image || ''} alt={member.user.name || 'Team member'} />
                            <AvatarFallback>{getUserInitials(member.user.name || '')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{member.user.name || 'Unnamed User'}</div>
                            <div className="text-sm text-gray-500">{member.user.email}</div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {getRoleBadge(member.role)}

                          <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                            {member.user.id !== team.ownerId && (
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuItem
                                    onClick={() => {
                                      setMemberToEdit(member);
                                      setNewRole(member.role);
                                      setIsEditDialogOpen(true);
                                    }}
                                  >
                                    <Edit className="h-4 w-4 mr-2" />
                                    Change Role
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    className="text-red-600"
                                    onClick={() => {
                                      setMemberToRemove(member);
                                      setIsRemoveDialogOpen(true);
                                    }}
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Remove
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            )}
                          </TeamPermissionGate>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                    View All Members
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="events">
            <Card>
              <CardHeader>
                <CardTitle>Team Events</CardTitle>
                <CardDescription>
                  Events managed by this team
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!team.events || team.events.length === 0 ? (
                  <div className="text-center py-6">
                    <Calendar className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Events Yet</h3>
                    <p className="text-gray-500 mb-6 max-w-md mx-auto">
                      This team doesn't have any events yet. Create an event and assign it to this team.
                    </p>
                    <TeamPermissionGate
                      teamId={team.id}
                      requiredPermissions={['create']}
                      fallback={
                        <p className="text-sm text-gray-500">You don't have permission to create events for this team.</p>
                      }
                    >
                      <Button asChild>
                        <Link href="/dashboard/organizer/events/create">
                          <Calendar className="mr-2 h-4 w-4" />
                          Create Event
                        </Link>
                      </Button>
                    </TeamPermissionGate>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {team.events?.map((event) => (
                      <div key={event.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center">
                          <div className="h-12 w-12 rounded-md bg-gray-100 mr-3 overflow-hidden">
                            {event.imagePath ? (
                              <img
                                src={event.imagePath}
                                alt={event.title}
                                className="h-full w-full object-cover"
                              />
                            ) : (
                              <CalendarDays className="h-full w-full p-2 text-gray-400" />
                            )}
                          </div>
                          <div>
                            <div className="font-medium">{event.title}</div>
                            <div className="text-sm text-gray-500">
                              {format(new Date(event.startDate), 'MMM d, yyyy')}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {getStatusBadge(event.status)}
                          <Button variant="ghost" size="sm" asChild>
                            <Link href={`/dashboard/organizer/events/edit/${event.id}`}>
                              <Edit className="h-4 w-4" />
                            </Link>
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/dashboard/organizer/team/${team.id}/events`}>
                    View All Events
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="invitations">
            <Card>
              <CardHeader>
                <CardTitle>Pending Invitations</CardTitle>
                <CardDescription>
                  People who have been invited to join this team
                </CardDescription>
              </CardHeader>
              <CardContent>
                {!team.invitations || team.invitations.length === 0 ? (
                  <div className="text-center py-6">
                    <Mail className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium mb-2">No Pending Invitations</h3>
                    <p className="text-gray-500 mb-6 max-w-md mx-auto">
                      There are no pending invitations for this team.
                    </p>
                    <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                      <Button asChild>
                        <Link href={`/dashboard/organizer/team/${team.id}/members`}>
                          <UserPlus className="mr-2 h-4 w-4" />
                          Invite Members
                        </Link>
                      </Button>
                    </TeamPermissionGate>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {team.invitations?.map((invitation) => (
                      <div key={invitation.id} className="flex items-center justify-between p-3 rounded-lg border">
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10 mr-3">
                            <AvatarFallback>
                              <Mail className="h-5 w-5 text-gray-400" />
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="font-medium">{invitation.email}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              Expires {format(new Date(invitation.expiresAt), 'MMM d, yyyy')}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          {getRoleBadge(invitation.role)}

                          <TeamPermissionGate teamId={team.id} requiredRole="ORGANIZER_ADMIN">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem
                                  onClick={async () => {
                                    try {
                                      setIsSubmitting(true);
                                      const response = await fetch(`/api/teams/invitations/${invitation.token}/resend`, {
                                        method: 'POST',
                                      });

                                      if (!response.ok) {
                                        const errorData = await response.json();
                                        throw new Error(errorData.error || `Error: ${response.status}`);
                                      }

                                      toast({
                                        title: 'Success',
                                        description: 'Invitation resent successfully',
                                      });
                                    } catch (error) {
                                      console.error('Error resending invitation:', error);
                                      toast({
                                        title: 'Error',
                                        description: error instanceof Error ? error.message : 'Failed to resend invitation',
                                        variant: 'destructive',
                                      });
                                    } finally {
                                      setIsSubmitting(false);
                                    }
                                  }}
                                >
                                  <Mail className="h-4 w-4 mr-2" />
                                  Resend Invitation
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-600"
                                  onClick={() => {
                                    setInvitationToCancel(invitation);
                                    setIsCancelDialogOpen(true);
                                  }}
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Cancel Invitation
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TeamPermissionGate>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/dashboard/organizer/team/${team.id}/invitations`}>
                    Manage Invitations
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Cancel Invitation Confirmation Dialog */}
        <AlertDialog open={isCancelDialogOpen} onOpenChange={setIsCancelDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Cancel Invitation</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to cancel the invitation sent to {invitationToCancel?.email}?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.preventDefault();
                  handleCancelInvitation();
                }}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'Cancelling...' : 'Cancel Invitation'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Remove Member Confirmation Dialog */}
        <AlertDialog open={isRemoveDialogOpen} onOpenChange={setIsRemoveDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Remove Team Member</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to remove {memberToRemove?.user.name || memberToRemove?.user.email} from the team?
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={(e) => {
                  e.preventDefault();
                  handleRemoveMember();
                }}
                disabled={isSubmitting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isSubmitting ? 'Removing...' : 'Remove Member'}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Edit Member Role Dialog */}
        <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Change Member Role</DialogTitle>
              <DialogDescription>
                Update the role for {memberToEdit?.user.name || memberToEdit?.user.email}.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="role">Role</Label>
                <Select value={newRole} onValueChange={setNewRole}>
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="ORGANIZER_ADMIN">Admin</SelectItem>
                    <SelectItem value="ORGANIZER_MANAGER">Manager</SelectItem>
                    <SelectItem value="ORGANIZER_EDITOR">Editor</SelectItem>
                    <SelectItem value="ORGANIZER_ANALYST">Analyst</SelectItem>
                    <SelectItem value="ORGANIZER_SUPPORT">Support</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-sm text-gray-500 mt-1">
                  {newRole === 'ORGANIZER_ADMIN' && 'Full access to manage the team, events, and members.'}
                  {newRole === 'ORGANIZER_MANAGER' && 'Can create and edit events, and invite members.'}
                  {newRole === 'ORGANIZER_EDITOR' && 'Can edit event details but cannot create new events.'}
                  {newRole === 'ORGANIZER_ANALYST' && 'View-only access to events and analytics.'}
                  {newRole === 'ORGANIZER_SUPPORT' && 'Can manage attendees and tickets.'}
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setIsEditDialogOpen(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button onClick={handleEditMemberRole} disabled={isSubmitting || !newRole}>
                {isSubmitting ? 'Updating...' : 'Update Role'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </RoleGate>
  );
}
