const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function approveVendorVerification() {
  try {
    // Get the pending verification
    const verification = await prisma.vendorVerification.findFirst({
      where: { status: 'PENDING' },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            vendorProfile: {
              select: {
                id: true,
                businessName: true
              }
            }
          }
        }
      }
    });

    if (!verification) {
      console.log('No pending vendor verifications found');
      return;
    }

    console.log('Found pending verification:');
    console.log('ID:', verification.id);
    console.log('User:', verification.user.name, verification.user.email);
    console.log('Business:', verification.user.vendorProfile?.businessName || 'No business name');
    
    // Update verification status
    console.log('\nApproving verification...');
    
    const updatedVerification = await prisma.vendorVerification.update({
      where: { id: verification.id },
      data: {
        status: 'APPROVED',
        reviewedAt: new Date(),
        reviewedBy: 'admin-script'
      }
    });
    
    console.log('Verification updated successfully. New status:', updatedVerification.status);
    
    // Update vendor profile verification status if it exists
    if (verification.user.vendorProfile) {
      console.log('\nUpdating vendor profile verification status...');
      
      const updatedProfile = await prisma.vendorProfile.update({
        where: { id: verification.user.vendorProfile.id },
        data: {
          verificationStatus: 'APPROVED',
          verifiedAt: new Date()
        }
      });
      
      console.log('Vendor profile updated successfully.');
    }
    
    // Create notification for the vendor
    console.log('\nCreating notification for the vendor...');
    
    const notification = await prisma.notification.create({
      data: {
        userId: verification.userId,
        type: 'VERIFICATION_APPROVED',
        message: 'Your vendor verification has been approved. You can now start selling products.',
      }
    });
    
    console.log('Notification created successfully.');
    
  } catch (error) {
    console.error('Error approving vendor verification:', error);
  } finally {
    await prisma.$disconnect();
  }
}

approveVendorVerification()
  .then(() => console.log('Done!'))
  .catch((error) => console.error('Script failed:', error));
