'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { toast } from '@/components/ui/use-toast';
import { Loader2, Package, Plus, Search, Edit, Trash2, AlertCircle, Eye, EyeOff, Send } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  stockQuantity: number;
  imagePath: string | null;
  status: string;
  createdAt: string;
}

export default function VendorProductsPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState<string | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);

  useEffect(() => {
    async function fetchProducts() {
      try {
        setLoading(true);

        // Get URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const category = urlParams.get('category');
        if (category) {
          setCategoryFilter(category);
        }

        // Build the API URL with any filters
        let apiUrl = '/api/vendors/products';
        const params = new URLSearchParams();
        if (category) {
          params.append('category', category);
        }
        if (params.toString()) {
          apiUrl += `?${params.toString()}`;
        }

        const response = await fetch(apiUrl);

        if (!response.ok) {
          throw new Error('Failed to fetch products');
        }

        const data = await response.json();
        setProducts(data);
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load products',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }

    fetchProducts();
  }, []);

  const deleteProduct = async (productId: string) => {
    if (confirm('Are you sure you want to delete this product? This action cannot be undone.')) {
      try {
        setIsDeleting(true);

        const response = await fetch(`/api/vendors/products/${productId}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error('Failed to delete product');
        }

        // Remove the product from the state
        setProducts(products.filter(product => product.id !== productId));

        toast({
          title: 'Success',
          description: 'Product deleted successfully',
          variant: 'default',
        });
      } catch (err) {
        console.error('Error deleting product:', err);
        toast({
          title: 'Error',
          description: err instanceof Error ? err.message : 'Failed to delete product',
          variant: 'destructive',
        });
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const updateProductStatus = async (productId: string, newStatus: 'Draft' | 'Onsale' | 'Discontinued') => {
    try {
      setIsUpdatingStatus(productId);
      const response = await fetch(`/api/vendors/products/${productId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update product status');
      }

      const data = await response.json();

      // Update the product in the local state
      setProducts(products.map(product =>
        product.id === productId
          ? { ...product, status: newStatus }
          : product
      ));

      toast({
        title: 'Success',
        description: data.message,
        variant: 'default',
      });
    } catch (error) {
      console.error('Error updating product status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update product status',
        variant: 'destructive',
      });
    } finally {
      setIsUpdatingStatus(null);
    }
  };

  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <h1 className="text-3xl font-bold">Your Products</h1>
          <p className="text-gray-500 mt-1">Manage your product listings</p>
        </div>
        <Button onClick={() => router.push('/vendor/createproduct')}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Product
        </Button>
      </div>

      <Card className="mb-8">
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search products by name, description, or category..."
              className="pl-10"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {loading ? (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading products...</span>
        </div>
      ) : error ? (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center text-red-500">
              <AlertCircle className="h-5 w-5 mr-2" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      ) : filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="py-12">
            <div className="text-center">
              <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              {searchTerm ? (
                <>
                  <h3 className="text-lg font-medium mb-2">No matching products found</h3>
                  <p className="text-gray-500 mb-4">Try adjusting your search terms</p>
                  <Button variant="outline" onClick={() => setSearchTerm('')}>
                    Clear Search
                  </Button>
                </>
              ) : (
                <>
                  <h3 className="text-lg font-medium mb-2">No Products Yet</h3>
                  <p className="text-gray-500 mb-4">Start adding products to your store</p>
                  <Button onClick={() => router.push('/vendor/createproduct')}>
                    Add Your First Product
                  </Button>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => (
            <Card key={product.id} className="overflow-hidden flex flex-col">
              <div className="relative h-48 bg-gray-100">
                {product.imagePath ? (
                  <Image
                    src={product.imagePath}
                    alt={product.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Package className="h-12 w-12 text-gray-400" />
                  </div>
                )}
                <Badge
                  className={`absolute top-2 right-2 ${
                    product.status === 'Onsale'
                      ? 'bg-green-100 text-green-800'
                      : product.status === 'Draft'
                      ? 'bg-yellow-100 text-yellow-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {product.status}
                </Badge>
              </div>
              <CardContent className="pt-4 flex-grow">
                <h3 className="font-medium text-lg truncate">{product.name}</h3>
                <p className="text-gray-500 text-sm line-clamp-2 mt-1">{product.description}</p>
                <div className="flex justify-between items-center mt-3">
                  <span className="font-bold text-lg">K{product.price.toLocaleString()}</span>
                  <span className="text-sm text-gray-500">Stock: {product.stockQuantity}</span>
                </div>
              </CardContent>
              <CardFooter className="border-t pt-4 flex flex-col gap-2">
                {/* Publish/Unpublish Button */}
                <div className="flex justify-center w-full">
                  {product.status === 'Draft' ? (
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => updateProductStatus(product.id, 'Onsale')}
                      disabled={isUpdatingStatus === product.id}
                    >
                      {isUpdatingStatus === product.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4 mr-1" />
                      )}
                      Publish Product
                    </Button>
                  ) : product.status === 'Onsale' ? (
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-orange-300 text-orange-600 hover:bg-orange-50"
                      onClick={() => updateProductStatus(product.id, 'Draft')}
                      disabled={isUpdatingStatus === product.id}
                    >
                      {isUpdatingStatus === product.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <EyeOff className="h-4 w-4 mr-1" />
                      )}
                      Unpublish
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-green-600 hover:bg-green-700 text-white"
                      onClick={() => updateProductStatus(product.id, 'Onsale')}
                      disabled={isUpdatingStatus === product.id}
                    >
                      {isUpdatingStatus === product.id ? (
                        <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                      ) : (
                        <Eye className="h-4 w-4 mr-1" />
                      )}
                      Republish
                    </Button>
                  )}
                </div>

                {/* Edit and Delete Buttons */}
                <div className="flex justify-between w-full">
                  <Button variant="outline" size="sm" onClick={() => router.push(`/vendor/products/${product.id}/edit`)}>
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-red-500 hover:text-red-700 hover:bg-red-50"
                    onClick={() => deleteProduct(product.id)}
                    disabled={isDeleting}
                  >
                    {isDeleting ? (
                      <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-1" />
                    )}
                    Delete
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
