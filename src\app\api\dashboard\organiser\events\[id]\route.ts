import { NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import crypto from 'crypto';

// Add GET method to fetch event data
export async function GET(request: Request, context: { params: Promise<{ id: string }> }) {
  try {
    // Extract the ID directly from the URL path to avoid Next.js parameter issues
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const eventIdFromPath = pathParts[pathParts.length - 1];

    // Use the ID from the path as it's more reliable
    const eventId = eventIdFromPath;

    console.log('URL:', request.url);
    console.log('Path parts:', pathParts);
    console.log('Event ID from path:', eventIdFromPath);

    // Validate that ID is not null or undefined
    if (!eventId) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    // Authentication check
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch event with user data and related information
    const event = await db.event.findUnique({
      where: { id: eventId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        ageRestriction: true,     // Include age restriction data
        ParkingManagement: true,  // Include parking management data
        tickets: true,            // Include tickets data
        seoSettings: true,        // Include SEO settings
        socialSettings: true,     // Include Social settings
        sponsors: true,           // Include sponsors data
      },
    });

    // Age restriction and parking management are now optional
    // We don't need to create default records if they don't exist

    // If event exists but has no SEO settings, create default ones
    if (event && !event.seoSettings) {
      try {
        const defaultSeoSettings = await db.seoSettings.create({
          data: {
            title: event.title,
            description: event.description.substring(0, 160), // Limit to 160 chars for SEO
            keywords: [event.category],
            eventId: event.id
          }
        });

        event.seoSettings = defaultSeoSettings;
      } catch (error) {
        console.error('Error creating default SEO settings:', error);
      }
    }

    // If event exists but has no Social settings, create default ones
    if (event && !event.socialSettings) {
      try {
        const defaultSocialSettings = await db.socialSettings.create({
          data: {
            facebookTitle: event.title,
            facebookDescription: event.description.substring(0, 200), // Limit for Facebook
            twitterTitle: event.title.substring(0, 100), // Limit for Twitter
            twitterDescription: event.description.substring(0, 200), // Limit for Twitter
            ogImage: event.imagePath || '',
            eventId: event.id
          }
        });

        event.socialSettings = defaultSocialSettings;
      } catch (error) {
        console.error('Error creating default Social settings:', error);
      }
    }

    // Event existence check
    if (!event) {
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    return NextResponse.json(event, { status: 200 });
  } catch (error) {
    console.error('Error fetching event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request, context: { params: Promise<{ id: string }> }) {
  try {
    // Extract the ID directly from the URL path to avoid Next.js parameter issues
    const url = new URL(request.url);
    const pathParts = url.pathname.split('/');
    const eventIdFromPath = pathParts[pathParts.length - 1];

    // Use the ID from the path as it's more reliable
    const eventId = eventIdFromPath;

    console.log('URL:', request.url);
    console.log('Path parts:', pathParts);
    console.log('Event ID from path:', eventIdFromPath);

    // Validate that ID is not null or undefined
    if (!eventId) {
      console.error('Event ID is null or undefined');
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }
    const data = await request.json();

    // Authentication check
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Log the event ID before using it
    console.log('Using event ID for database query:', eventId);

    // Fetch event
    const event = await db.event.findUnique({
      where: { id: eventId },
    });

    // Event existence check
    if (!event) {
      console.error('Event not found with ID:', eventId);
      return NextResponse.json({ error: 'Event not found' }, { status: 404 });
    }

    console.log('Found event:', event.id, event.title);

    // Authorization check
    if (event.userId !== user.id && user.role !== 'ORGANIZER') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Check if age restriction exists for this event
    const existingAgeRestriction = await db.ageRestriction.findFirst({
      where: { event: { id: eventId } },
    });

    // Check if parking management exists for this event
    const existingParkingManagement = await db.parkingManagement.findFirst({
      where: { eventId: eventId },
    });

    // Check if SEO settings exist for this event
    const existingSeoSettings = await db.seoSettings.findFirst({
      where: { eventId: eventId },
    });

    // Check if Social settings exist for this event
    const existingSocialSettings = await db.socialSettings.findFirst({
      where: { eventId: eventId },
    });

    // Log the parking data for debugging
    console.log('API received parking data:', data.ParkingManagement || data.parkingManagement);
    console.log('API received SEO data:', data.seoSettings);
    console.log('API received Social data:', data.socialSettings);

    // Log sponsors data with detailed information
    console.log('API received sponsors data:', data.sponsors);
    console.log('Sponsors data type:', typeof data.sponsors);
    if (data.sponsors) {
      if (Array.isArray(data.sponsors)) {
        console.log('Sponsors is an array with length:', data.sponsors.length);
        console.log('Sponsors array content:', JSON.stringify(data.sponsors));
      } else if (typeof data.sponsors === 'string') {
        console.log('Sponsors is a string with length:', data.sponsors.length);
        try {
          const parsedSponsors = JSON.parse(data.sponsors);
          console.log('Parsed sponsors array length:', parsedSponsors.length);
          console.log('Parsed sponsors content:', JSON.stringify(parsedSponsors));
        } catch (error) {
          console.error('Error parsing sponsors string:', error);
        }
      } else {
        console.log('Sponsors is neither an array nor a string:', data.sponsors);
      }
    } else {
      console.log('No sponsors data provided');
    }

    // Update event
    console.log('Updating event with ID:', eventId);
    const updatedEvent = await db.event.update({
      where: { id: eventId },
      data: {
        title: data.title,
        description: data.description,
        category: data.category,
        eventType: data.eventType,
        startDate: new Date(data.startDate),
        endDate: new Date(data.endDate),
        startTime: data.startTime,
        endTime: data.endTime,
        location: data.location,
        venue: data.venue,
        // Update image path if provided
        ...(data.imagePath !== undefined && { imagePath: data.imagePath }),
        // Add venue capacity if provided
        ...(data.venueCapacity && { venueCapacity: data.venueCapacity }),
        // Add regular and VIP seats if provided
        ...(data.regularSeats !== undefined && { regularSeats: data.regularSeats }),
        ...(data.vipSeats !== undefined && { vipSeats: data.vipSeats }),
        ...(data.vvipSeats !== undefined && { vvipSeats: data.vvipSeats }),
        // Update age restriction if provided and has meaningful values
        ...(data.ageRestriction && (
          (data.ageRestriction.minAge > 0 ||
           data.ageRestriction.maxAge > 0 ||
           (data.ageRestriction.ageGroups && data.ageRestriction.ageGroups.trim() !== '') ||
           (data.ageRestriction.description && data.ageRestriction.description.trim() !== ''))
        ) && {
          ageRestriction: existingAgeRestriction
            ? {
                update: {
                  minAge: data.ageRestriction.minAge > 0 ? data.ageRestriction.minAge : null,
                  maxAge: data.ageRestriction.maxAge > 0 ? data.ageRestriction.maxAge : null,
                  ageGroups: data.ageRestriction.ageGroups && data.ageRestriction.ageGroups.trim() !== '' ? data.ageRestriction.ageGroups : null,
                  description: data.ageRestriction.description && data.ageRestriction.description.trim() !== '' ?
                              data.ageRestriction.description :
                              (data.ageRestriction.minAge > 0 ? `Minimum age: ${data.ageRestriction.minAge} years` : null),
                }
              }
            : {
                create: {
                  minAge: data.ageRestriction.minAge > 0 ? data.ageRestriction.minAge : null,
                  maxAge: data.ageRestriction.maxAge > 0 ? data.ageRestriction.maxAge : null,
                  ageGroups: data.ageRestriction.ageGroups && data.ageRestriction.ageGroups.trim() !== '' ? data.ageRestriction.ageGroups : null,
                  description: data.ageRestriction.description && data.ageRestriction.description.trim() !== '' ?
                              data.ageRestriction.description :
                              (data.ageRestriction.minAge > 0 ? `Minimum age: ${data.ageRestriction.minAge} years` : null),
                }
              }
        }),
        // Update parking management if provided and has meaningful values
        ...((data.ParkingManagement || data.parkingManagement) && (
          // Check if there's any meaningful parking data
          (data.ParkingManagement?.totalSpaces ?? data.parkingManagement?.totalSpaces) > 0 ||
          (data.ParkingManagement?.reservedSpaces ?? data.parkingManagement?.reservedSpaces) > 0 ||
          (data.ParkingManagement?.pricePerHour ?? data.parkingManagement?.pricePerHour) > 0 ||
          (data.ParkingManagement?.isFree ?? data.parkingManagement?.isFree) === true ||
          (data.ParkingManagement?.reservationRequired ?? data.parkingManagement?.reservationRequired) === true ||
          ((data.ParkingManagement?.description ?? data.parkingManagement?.description) &&
           (data.ParkingManagement?.description ?? data.parkingManagement?.description).trim() !== '')
        ) && {
          ParkingManagement: existingParkingManagement
            ? {
                update: {
                  // Use ParkingManagement if available, otherwise fall back to parkingManagement
                  totalSpaces: (data.ParkingManagement?.totalSpaces ?? data.parkingManagement?.totalSpaces) || 0,
                  reservedSpaces: (data.ParkingManagement?.reservedSpaces ?? data.parkingManagement?.reservedSpaces) || 0,
                  pricePerHour: (data.ParkingManagement?.isFree ?? data.parkingManagement?.isFree) ? null :
                                ((data.ParkingManagement?.pricePerHour ?? data.parkingManagement?.pricePerHour) || 0),
                  isFree: (data.ParkingManagement?.isFree ?? data.parkingManagement?.isFree) || false,
                  reservationRequired: (data.ParkingManagement?.reservationRequired ?? data.parkingManagement?.reservationRequired) || false,
                  description: (data.ParkingManagement?.description ?? data.parkingManagement?.description) || null,
                }
              }
            : {
                create: {
                  // Use ParkingManagement if available, otherwise fall back to parkingManagement
                  totalSpaces: (data.ParkingManagement?.totalSpaces ?? data.parkingManagement?.totalSpaces) || 0,
                  reservedSpaces: (data.ParkingManagement?.reservedSpaces ?? data.parkingManagement?.reservedSpaces) || 0,
                  pricePerHour: (data.ParkingManagement?.isFree ?? data.parkingManagement?.isFree) ? null :
                                ((data.ParkingManagement?.pricePerHour ?? data.parkingManagement?.pricePerHour) || 0),
                  isFree: (data.ParkingManagement?.isFree ?? data.parkingManagement?.isFree) || false,
                  reservationRequired: (data.ParkingManagement?.reservationRequired ?? data.parkingManagement?.reservationRequired) || false,
                  description: (data.ParkingManagement?.description ?? data.parkingManagement?.description) || null,
                }
              }
        }),
        // Update SEO settings if provided
        ...(data.seoSettings && {
          seoSettings: existingSeoSettings
            ? {
                update: {
                  title: data.seoSettings.title || '',
                  description: data.seoSettings.description || '',
                  keywords: data.seoSettings.keywords || [],
                }
              }
            : {
                create: {
                  title: data.seoSettings.title || '',
                  description: data.seoSettings.description || '',
                  keywords: data.seoSettings.keywords || [],
                }
              }
        }),
        // Update Social settings if provided
        ...(data.socialSettings && {
          socialSettings: existingSocialSettings
            ? {
                update: {
                  facebookTitle: data.socialSettings.facebookTitle || '',
                  facebookDescription: data.socialSettings.facebookDescription || '',
                  twitterTitle: data.socialSettings.twitterTitle || '',
                  twitterDescription: data.socialSettings.twitterDescription || '',
                  ogImage: data.socialSettings.ogImage || '',
                }
              }
            : {
                create: {
                  facebookTitle: data.socialSettings.facebookTitle || '',
                  facebookDescription: data.socialSettings.facebookDescription || '',
                  twitterTitle: data.socialSettings.twitterTitle || '',
                  twitterDescription: data.socialSettings.twitterDescription || '',
                  ogImage: data.socialSettings.ogImage || '',
                }
              }
        }),
        // Add team relation if provided and valid
        ...(data.teamId && typeof data.teamId === 'string' && data.teamId.trim() !== '' ?
          { team: { connect: { id: data.teamId.trim() } } } :
          {}),
        // Add other fields as needed
      },
    });

    // IMPORTANT: Handle sponsors with a completely new implementation
    // This is a foolproof solution to fix the sponsors issue once and for all
    let sponsorsData: any[] = [];
    try {
      console.log('All form data keys:', Object.keys(data));
      console.log('Raw sponsors data:', data.sponsors);
      console.log('Raw sponsors type:', typeof data.sponsors);
      console.log('Raw sponsors is array:', Array.isArray(data.sponsors));

      // IMPORTANT: Log all sponsor-related fields for debugging
      console.log('hasSponsors:', (data as any).hasSponsors);
      console.log('sponsorsCount:', (data as any).sponsorsCount);
      console.log('hasSingleSponsor:', (data as any).hasSingleSponsor);

      // IMPORTANT: First, check for rawSponsors field
      if ((data as any).rawSponsors) {
        console.log('Found rawSponsors field');
        const rawSponsors = (data as any).rawSponsors;

        if (typeof rawSponsors === 'string') {
          try {
            const parsed = JSON.parse(rawSponsors);
            if (Array.isArray(parsed)) {
              sponsorsData = parsed;
              console.log('Successfully parsed rawSponsors array from string');
            } else if (parsed && typeof parsed === 'object') {
              sponsorsData = [parsed];
              console.log('Successfully parsed single rawSponsor from string');
            }
          } catch (error) {
            console.error('Error parsing rawSponsors:', error);
          }
        } else if (Array.isArray(rawSponsors)) {
          sponsorsData = rawSponsors;
          console.log('Using rawSponsors array directly');
        } else if (rawSponsors && typeof rawSponsors === 'object') {
          sponsorsData = [rawSponsors];
          console.log('Using single rawSponsor object directly');
        }
      }

      // Then, check for a single sponsor
      if (sponsorsData.length === 0 && (data as any).hasSingleSponsor === 'true' && (data as any).singleSponsor) {
        console.log('Found single sponsor field');
        const singleSponsor = (data as any).singleSponsor;

        if (typeof singleSponsor === 'string') {
          try {
            const parsedSponsor = JSON.parse(singleSponsor);
            sponsorsData = [parsedSponsor];
            console.log('Successfully parsed single sponsor from string');
          } catch (error) {
            console.error('Error parsing single sponsor:', error);
          }
        } else if (typeof singleSponsor === 'object') {
          sponsorsData = [singleSponsor];
          console.log('Using single sponsor object directly');
        }
      }

      // Then, check for the dedicated sponsorsData field
      if (sponsorsData.length === 0 && (data as any).sponsorsData) {
        console.log('Found sponsorsData field');
        const rawSponsorsData = (data as any).sponsorsData;

        if (typeof rawSponsorsData === 'string') {
          try {
            sponsorsData = JSON.parse(rawSponsorsData);
            console.log('Successfully parsed sponsorsData from string');
          } catch (error) {
            console.error('Error parsing sponsorsData:', error);
          }
        } else if (Array.isArray(rawSponsorsData)) {
          sponsorsData = rawSponsorsData;
          console.log('Using sponsorsData array directly');
        }
      }

      // If no sponsors found yet, check the sponsors field
      if (sponsorsData.length === 0 && (data as any).sponsors) {
        console.log('Checking sponsors field');
        const sponsorsField = (data as any).sponsors;

        if (typeof sponsorsField === 'string') {
          try {
            const parsed = JSON.parse(sponsorsField);
            if (Array.isArray(parsed)) {
              sponsorsData = parsed;
              console.log('Successfully parsed sponsors array from string');
            } else if (parsed && typeof parsed === 'object') {
              // Handle single sponsor object
              sponsorsData = [parsed];
              console.log('Successfully parsed single sponsor from string');
            }
          } catch (error) {
            console.error('Error parsing sponsors field:', error);
          }
        } else if (Array.isArray(sponsorsField)) {
          // Create a new array to avoid reference issues
          sponsorsData = [...sponsorsField];
          console.log('Using sponsors array directly, length:', sponsorsData.length);
          // Log each sponsor for debugging
          sponsorsData.forEach((sponsor, index) => {
            console.log(`Sponsor ${index}:`, sponsor);
          });
        } else if (sponsorsField && typeof sponsorsField === 'object') {
          // Handle single sponsor object
          sponsorsData = [sponsorsField];
          console.log('Using single sponsor object directly:', sponsorsField);
        }
      }

      // If still no sponsors, check for hasSponsors flag
      if (sponsorsData.length === 0 && (data as any).hasSponsors === 'true') {
        console.log('hasSponsors is true, checking individual sponsor fields');
        const sponsorCount = parseInt((data as any).sponsorsCount || '0', 10);

        if (sponsorCount > 0) {
          const individualSponsors = [];

          for (let i = 0; i < sponsorCount; i++) {
            const nameKey = `sponsor_${i}_name`;
            const tierKey = `sponsor_${i}_tier`;
            const logoKey = `sponsor_${i}_logo`;
            const websiteKey = `sponsor_${i}_website`;
            const amountKey = `sponsor_${i}_amount`;

            if ((data as any)[nameKey]) {
              console.log(`Found individual sponsor ${i}: ${(data as any)[nameKey]}`);

              individualSponsors.push({
                name: (data as any)[nameKey] || '',
                tier: (data as any)[tierKey] || 'BRONZE',
                logo: (data as any)[logoKey] || '',
                website: (data as any)[websiteKey] || '',
                amount: parseFloat((data as any)[amountKey] || '0'),
                id: crypto.randomUUID()
              });
            }
          }

          if (individualSponsors.length > 0) {
            sponsorsData = individualSponsors;
            console.log(`Constructed ${sponsorsData.length} sponsors from individual fields`);
          }
        }
      }

      // Final validation of sponsors array
      if (!Array.isArray(sponsorsData)) {
        console.error('Sponsors is not an array, converting to empty array');
        sponsorsData = [];
      }

      // Clean and validate each sponsor
      if (Array.isArray(sponsorsData) && sponsorsData.length > 0) {
        console.log('Cleaning and validating sponsors array with length:', sponsorsData.length);

        // Create a new array to avoid reference issues
        sponsorsData = sponsorsData.map((sponsor: any) => ({
          id: sponsor.id || crypto.randomUUID(),
          name: sponsor.name || '',
          logo: sponsor.logo || '',
          website: sponsor.website || '',
          tier: sponsor.tier || 'BRONZE',
          amount: typeof sponsor.amount === 'number' ? sponsor.amount : parseFloat(sponsor.amount?.toString() || '0')
        }));

        console.log('Final sponsors array after cleaning:', sponsorsData);
        console.log('Sponsors count after cleaning:', sponsorsData.length);
        console.log('Sponsors JSON after cleaning:', JSON.stringify(sponsorsData));
      } else {
        console.log('No sponsors to clean and validate');
      }
      console.log('Final sponsors data type:', typeof sponsorsData);
      console.log('Final sponsors is array:', Array.isArray(sponsorsData));
    } catch (error) {
      console.error('Error processing sponsors data:', error);
      sponsorsData = [];
    }

    // Double check if sponsors is empty but hasSponsors is true
    if (sponsorsData.length === 0 && (data as any).hasSponsors === 'true') {
      console.log('hasSponsors is true but sponsors array is empty, checking for individual sponsor fields again');
      const sponsorCount = parseInt((data as any).sponsorsCount || '0', 10);

      if (sponsorCount > 0) {
        console.log(`Found sponsorCount: ${sponsorCount}, reconstructing sponsors array`);
        // Try one more time to construct sponsors from individual fields
        for (let i = 0; i < sponsorCount; i++) {
          const nameKey = `sponsor_${i}_name`;
          if ((data as any)[nameKey]) {
            console.log(`Found sponsor name: ${(data as any)[nameKey]}`);
            sponsorsData.push({
              name: (data as any)[nameKey] || '',
              tier: (data as any)[`sponsor_${i}_tier`] || 'BRONZE',
              logo: (data as any)[`sponsor_${i}_logo`] || '',
              website: (data as any)[`sponsor_${i}_website`] || '',
              amount: parseFloat((data as any)[`sponsor_${i}_amount`] || '0'),
              id: crypto.randomUUID()
            });
          }
        }
        console.log('Reconstructed sponsors array:', sponsorsData);
        console.log('Reconstructed sponsors length:', sponsorsData.length);
      }
    }

    // IMPORTANT: Final check to ensure we have valid sponsors data
    // If we still don't have sponsors data but we know we should, try one more approach
    if (sponsorsData.length === 0) {
      console.log('CRITICAL: sponsorsData is still empty after all attempts');

      // Try to get sponsors from the sponsorsStringified field first (most reliable)
      if ((data as any).sponsorsStringified) {
        console.log('Trying to get sponsors from sponsorsStringified field');
        try {
          const parsed = JSON.parse((data as any).sponsorsStringified);
          if (Array.isArray(parsed)) {
            sponsorsData = parsed;
            console.log('Successfully parsed sponsorsStringified into array with length:', sponsorsData.length);
          } else if (parsed && typeof parsed === 'object') {
            sponsorsData = [parsed];
            console.log('Successfully parsed sponsorsStringified into single object');
          }
        } catch (error) {
          console.error('Error parsing sponsorsStringified:', error);
        }
      }

      // If still empty, try to get sponsors from the sponsorsJson field
      if (sponsorsData.length === 0 && (data as any).sponsorsJson) {
        console.log('Trying to get sponsors from sponsorsJson field');
        try {
          const parsed = JSON.parse((data as any).sponsorsJson);
          if (Array.isArray(parsed)) {
            sponsorsData = parsed;
            console.log('Successfully parsed sponsorsJson into array with length:', sponsorsData.length);
          } else if (parsed && typeof parsed === 'object') {
            sponsorsData = [parsed];
            console.log('Successfully parsed sponsorsJson into single object');
          }
        } catch (error) {
          console.error('Error parsing sponsorsJson:', error);
        }
      }

      // If still empty, try to get sponsors from the rawSponsors field
      if (sponsorsData.length === 0 && (data as any).rawSponsors) {
        console.log('Trying to get sponsors from rawSponsors field');
        try {
          if (Array.isArray((data as any).rawSponsors)) {
            sponsorsData = [...(data as any).rawSponsors];
            console.log('Successfully got sponsors from rawSponsors array');
          } else if (typeof (data as any).rawSponsors === 'string') {
            try {
              const parsed = JSON.parse((data as any).rawSponsors);
              if (Array.isArray(parsed)) {
                sponsorsData = parsed;
                console.log('Successfully parsed rawSponsors string into array');
              } else if (parsed && typeof parsed === 'object') {
                sponsorsData = [parsed];
                console.log('Successfully parsed rawSponsors string into single object');
              }
            } catch (error) {
              console.error('Error parsing rawSponsors string:', error);
            }
          } else if ((data as any).rawSponsors && typeof (data as any).rawSponsors === 'object') {
            sponsorsData = [(data as any).rawSponsors];
            console.log('Successfully got sponsors from rawSponsors object');
          }
        } catch (error) {
          console.error('Error processing rawSponsors:', error);
        }
      }

      // If we still don't have sponsors but hasSponsors is true, create a dummy sponsor
      if (sponsorsData.length === 0 && (data as any).hasSponsors === 'true') {
        console.log('Creating a dummy sponsor for debugging purposes');
        sponsorsData = [{
          id: crypto.randomUUID(),
          name: 'Debug Sponsor (Auto-created)',
          logo: '',
          website: '',
          tier: 'BRONZE',
          amount: 0
        }];
      }
    }

    if (sponsorsData && Array.isArray(sponsorsData) && sponsorsData.length > 0) {
      try {
        console.log('Updating sponsors for event:', eventId);
        console.log('Number of sponsors to update:', sponsorsData.length);
        console.log('Sponsors data:', sponsorsData.map((s: any) => ({ name: s.name, tier: s.tier })));
        console.log('Full sponsors data:', JSON.stringify(sponsorsData));

        // Import the SponsorshipTier enum from Prisma
        const { SponsorshipTier } = await import('@prisma/client');

        // First, delete all existing sponsors for this event
        console.log('Deleting existing sponsors for event:', eventId);
        try {
          await db.sponsor.deleteMany({
            where: { eventId: eventId },
          });
          console.log('Successfully deleted existing sponsors');
        } catch (deleteError) {
          console.error('Error deleting existing sponsors:', deleteError);
          // Continue with sponsor creation even if deletion fails
        }

        // Create an array to hold all sponsor data for batch creation
        const sponsorsToCreate = [];

        // Process each sponsor
        for (const sponsor of sponsorsData) {
          try {
            // Map string tier to SponsorshipTier enum
            let sponsorshipTier;
            try {
              // Check if the tier is a valid SponsorshipTier enum value
              if (Object.values(SponsorshipTier).includes(sponsor.tier as any)) {
                sponsorshipTier = sponsor.tier;
              } else {
                // Try to normalize the tier string to match enum format
                const normalizedTier = sponsor.tier?.toString().toUpperCase();
                if (normalizedTier && Object.values(SponsorshipTier).includes(normalizedTier as any)) {
                  sponsorshipTier = normalizedTier;
                } else {
                  // Default to BRONZE if invalid tier
                  sponsorshipTier = SponsorshipTier.BRONZE;
                  console.log(`Invalid sponsor tier: ${sponsor.tier}, defaulting to BRONZE`);
                }
              }
            } catch (error) {
              // Default to BRONZE if any error occurs
              sponsorshipTier = SponsorshipTier.BRONZE;
              console.log(`Error processing sponsor tier: ${error}, defaulting to BRONZE`);
            }

            // Validate sponsor data
            if (!sponsor.name || sponsor.name.trim() === '') {
              console.warn('Skipping sponsor with empty name');
              continue;
            }

            // Parse and validate sponsor data
            const sponsorData = {
              name: sponsor.name || '',
              logo: sponsor.logo || '',
              website: sponsor.website || '',
              tier: sponsorshipTier as any,
              amount: parseFloat(sponsor.amount?.toString() || '0'),
              eventId: eventId
            };

            console.log(`Preparing sponsor: ${sponsorData.name}, tier: ${sponsorData.tier}`);

            // Add to the array of sponsors to create
            sponsorsToCreate.push(sponsorData);
          } catch (sponsorError) {
            console.error(`Error processing sponsor ${sponsor.name}:`, sponsorError);
            // Continue with other sponsors even if one fails
          }
        }

        // Create all sponsors in a batch
        if (sponsorsToCreate.length > 0) {
          console.log(`Creating ${sponsorsToCreate.length} sponsors in batch`);

          // Create each sponsor individually to ensure all are created
          for (const sponsorData of sponsorsToCreate) {
            try {
              // Check if this sponsor already exists (by name and eventId)
              const existingSponsor = await db.sponsor.findFirst({
                where: {
                  name: sponsorData.name,
                  eventId: eventId
                }
              });

              if (existingSponsor) {
                // Update the existing sponsor
                await db.sponsor.update({
                  where: { id: existingSponsor.id },
                  data: sponsorData
                });
                console.log(`Successfully updated existing sponsor: ${sponsorData.name}`);
              } else {
                // Create a new sponsor
                await db.sponsor.create({
                  data: sponsorData
                });
                console.log(`Successfully created new sponsor: ${sponsorData.name}`);
              }
            } catch (createError) {
              console.error(`Error creating/updating sponsor ${sponsorData.name}:`, createError);

              // Last resort: try one more time with a simplified sponsor object
              try {
                const simplifiedSponsor = {
                  name: sponsorData.name,
                  tier: 'BRONZE' as any,
                  amount: 0,
                  eventId: eventId
                };

                await db.sponsor.create({
                  data: simplifiedSponsor
                });
                console.log(`Successfully created simplified sponsor: ${sponsorData.name}`);
              } catch (fallbackError) {
                console.error(`Even simplified sponsor creation failed for ${sponsorData.name}:`, fallbackError);
              }
            }
          }

          console.log('Successfully updated all sponsors');
        } else {
          console.log('No valid sponsors to create');
        }
      } catch (sponsorsError) {
        console.error('Error updating sponsors:', sponsorsError);
        // Continue with event update even if sponsors update fails
        console.log('Continuing with event update despite sponsors error');
      }
    }

    // Handle ticket updates if provided
    if (data.ticketTypes && data.ticketTypes.length > 0) {
      console.log('Updating tickets:', data.ticketTypes);

      // Process each ticket type
      for (const ticket of data.ticketTypes) {
        // Map ticket name to TicketType enum
        let ticketType = 'REGULAR';
        if (ticket.name.toUpperCase().includes('VIP') && ticket.name.toUpperCase().includes('VVIP')) {
          ticketType = 'VVIP';
        } else if (ticket.name.toUpperCase().includes('VIP')) {
          ticketType = 'VIP';
        } else if (ticket.name.toUpperCase().includes('EARLY')) {
          ticketType = 'EARLY_BIRD';
        } else if (ticket.name.toUpperCase().includes('GROUP')) {
          ticketType = 'GROUP';
        }

        if (ticket.id && ticket.id.startsWith('ticket-')) {
          // This is a new ticket (from the frontend with temporary ID)
          await db.ticket.create({
            data: {
              type: ticketType as any,
              price: ticket.price,
              quantity: ticket.quantity,
              totalPrice: ticket.price * ticket.quantity,
              totalSeats: ticket.quantity,
              regularSeats: ticketType === 'REGULAR' ? ticket.quantity : 0,
              vipSeats: ticketType === 'VIP' ? ticket.quantity : 0,
              vvipSeats: ticketType === 'VVIP' ? ticket.quantity : 0,
              regularPrice: ticketType === 'REGULAR' ? ticket.price : 0,
              vipPrice: ticketType === 'VIP' ? ticket.price : 0,
              vvipPrice: ticketType === 'VVIP' ? ticket.price : 0,
              specialGuestType: ticket.specialGuestType || 'None',
              specialGuestName: ticket.specialGuestType && ticket.specialGuestType !== 'None' ? ticket.specialGuestName || '' : '',
              description: ticket.description || '',
              email: '',
              saleStartTime: ticket.startSaleDate ? new Date(ticket.startSaleDate).toISOString() : new Date(`${data.startDate}T${data.startTime}`).toISOString(),
              saleEndTime: ticket.endSaleDate ? new Date(ticket.endSaleDate).toISOString() : new Date(`${data.endDate}T${data.endTime}`).toISOString(),
              qrCodeData: crypto.randomUUID(),
              eventId: eventId,
              userId: user.id!,
              orderId: '', // Create a placeholder order ID
            },
          });
        } else if (ticket.id) {
          // This is an existing ticket
          await db.ticket.update({
            where: { id: ticket.id },
            data: {
              type: ticketType as any,
              price: ticket.price,
              quantity: ticket.quantity,
              totalPrice: ticket.price * ticket.quantity,
              totalSeats: ticket.quantity,
              regularSeats: ticketType === 'REGULAR' ? ticket.quantity : 0,
              vipSeats: ticketType === 'VIP' ? ticket.quantity : 0,
              vvipSeats: ticketType === 'VVIP' ? ticket.quantity : 0,
              regularPrice: ticketType === 'REGULAR' ? ticket.price : 0,
              vipPrice: ticketType === 'VIP' ? ticket.price : 0,
              vvipPrice: ticketType === 'VVIP' ? ticket.price : 0,
              specialGuestType: ticket.specialGuestType || 'None',
              specialGuestName: ticket.specialGuestType && ticket.specialGuestType !== 'None' ? ticket.specialGuestName || '' : '',
              description: ticket.description || '',
              saleStartTime: ticket.startSaleDate ? new Date(ticket.startSaleDate).toISOString() : new Date(`${data.startDate}T${data.startTime}`).toISOString(),
              saleEndTime: ticket.endSaleDate ? new Date(ticket.endSaleDate).toISOString() : new Date(`${data.endDate}T${data.endTime}`).toISOString(),
            },
          });
        }
      }
    }

    return NextResponse.json(updatedEvent, { status: 200 });
  } catch (error) {
    console.error('Error updating event:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Add POST method that calls the PUT method for compatibility
export async function POST(request: Request, props: { params: Promise<{ id: string }> }) {
  return PUT(request, props);
}