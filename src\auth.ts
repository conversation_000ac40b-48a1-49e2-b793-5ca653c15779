import { getServerSession } from "next-auth"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import type { NextAuthOptions } from "next-auth"
import authConfig from "./auth.config"
import { db } from "./lib/prisma"
import { getUserById } from "./data/user"
import { getTwoFactorConfirmationByUserId } from "./data/two-factor-confirmation"
import { getAccountByUserId } from "./data/account"
import { generateAccessToken } from "./token"
import { UserSession } from "./types/session"

// For debugging
// Removed excessive logging that could cause issues in production

// Export a function to get the session on the server
export const getSession = async (): Promise<UserSession | null> => {
  return await getServerSession(fullAuthConfig) as UserSession | null;
};

// Export the auth config with callbacks and adapter
export const authOptions: NextAuthOptions = {
  providers: authConfig.providers,
  pages: {
    signIn: '/auth/login',
    error: '/auth/error',
  },
  debug: process.env.NODE_ENV === 'development',
  // We'll handle account linking in the callbacks
  events: {
    async linkAccount({ user }) {
      await db.user.update({
        where: { id: user.id },
        data: {
          emailVerified: new Date(),
          role: 'USER' // Set default role for OAuth users
        }
      })
    },
    async createUser({ user }) {
      // This event is triggered when a new user is created
      console.log('New user created:', user);
    }
  },
  callbacks: {
    async signIn({ user, account, profile, email }) {
      console.log("SignIn callback called with:", {
        provider: account?.provider,
        userId: user?.id,
        userEmail: user?.email,
        userRole: user?.role
      });

      // Allow OAuth without email verification
      if (account?.provider !== 'credentials') {
        console.log("OAuth sign-in detected");
        // If the user is signing in with OAuth and we have their email
        if (user.email) {
          // Check if a user with this email already exists
          const existingUser = await db.user.findUnique({
            where: { email: user.email },
          })

          // If we found a user with the same email, use that user's ID
          if (existingUser && existingUser.id !== user.id) {
            console.log(`Found existing user with email: ${user.email}, ID: ${existingUser.id}`);
            // Update the user object to use the existing user's ID
            user.id = existingUser.id;
          } else if (!existingUser && user.id) {
            // If no user exists with this email, create one
            try {
              console.log(`Creating new user for OAuth sign-in: ${user.email}`)
              await db.user.create({
                data: {
                  id: user.id,
                  name: user.name || 'User',
                  email: user.email,
                  role: 'USER', // Default role for new users
                  emailVerified: new Date(),
                  image: user.image,
                }
              });
              console.log(`User created successfully: ${user.id}`)
            } catch (error) {
              console.error(`Error creating user during OAuth sign-in:`, error)
              // Don't block sign-in if user creation fails
            }
          }

          // For new OAuth users, we'll handle role selection after account creation
          // The user will be redirected to the role selection page
        }
        return true
      }

      console.log("Credentials sign-in detected");

      if (!user.id) {
        console.log("No user ID provided, rejecting sign-in");
        return false
      }

      const existingUser = await getUserById(user.id)
      console.log("Existing user found:", existingUser ? {
        id: existingUser.id,
        email: existingUser.email,
        emailVerified: !!existingUser.emailVerified,
        role: existingUser.role,
        twoFactorEnabled: existingUser.isTwoFactorEnabled
      } : "null");

      // Prevent sign in without email verification
      if (!existingUser) {
        console.log("User not found in database");
        return false;
      }

      if (!existingUser.emailVerified) {
        console.log("Email not verified, rejecting sign-in");
        return false;
      }

      if (existingUser.isTwoFactorEnabled) {
        console.log("Two-factor authentication is enabled, checking confirmation");
        const twoFactorConfirmation = await getTwoFactorConfirmationByUserId(existingUser.id)

        // If no confirmation, block login
        if (!twoFactorConfirmation) {
          console.log("No two-factor confirmation found, rejecting sign-in");
          return false;
        }

        console.log("Two-factor confirmation found, deleting for next sign-in");
        // Delete two factor confirmation for next sign in
        await db.twoFactorConfirmation.delete({
          where: { userId: existingUser.id }
        })
      }

      console.log("Sign-in successful for user:", existingUser.email);
      return true
    },
    async session({ token, session }) {
      // First, ensure session and session.user exist
      if (!session) {
        console.error("Session is undefined in session callback");
        return {
          user: {},
          expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
        };
      }

      // Initialize user object if it doesn't exist
      if (!session.user) {
        session.user = {
          id: '',
          role: 'USER' as const,
          isTwoFactorEnabled: false,
          isOAuth: false,
          accountBalance: 0,
        };
      }

      // Now safely assign properties
      if (token?.sub) {
        session.user.id = token.sub;
      }

      if (token?.role) {
        session.user.role = token.role;
      }

      // Safely assign other properties
      session.user.isTwoFactorEnabled = token?.isTwoFactorEnabled ?? false;
      session.user.name = token?.name ?? '';
      session.user.email = token?.email ?? '';
      session.user.accountBalance = token?.accountBalance ?? 0;
      session.user.isOAuth = token?.isOAuth ?? false;

      if (token?.accessToken) {
        // Make sure we're not including any circular references or non-serializable data
        return {
          ...session,
          accessToken: token.accessToken
        };
      }

      return session;
    },
    async jwt({ token, account, user }) {
      // Ensure token exists
      if (!token || !token.sub) return token;

      if (user) {
        try {
          // Check if user exists in database
          const dbUser = await db.user.findUnique({
            where: { id: user.id },
          });

          if (!dbUser) {
            // User doesn't exist in database, create them
            try {
              await db.user.create({
                data: {
                  id: user.id,
                  name: user.name || 'User',
                  email: user.email || `user-${Date.now()}@example.com`,
                  role: user.role || 'USER',
                  emailVerified: new Date(),
                  image: user.image,
                },
              });
            } catch (error) {
              // Don't block token generation if user creation fails
              console.error(`Error creating user in JWT callback:`, error);
            }
          }

          // Generate access token when user logs in
          const accessToken = generateAccessToken(user)

          try {
            // Update user with new access token
            await db.user.update({
              where: { id: user.id },
              data: { accessToken }
            })

            token.accessToken = accessToken
          } catch (error) {
            // Don't block token generation if token update fails
            console.error(`Error updating access token:`, error);
          }
        } catch (error) {
          // Handle any unexpected errors in the JWT callback
          console.error("Error in JWT callback:", error);
          // Still return the token to prevent authentication failures
        }
      }

      const existingUser = await getUserById(token.sub)
      if (!existingUser) return token

      const existingAccount = await getAccountByUserId(existingUser.id)

      token.isOAuth = !!existingAccount
      token.name = existingUser.name
      token.email = existingUser.email
      token.accountBalance = existingUser.accountBalance

      // Check if this user is a partner
      const partner = await db.partner.findUnique({
        where: { userId: existingUser.id },
      });

      if (partner && existingUser.role !== 'PARTNER') {
        // If user is a partner but role is not set, update it
        await db.user.update({
          where: { id: existingUser.id },
          data: { role: 'PARTNER' }
        });
        token.role = 'PARTNER';
      } else if (existingUser.role === 'ADMIN' || existingUser.role === 'ORGANIZER' || existingUser.role === 'VENDOR' ||
                existingUser.role === 'USER' || existingUser.role === 'DEVELOPER' || existingUser.role === 'SUPERADMIN' ||
                existingUser.role === 'PARTNER') {
        token.role = existingUser.role
      } else {
        token.role = 'USER' // default to 'USER' if the role is not recognized
      }

      token.isTwoFactorEnabled = existingUser.isTwoFactorEnabled

      return token
    }
  },
  adapter: PrismaAdapter(db),
  session: {
    strategy: "jwt",
    maxAge: 7 * 24 * 60 * 60, // 7 days in seconds
  },
};

// Merge the auth options with the auth config
export const fullAuthConfig: NextAuthOptions = {
  ...authConfig,
  ...authOptions,
};

// Export signIn function for server components
export const signIn = async (provider: string, credentials: any) => {
  try {
    // This is a server-side implementation of signIn
    // It's used in server actions to authenticate users
    if (provider === 'credentials') {
      const { email, password } = credentials;

      // Find the user by email
      const user = await db.user.findUnique({
        where: { email }
      });

      if (!user || !user.password) {
        return { error: 'InvalidCredentials' };
      }

      // Verify the password using bcrypt
      const { compare } = await import('bcryptjs');
      const isPasswordValid = await compare(password, user.password);

      if (!isPasswordValid) {
        return { error: 'CredentialsSignin' };
      }

      // Check if email is verified
      if (!user.emailVerified) {
        return { error: 'EmailNotVerified' };
      }

      // Generate access token
      const accessToken = generateAccessToken(user);

      // Update user with new access token
      await db.user.update({
        where: { id: user.id },
        data: { accessToken }
      });

      return { ok: true, user };
    }

    // For other providers, we would implement OAuth flows
    return { error: 'UnsupportedProvider' };
  } catch (error) {
    console.error('Error in signIn function:', error);
    return { error: 'InternalServerError' };
  }
};
