#!/usr/bin/env node

/**
 * Fix all instances of userId: user.id to userId: user.id! in Prisma create operations
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing all user.id type assertions in the codebase...\n');

// Find all TypeScript files in src directory
function findAllTsFiles(dir) {
  const files = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and .next directories
        if (!item.startsWith('.') && item !== 'node_modules') {
          scanDirectory(fullPath);
        }
      } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
        files.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return files;
}

// Check if file contains userId: user.id patterns
function needsUpdate(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  return content.includes('userId: user.id,') || content.includes('userId: user.id\n');
}

// Update file to fix user.id type assertions
function updateFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let updated = false;
  
  // Replace userId: user.id with userId: user.id!
  const patterns = [
    /userId:\s*user\.id,/g,
    /userId:\s*user\.id\s*$/gm,
    /userId:\s*user\.id\s*\n/g
  ];
  
  const replacements = [
    'userId: user.id!,',
    'userId: user.id!',
    'userId: user.id!\n'
  ];
  
  for (let i = 0; i < patterns.length; i++) {
    const originalContent = content;
    content = content.replace(patterns[i], replacements[i]);
    if (content !== originalContent) {
      updated = true;
    }
  }
  
  if (updated) {
    fs.writeFileSync(filePath, content);
    console.log(`✅ Fixed: ${path.relative(process.cwd(), filePath)}`);
    return true;
  }
  
  return false;
}

// Main execution
try {
  const srcDir = path.join(__dirname, '..', 'src');
  console.log(`Searching for TypeScript files in ${srcDir}...`);
  
  const allFiles = findAllTsFiles(srcDir);
  console.log(`Found ${allFiles.length} TypeScript files\n`);
  
  let updatedCount = 0;
  
  for (const filePath of allFiles) {
    if (needsUpdate(filePath)) {
      if (updateFile(filePath)) {
        updatedCount++;
      }
    }
  }
  
  console.log(`\n🎉 Fixed ${updatedCount} files with user.id type issues!`);
  
  if (updatedCount === 0) {
    console.log('✨ No files needed updating - all user.id references are already correctly typed!');
  }
  
} catch (error) {
  console.error('❌ Error fixing user.id types:', error);
  process.exit(1);
}
