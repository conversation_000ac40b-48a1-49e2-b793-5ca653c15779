import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/vendors
 * Get all vendors with NFC terminal settings
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const pageSize = parseInt(searchParams.get('pageSize') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const verification = searchParams.get('verification') || 'all';
    const sortField = searchParams.get('sortField') || 'businessName';
    const sortDirection = searchParams.get('sortDirection') || 'asc';

    // Calculate pagination
    const skip = (page - 1) * pageSize;

    // Build where clause
    const whereClause: any = {
      // We'll filter for vendors with NFC terminal settings later
    };

    // Add search filter
    if (search) {
      whereClause.OR = [
        { businessName: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } },
        { user: { email: { contains: search, mode: 'insensitive' } } },
        { phoneNumber: { contains: search } }
      ];
    }

    // We'll handle status filter after fetching the vendors

    // Add verification filter
    if (verification !== 'all') {
      whereClause.verificationStatus = verification;
    }

    // Get total count for pagination - we'll adjust this after filtering
    const totalCount = await db.vendorProfile.count({
      where: whereClause
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get vendors with pagination, sorting, and filtering
    const vendors = await db.vendorProfile.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            nfcTransactions: true
          }
        },
        nfcTransactions: {
          take: 1,
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            createdAt: true
          }
        }
      },
      orderBy: {
        [sortField === 'email' ? 'user.email' :
         sortField === 'name' ? 'user.name' :
         sortField]: sortDirection === 'asc' ? 'asc' : 'desc'
      },
      skip,
      take: pageSize
    });

    // Get terminal settings for all vendors
    const vendorUserIds = vendors.map(vendor => vendor.userId);
    const terminalSettings = await db.nFCTerminalSettings.findMany({
      where: {
        vendorId: {
          in: vendorUserIds
        }
      }
    });

    // Create a map of user ID to terminal settings
    const terminalSettingsByUserId = new Map();
    terminalSettings.forEach(settings => {
      terminalSettingsByUserId.set(settings.vendorId, settings);
    });

    // Calculate total revenue for each vendor
    const vendorIds = vendors.map(vendor => vendor.id);
    const transactionSums = await db.vendorNFCTransaction.groupBy({
      by: ['vendorId'],
      where: {
        vendorId: {
          in: vendorIds
        },
        status: 'COMPLETED'
      },
      _sum: {
        amount: true
      }
    });

    // Create a map of vendor ID to total revenue
    const revenueByVendor = new Map();
    transactionSums.forEach(sum => {
      revenueByVendor.set(sum.vendorId, sum._sum.amount || 0);
    });

    // Format vendor data for response
    const formattedVendors = vendors.map(vendor => {
      const terminalSettings = terminalSettingsByUserId.get(vendor.userId);
      const lastActive = vendor.nfcTransactions[0]?.createdAt || null;
      const totalRevenue = revenueByVendor.get(vendor.id) || 0;

      // Apply status filter if needed
      if (status !== 'all') {
        const isOffline = terminalSettings?.offlineMode || false;
        if ((status === 'offline' && !isOffline) || (status === 'online' && isOffline)) {
          return null; // Skip this vendor if it doesn't match the status filter
        }
      }

      return {
        id: vendor.id,
        userId: vendor.userId,
        businessName: vendor.businessName,
        email: vendor.user.email,
        phone: vendor.phoneNumber || '',
        status: terminalSettings ? (terminalSettings.offlineMode ? 'offline' : 'online') : 'inactive',
        verificationStatus: vendor.verificationStatus,
        terminalId: terminalSettings?.id || null,
        terminalName: terminalSettings?.terminalName || null,
        deviceId: terminalSettings?.deviceId || null,
        lastActive: lastActive ? lastActive.toISOString() : null,
        transactionCount: vendor._count.nfcTransactions,
        totalRevenue,
        offlineMode: terminalSettings?.offlineMode || false,
        autoSync: terminalSettings?.autoSync || true,
        notificationsEnabled: terminalSettings?.notificationsEnabled || true,
        autoPrint: terminalSettings?.autoPrint || false
      };
    }).filter(Boolean); // Remove null entries (filtered out vendors)

    // Adjust total count and pages based on filtered vendors if status filter is applied
    const filteredCount = status !== 'all' ? formattedVendors.length : totalCount;
    const filteredTotalPages = Math.ceil(filteredCount / pageSize);

    return NextResponse.json({
      vendors: formattedVendors,
      totalCount: filteredCount,
      totalPages: filteredTotalPages,
      currentPage: page
    });
  } catch (error) {
    console.error('Error fetching NFC vendors:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC vendors' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/nfc/vendors
 * Create a new vendor with NFC terminal settings
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      userId,
      terminalName,
      offlineMode,
      autoSync,
      notificationsEnabled,
      autoPrint,
      deviceId
    } = body;

    // Check if user exists and is a vendor
    const vendorUser = await db.user.findUnique({
      where: {
        id: userId,
        role: 'VENDOR'
      },
      include: {
        vendorProfile: true
      }
    });

    if (!vendorUser) {
      return NextResponse.json(
        { error: 'Vendor user not found' },
        { status: 404 }
      );
    }

    // Check if vendor already has NFC terminal settings
    const existingSettings = await db.nFCTerminalSettings.findUnique({
      where: {
        vendorId: userId
      }
    });

    if (existingSettings) {
      return NextResponse.json(
        { error: 'Vendor already has NFC terminal settings' },
        { status: 400 }
      );
    }

    // Create NFC terminal settings
    const terminalSettings = await db.nFCTerminalSettings.create({
      data: {
        vendorId: userId,
        terminalName: terminalName || `${vendorUser.name} Terminal`,
        offlineMode: offlineMode || false,
        autoSync: autoSync !== undefined ? autoSync : true,
        notificationsEnabled: notificationsEnabled !== undefined ? notificationsEnabled : true,
        autoPrint: autoPrint || false,
        deviceId: deviceId || `device-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
        softwareVersion: '1.0.0'
      }
    });

    return NextResponse.json({
      success: true,
      terminalSettings
    });
  } catch (error) {
    console.error('Error creating NFC vendor terminal settings:', error);
    return NextResponse.json(
      { error: 'Failed to create NFC vendor terminal settings' },
      { status: 500 }
    );
  }
}
