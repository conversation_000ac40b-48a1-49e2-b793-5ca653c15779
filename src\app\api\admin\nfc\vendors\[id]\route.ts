import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/vendors/[id]
 * Get a specific vendor with NFC terminal settings
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const vendorId = resolvedParams.id;

    // Get vendor with NFC terminal settings
    const vendor = await db.vendorProfile.findUnique({
      where: {
        id: vendorId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        _count: {
          select: {
            nfcTransactions: true
          }
        },
        nfcTransactions: {
          take: 5,
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            event: {
              select: {
                id: true,
                title: true
              }
            }
          }
        }
      }
    });

    // Get terminal settings separately
    const terminalSettings = vendor ? await db.nFCTerminalSettings.findUnique({
      where: {
        vendorId: vendor.userId
      }
    }) : null;

    if (!vendor) {
      return NextResponse.json(
        { error: 'Vendor not found' },
        { status: 404 }
      );
    }

    // Calculate total revenue
    const revenueResult = await db.vendorNFCTransaction.aggregate({
      where: {
        vendorId: vendor.id,
        status: 'COMPLETED'
      },
      _sum: {
        amount: true
      }
    });

    const totalRevenue = revenueResult._sum.amount || 0;

    // Format vendor data for response
    const formattedVendor = {
      id: vendor.id,
      userId: vendor.userId,
      businessName: vendor.businessName,
      email: vendor.user.email,
      phone: vendor.phoneNumber || '',
      status: terminalSettings ?
        (terminalSettings.offlineMode ? 'offline' : 'online') :
        'inactive',
      verificationStatus: vendor.verificationStatus,
      terminalSettings: terminalSettings,
      transactionCount: vendor._count.nfcTransactions,
      totalRevenue,
      recentTransactions: vendor.nfcTransactions.map(tx => ({
        id: tx.id,
        amount: tx.amount,
        status: tx.status,
        createdAt: tx.createdAt.toISOString(),
        event: tx.event ? {
          id: tx.event.id,
          title: tx.event.title
        } : null
      }))
    };

    return NextResponse.json(formattedVendor);
  } catch (error) {
    console.error('Error fetching NFC vendor:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC vendor' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/nfc/vendors/[id]
 * Update a vendor's NFC terminal settings
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const vendorId = resolvedParams.id;
    const body = await request.json();

    // Get vendor
    const vendor = await db.vendorProfile.findUnique({
      where: {
        id: vendorId
      },
      include: {
        user: true
      }
    });

    if (!vendor) {
      return NextResponse.json(
        { error: 'Vendor not found' },
        { status: 404 }
      );
    }

    // Update terminal settings if they exist
    if (body.terminalSettings) {
      const {
        terminalName,
        offlineMode,
        autoSync,
        notificationsEnabled,
        autoPrint
      } = body.terminalSettings;

      // Check if terminal settings exist
      const existingSettings = await db.nFCTerminalSettings.findUnique({
        where: {
          vendorId: vendor.userId
        }
      });

      if (existingSettings) {
        // Update existing settings
        await db.nFCTerminalSettings.update({
          where: {
            id: existingSettings.id
          },
          data: {
            terminalName: terminalName !== undefined ? terminalName : existingSettings.terminalName,
            offlineMode: offlineMode !== undefined ? offlineMode : existingSettings.offlineMode,
            autoSync: autoSync !== undefined ? autoSync : existingSettings.autoSync,
            notificationsEnabled: notificationsEnabled !== undefined ? notificationsEnabled : existingSettings.notificationsEnabled,
            autoPrint: autoPrint !== undefined ? autoPrint : existingSettings.autoPrint,
          }
        });
      } else {
        // Create new settings if they don't exist
        await db.nFCTerminalSettings.create({
          data: {
            vendorId: vendor.userId,
            terminalName: terminalName || `${vendor.businessName} Terminal`,
            offlineMode: offlineMode || false,
            autoSync: autoSync !== undefined ? autoSync : true,
            notificationsEnabled: notificationsEnabled !== undefined ? notificationsEnabled : true,
            autoPrint: autoPrint || false,
            deviceId: `device-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            softwareVersion: '1.0.0'
          }
        });
      }
    }

    // Update vendor profile if needed
    if (body.businessName || body.phone || body.status) {
      await db.vendorProfile.update({
        where: {
          id: vendorId
        },
        data: {
          businessName: body.businessName || vendor.businessName,
          phoneNumber: body.phone || vendor.phoneNumber
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Vendor updated successfully'
    });
  } catch (error) {
    console.error('Error updating NFC vendor:', error);
    return NextResponse.json(
      { error: 'Failed to update NFC vendor' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/nfc/vendors/[id]
 * Remove a vendor from the NFC system
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await params;
    const vendorId = resolvedParams.id;

    // Get vendor
    const vendor = await db.vendorProfile.findUnique({
      where: {
        id: vendorId
      },
      include: {
        user: true
      }
    });

    if (!vendor) {
      return NextResponse.json(
        { error: 'Vendor not found' },
        { status: 404 }
      );
    }

    // Delete terminal settings
    await db.nFCTerminalSettings.deleteMany({
      where: {
        vendorId: vendor.userId
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Vendor removed from NFC system'
    });
  } catch (error) {
    console.error('Error removing NFC vendor:', error);
    return NextResponse.json(
      { error: 'Failed to remove NFC vendor' },
      { status: 500 }
    );
  }
}
