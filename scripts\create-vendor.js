// scripts/create-vendor.js
const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');

const prisma = new PrismaClient();

async function main() {
  console.log('Creating vendor user...');
  
  try {
    // Create a vendor user
    const vendor = await prisma.user.create({
      data: {
        name: 'Test Vendor',
        email: '<EMAIL>',
        role: 'VENDOR',
        emailVerified: new Date(),
        // Create NFC Terminal Settings for the vendor
        nfcTerminalSettings: {
          create: {
            deviceId: `TERM-${Math.floor(Math.random() * 10000).toString().padStart(4, '0')}`,
            terminalName: 'Main Terminal',
            offlineMode: false,
            autoSync: true,
            notificationsEnabled: true,
            autoPrint: false,
            softwareVersion: '1.0.0',
          }
        }
      },
      include: {
        nfcTerminalSettings: true,
      }
    });
    
    console.log('Vendor user created successfully:');
    console.log({
      id: vendor.id,
      name: vendor.name,
      email: vendor.email,
      role: vendor.role,
      nfcTerminalSettings: vendor.nfcTerminalSettings ? {
        id: vendor.nfcTerminalSettings.id,
        deviceId: vendor.nfcTerminalSettings.deviceId,
        terminalName: vendor.nfcTerminalSettings.terminalName,
      } : null,
    });
    
    return vendor;
  } catch (error) {
    console.error('Error creating vendor user:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
