import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { POSRentalStatus } from '@prisma/client';
import { 
  createPOSDeviceRental, 
  completePOSDeviceRental,
  processPOSRentalPayment
} from '@/lib/pos-device-management';

/**
 * GET /api/admin/pos-rentals
 * Get all POS device rentals
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') as POSRentalStatus | null;
    const vendorId = searchParams.get('vendorId');
    const eventId = searchParams.get('eventId');
    
    // Build query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    
    if (vendorId) {
      query.vendorId = vendorId;
    }
    
    if (eventId) {
      query.eventId = eventId;
    }
    
    // Get POS device rentals
    const rentals = await db.pOSDeviceRental.findMany({
      where: query,
      include: {
        device: true,
        vendor: true,
        event: true,
        transactions: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(rentals);
  } catch (error) {
    console.error('Error getting POS device rentals:', error);
    return NextResponse.json(
      { error: 'Failed to get POS device rentals' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/pos-rentals
 * Create a new POS device rental
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();
    
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      deviceId,
      vendorId,
      eventId,
      rentalStartDate,
      rentalEndDate,
      depositAmount,
      notes
    } = body;
    
    // Validate required fields
    if (!deviceId || !vendorId || !rentalStartDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Create POS device rental
    const rental = await createPOSDeviceRental({
      deviceId,
      vendorId,
      eventId,
      rentalStartDate: new Date(rentalStartDate),
      rentalEndDate: rentalEndDate ? new Date(rentalEndDate) : undefined,
      depositAmount: depositAmount ? parseFloat(depositAmount) : undefined,
      notes
    });
    
    return NextResponse.json(rental, { status: 201 });
  } catch (error) {
    console.error('Error creating POS device rental:', error);
    return NextResponse.json(
      { error: 'Failed to create POS device rental' },
      { status: 500 }
    );
  }
}

// Custom functions removed - these should be implemented as separate dynamic routes
// GET_RENTAL -> /api/admin/pos-rentals/[id]/route.ts
// PATCH_COMPLETE -> /api/admin/pos-rentals/[id]/complete/route.ts

// Custom function removed - this should be implemented as a separate dynamic route
// PATCH_PROCESS_PAYMENT -> /api/admin/pos-rentals/transactions/[id]/process/route.ts

/**
 * DELETE /api/admin/pos-rentals?id=:id
 * Cancel a POS device rental
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Rental ID is required' }, { status: 400 });
    }
    
    // Get the rental
    const rental = await db.pOSDeviceRental.findUnique({
      where: { id },
      include: {
        device: true
      }
    });
    
    if (!rental) {
      return NextResponse.json(
        { error: 'POS device rental not found' },
        { status: 404 }
      );
    }
    
    // Cancel rental in a transaction
    await db.$transaction(async (prisma) => {
      // Update device status
      await prisma.pOSDevice.update({
        where: {
          id: rental.deviceId
        },
        data: {
          status: 'AVAILABLE'
        }
      });
      
      // Update rental
      await prisma.pOSDeviceRental.update({
        where: {
          id
        },
        data: {
          status: 'CANCELLED'
        }
      });
      
      // Update transactions
      await prisma.pOSRentalTransaction.updateMany({
        where: {
          rentalId: id,
          status: 'PENDING'
        },
        data: {
          status: 'CANCELLED'
        }
      });
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error cancelling POS device rental:', error);
    return NextResponse.json(
      { error: 'Failed to cancel POS device rental' },
      { status: 500 }
    );
  }
}
