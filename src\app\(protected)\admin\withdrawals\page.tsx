import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import WithdrawalsPageClient from '@/components/admin/withdrawals-page-client';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
  };
};

export default async function AdminWithdrawalsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only admins can access this page
  if (session.user.role !== 'ADMIN' && session.user.role !== 'SUPERADMIN') {
    redirect('/dashboard');
  }

  return (
    <Suspense fallback={<div className="container mx-auto py-8">Loading withdrawals...</div>}>
      <WithdrawalsPageClient />
    </Suspense>
  );
}
