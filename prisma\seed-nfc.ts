import { PrismaClient, TransactionStatus } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

async function main() {
  console.log('Starting to seed NFC data...');

  // Get all events with NFC system enabled
  // First get all events with NFC system settings
  const nfcSettings = await prisma.nFCSystemSettings.findMany({
    select: {
      eventId: true
    }
  });

  const events = await prisma.event.findMany({
    where: {
      id: {
        in: nfcSettings.map(setting => setting.eventId)
      }
    },
    select: {
      id: true,
      title: true,
    }
  });

  if (events.length === 0) {
    console.log('No events with NFC system found. Creating a sample event with NFC system...');

    // Find an organizer
    const organizer = await prisma.user.findFirst({
      where: {
        role: 'ORGANIZER'
      }
    });

    if (!organizer) {
      console.log('No organizer found. Please create an organizer account first.');
      return;
    }

    // Create a sample event with NFC system
    const event = await prisma.event.create({
      data: {
        title: 'Sample Event with NFC System',
        description: 'This is a sample event with NFC system enabled for testing.',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        endDate: new Date(Date.now() + 8 * 24 * 60 * 60 * 1000), // 8 days from now
        location: 'Sample Location',
        venue: 'Sample Venue',
        startTime: '10:00 AM',
        endTime: '6:00 PM',
        eventType: 'PHYSICAL',
        category: 'MUSIC',
        status: 'Published',
        userId: organizer.id
      }
    });

    // Create NFC system settings for the event
    await prisma.nFCSystemSettings.create({
      data: {
        eventId: event.id,
        systemName: 'Event NFC Payment System',
        currencySymbol: 'K',
        defaultLanguage: 'en',
        maxTransactionAmount: 500,
        requirePinForHighValue: true,
        highValueThreshold: 100,
        cardLockoutThreshold: 3,
        offlineModeEnabled: true,
        maxOfflineTransactions: 50,
        offlineTransactionLimit: 50,
        syncInterval: 15,
        receiptEnabled: true,
        analyticsEnabled: true
      }
    });

    console.log(`Created sample event: ${event.title}`);
    events.push({ id: event.id, title: event.title });
  }

  console.log(`Found ${events.length} events with NFC system.`);

  // Get all vendors
  const vendors = await prisma.vendorProfile.findMany({
    where: {
      verificationStatus: 'APPROVED'
    },
    select: {
      id: true,
      businessName: true,
      userId: true
    }
  });

  if (vendors.length === 0) {
    console.log('No approved vendors found. Creating a sample vendor...');

    // Find a user to associate with the vendor
    let user = await prisma.user.findFirst({
      where: {
        OR: [
          { role: 'VENDOR' },
          { role: 'USER' }
        ]
      }
    });

    if (!user) {
      console.log('No suitable user found. Creating a new user...');

      // Create a new user
      user = await prisma.user.create({
        data: {
          name: 'Test User',
          email: '<EMAIL>',
          password: 'password123', // In a real app, this would be hashed
          role: 'USER',
          isTwoFactorEnabled: false
        }
      });

      console.log(`Created new user: ${user.email}`);
    }

    // Create a vendor profile
    const vendor = await prisma.vendorProfile.create({
      data: {
        userId: user.id,
        businessName: 'Sample Food Vendor',
        businessType: 'Food Service',
        description: 'A sample food vendor for testing NFC transactions',
        phoneNumber: faker.phone.number(),
        email: user.email || faker.internet.email(),
        website: faker.internet.url(),
        physicalAddress: faker.location.streetAddress(),
        city: 'Lusaka',
        province: 'Lusaka',
        postalCode: faker.location.zipCode(),
        productCategories: 'FOOD_AND_BEVERAGES',
        verificationStatus: 'APPROVED',
        verifiedAt: new Date(),
        featured: true
      }
    });

    console.log(`Created sample vendor: ${vendor.businessName}`);
    vendors.push(vendor);
  }

  console.log(`Found ${vendors.length} approved vendors.`);

  // Create NFC cards
  const existingCards = await prisma.nFCCard.count();

  if (existingCards === 0) {
    console.log('Creating NFC cards...');

    // Create 50 NFC cards
    for (let i = 0; i < 50; i++) {
      await prisma.nFCCard.create({
        data: {
          uid: faker.string.alphanumeric(10).toUpperCase(),
          isActive: true,
          status: 'active',
          balance: faker.number.float({ min: 0, max: 1000, fractionDigits: 2 }),
          eventId: events[0].id
        }
      });
    }

    console.log('Created 50 NFC cards.');
  } else {
    console.log(`Found ${existingCards} existing NFC cards.`);
  }

  // Get all NFC cards
  const cards = await prisma.nFCCard.findMany({
    select: {
      id: true,
      uid: true
    }
  });

  console.log(`Found ${cards.length} NFC cards.`);

  // Get all users
  const users = await prisma.user.findMany({
    where: {
      role: 'USER'
    },
    select: {
      id: true,
      name: true,
      email: true
    }
  });

  if (users.length === 0) {
    console.log('No users found. Please create user accounts first.');
    return;
  }

  console.log(`Found ${users.length} users.`);

  // Create products for vendors if they don't have any
  for (const vendor of vendors) {
    const productCount = await prisma.product.count({
      where: {
        vendorId: vendor.id
      }
    });

    if (productCount === 0) {
      console.log(`Creating products for vendor: ${vendor.businessName}`);

      // Create 5-10 products for each vendor
      const numProducts = faker.number.int({ min: 5, max: 10 });

      for (let i = 0; i < numProducts; i++) {
        await prisma.product.create({
          data: {
            name: faker.commerce.productName(),
            description: faker.commerce.productDescription(),
            price: parseFloat(faker.commerce.price({ min: 10, max: 100 })),
            stockQuantity: faker.number.int({ min: 10, max: 100 }),
            vendorId: vendor.id,
            userId: vendor.userId,
            category: 'FOOD_AND_BEVERAGES',
            productType: 'PHYSICAL',
            status: 'Onsale',
            dimensions: faker.commerce.productMaterial(),
            material: faker.commerce.productMaterial(),
            weight: faker.number.float({ min: 0.1, max: 10, fractionDigits: 1 })
          }
        });
      }
    }
  }

  // Get all products
  const productsByVendor = await Promise.all(
    vendors.map(async (vendor) => {
      const products = await prisma.product.findMany({
        where: {
          vendorId: vendor.id
        },
        select: {
          id: true,
          name: true,
          price: true
        }
      });

      return {
        vendorId: vendor.id,
        products
      };
    })
  );

  // Create NFC transactions
  console.log('Creating NFC transactions...');

  const transactionsToCreate = 100;
  const statuses: TransactionStatus[] = [TransactionStatus.COMPLETED, TransactionStatus.FAILED, TransactionStatus.PENDING, TransactionStatus.REFUNDED, TransactionStatus.CANCELLED];
  const statusWeights = [0.8, 0.05, 0.05, 0.05, 0.05]; // 80% completed, 5% each for others

  for (let i = 0; i < transactionsToCreate; i++) {
    // Select a random event, vendor, card, and user
    const event = events[Math.floor(Math.random() * events.length)];
    const vendorWithProducts = productsByVendor[Math.floor(Math.random() * productsByVendor.length)];
    const card = cards[Math.floor(Math.random() * cards.length)];
    const user = users[Math.floor(Math.random() * users.length)];

    // Select 1-5 products from the vendor
    const numProducts = faker.number.int({ min: 1, max: 5 });
    const selectedProducts = [];
    let totalAmount = 0;

    for (let j = 0; j < numProducts; j++) {
      if (vendorWithProducts.products.length === 0) continue;

      const product = vendorWithProducts.products[Math.floor(Math.random() * vendorWithProducts.products.length)];
      const quantity = faker.number.int({ min: 1, max: 3 });
      const unitPrice = product.price;
      const totalPrice = unitPrice * quantity;

      selectedProducts.push({
        productId: product.id,
        quantity,
        unitPrice,
        totalPrice
      });

      totalAmount += totalPrice;
    }

    if (selectedProducts.length === 0) continue;

    // Determine status based on weights
    const randomValue = Math.random();
    let cumulativeWeight = 0;
    let statusIndex = 0;

    for (let j = 0; j < statusWeights.length; j++) {
      cumulativeWeight += statusWeights[j];
      if (randomValue <= cumulativeWeight) {
        statusIndex = j;
        break;
      }
    }

    const status = statuses[statusIndex];

    // Create the transaction
    const createdAt = faker.date.past({ years: 0.5 });
    const processedAt = status === TransactionStatus.COMPLETED || status === TransactionStatus.REFUNDED
      ? faker.date.between({ from: createdAt, to: new Date() })
      : null;

    await prisma.vendorNFCTransaction.create({
      data: {
        vendorId: vendorWithProducts.vendorId,
        eventId: event.id,
        cardId: card.id,
        userId: user.id,
        amount: totalAmount,
        currency: 'ZMW',
        status,
        reference: faker.string.alphanumeric(8).toUpperCase(),
        notes: faker.helpers.maybe(() => faker.lorem.sentence(), { probability: 0.3 }),
        createdAt,
        processedAt,
        products: {
          create: selectedProducts.map(product => ({
            productId: product.productId,
            quantity: product.quantity,
            unitPrice: product.unitPrice,
            totalPrice: product.totalPrice
          }))
        }
      }
    });
  }

  console.log(`Created ${transactionsToCreate} NFC transactions.`);
  console.log('NFC data seeding completed!');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
