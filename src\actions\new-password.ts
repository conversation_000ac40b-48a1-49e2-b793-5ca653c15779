'use server'

import { getPasswordResetTokenByToken } from "@/data/password-reset-token";
import { getUserByEmail } from "@/data/user";
import { db } from "@/lib/prisma";
import { NewPasswordSchema, newPasswordSchema } from "@/schemas";
import { hash } from "bcryptjs";
import { isLoginBlocked, recordFailedLoginAttempt } from "@/lib/rate-limiter-auth";
import { headers } from "next/headers";

export async function newPassword(values: NewPasswordSchema, token: string) {
  // Get IP address from headers
  const headersList = await headers();
  const ipAddress = headersList.get('x-forwarded-for') || 'unknown-ip';
  const userAgent = headersList.get('user-agent') || 'unknown-browser';

  if (!token) {
    await recordFailedLoginAttempt('unknown', ipAddress, userAgent);
    return { error: 'Missing token!' }
  }

  // Check if password reset attempts are blocked for this IP
  const blockStatus = await isLoginBlocked('unknown', ipAddress);
  if (blockStatus.isBlocked) {
    const minutes = Math.ceil((blockStatus.timeLeft || 0) / 60);
    return {
      error: `Too many password reset attempts. Please try again in ${minutes} minute${minutes > 1 ? 's' : ''}.`
    };
  }

  const validatedFields = newPasswordSchema.safeParse(values)

  if (!validatedFields.success) {
    await recordFailedLoginAttempt('unknown', ipAddress, userAgent);
    return { error: 'Invalid fields!' }
  }

  const { password } = validatedFields.data

  const existingToken = await getPasswordResetTokenByToken(token)

  if (!existingToken) {
    await recordFailedLoginAttempt('unknown', ipAddress, userAgent);
    return { error: 'Invalid token', }
  }

  const tokenHasExpired = new Date(existingToken.expires) < new Date()

  if (tokenHasExpired) {
    await recordFailedLoginAttempt(existingToken.email, ipAddress, userAgent);
    return { error: 'Token has expired!' }
  }

  const existingUser = await getUserByEmail(existingToken.email)

  if (!existingUser) {
    await recordFailedLoginAttempt(existingToken.email, ipAddress, userAgent);
    return { error: 'Email does not exists!' }
  }

  const hashedPassoword = await hash(password, 10)

  await db.user.update({
    where: { id: existingUser.id },
    data: { password: hashedPassoword}
  })

  await db.passwordResetToken.delete({
    where: { id: existingToken.id }
  })

  return { success: 'Password updated!' }
}