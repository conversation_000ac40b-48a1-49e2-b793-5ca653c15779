import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma-client';

/**
 * GET /api/organizer/events
 * Get all events for the authenticated organizer
 */
export async function GET(request: NextRequest) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ 
        error: 'Not authenticated'
      }, { status: 401 });
    }
    
    // Check if user is an organizer
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ 
        error: 'Unauthorized. Organizer access required'
      }, { status: 403 });
    }
    
    // Get all events for the organizer
    const events = await prisma.event.findMany({
      where: {
        userId: user.id!
      },
      select: {
        id: true,
        title: true,
        startDate: true,
        endDate: true,
        location: true,
        status: true,
        createdAt: true
      },
      orderBy: {
        startDate: 'asc'
      }
    });
    
    return NextResponse.json(events);
    
  } catch (error) {
    console.error('Error fetching organizer events:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch events'
    }, { status: 500 });
  }
}
