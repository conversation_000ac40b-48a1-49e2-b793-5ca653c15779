'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2, Copy, Trash2 } from 'lucide-react';
import DeveloperGuide from './developer-guide';

import { API_PERMISSIONS } from '@/lib/api-permissions';

interface ApiKey {
  id: string;
  name: string;
  key: string;
  permissions: string[];
  lastUsed: string | null;
  expiresAt: string | null;
  createdAt: string;
  rateLimit: number;
  usageCount: number;
}

export default function ApiKeysPage() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [loading, setLoading] = useState(true);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyRateLimit, setNewKeyRateLimit] = useState(100);
  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>(['read:events']);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newApiKey, setNewApiKey] = useState<ApiKey | null>(null);

  // Fetch API keys
  const fetchApiKeys = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/api-keys');

      if (!response.ok) {
        throw new Error('Failed to fetch API keys');
      }

      const data = await response.json();
      setApiKeys(data);
    } catch (error) {
      console.error('Error fetching API keys:', error);
      toast.error('Failed to fetch API keys');
    } finally {
      setLoading(false);
    }
  };

  // Create a new API key
  const createApiKey = async () => {
    try {
      if (!newKeyName.trim()) {
        toast.error('Please enter a name for the API key');
        return;
      }

      if (newKeyPermissions.length === 0) {
        toast.error('Please select at least one permission');
        return;
      }

      const response = await fetch('/api/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newKeyName,
          permissions: newKeyPermissions,
          rateLimit: newKeyRateLimit,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create API key');
      }

      const data = await response.json();
      setNewApiKey(data);
      fetchApiKeys();
      setNewKeyName('');
      setNewKeyPermissions(['read:events']);
      setNewKeyRateLimit(100);
    } catch (error) {
      console.error('Error creating API key:', error);
      toast.error('Failed to create API key');
    }
  };

  // Delete an API key
  const deleteApiKey = async (id: string) => {
    try {
      const response = await fetch(`/api/api-keys?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete API key');
      }

      toast.success('API key deleted');
      fetchApiKeys();
    } catch (error) {
      console.error('Error deleting API key:', error);
      toast.error('Failed to delete API key');
    }
  };

  // Copy API key to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('API key copied to clipboard');
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  useEffect(() => {
    fetchApiKeys();
  }, []);

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">API Keys</h1>
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button>Create API Key</Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl">
            <DialogHeader>
              <DialogTitle>Create New API Key</DialogTitle>
              <DialogDescription>
                Configure your API key settings. You will only be shown the key once after creation.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  className="col-span-3"
                  placeholder="My API Key"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="rateLimit" className="text-right">
                  Rate Limit
                </Label>
                <div className="col-span-3 flex items-center gap-2">
                  <Input
                    id="rateLimit"
                    type="number"
                    min="10"
                    max="1000"
                    value={newKeyRateLimit}
                    onChange={(e) => setNewKeyRateLimit(parseInt(e.target.value) || 100)}
                    className="w-24"
                  />
                  <span className="text-sm text-gray-500">requests per minute</span>
                </div>
              </div>

              <div className="grid grid-cols-4 items-start gap-4">
                <Label className="text-right pt-2">
                  Permissions
                </Label>
                <div className="col-span-3 grid grid-cols-2 gap-3">
                  {Object.entries(API_PERMISSIONS).map(([key, description]) => (
                    <div key={key} className="flex items-start space-x-2">
                      <input
                        type="checkbox"
                        id={`permission-${key}`}
                        checked={newKeyPermissions.includes(key)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewKeyPermissions([...newKeyPermissions, key]);
                          } else {
                            setNewKeyPermissions(newKeyPermissions.filter(p => p !== key));
                          }
                        }}
                        className="mt-1"
                      />
                      <div>
                        <Label htmlFor={`permission-${key}`} className="font-medium">
                          {key}
                        </Label>
                        <p className="text-xs text-gray-500">{description}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button onClick={() => setIsDialogOpen(false)} variant="outline">
                Cancel
              </Button>
              <Button onClick={() => {
                createApiKey();
                setIsDialogOpen(false);
              }}>
                Create
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {newApiKey && (
        <Card className="mb-6 border-green-500">
          <CardHeader>
            <CardTitle className="text-green-600">API Key Created</CardTitle>
            <CardDescription>
              Copy your API key now. You won&apos;t be able to see it again!
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <code className="bg-gray-100 p-2 rounded flex-1 overflow-x-auto">
                {newApiKey.key}
              </code>
              <Button
                variant="outline"
                size="icon"
                onClick={() => copyToClipboard(newApiKey.key)}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              variant="outline"
              onClick={() => setNewApiKey(null)}
            >
              Done
            </Button>
          </CardFooter>
        </Card>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
        </div>
      ) : apiKeys.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No API Keys</CardTitle>
            <CardDescription>
              You haven&apos;t created any API keys yet.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-gray-500">
              API keys allow external applications to access your data securely.
            </p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => setIsDialogOpen(true)}>
              Create Your First API Key
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid gap-4">
          {apiKeys.map((apiKey) => (
            <Card key={apiKey.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{apiKey.name}</CardTitle>
                    <CardDescription>
                      Created: {formatDate(apiKey.createdAt)}
                    </CardDescription>
                  </div>
                  <Button
                    variant="destructive"
                    size="icon"
                    onClick={() => deleteApiKey(apiKey.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <span className="text-sm font-medium">Permissions:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {apiKey.permissions.map((permission) => (
                        <span
                          key={permission}
                          className="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded"
                        >
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium">Rate Limit:</span>
                      <span className="text-sm ml-2 text-gray-500">
                        {apiKey.rateLimit} requests/minute
                      </span>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Usage Count:</span>
                      <span className="text-sm ml-2 text-gray-500">
                        {apiKey.usageCount} requests
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium">Last used:</span>
                      <span className="text-sm ml-2 text-gray-500">
                        {formatDate(apiKey.lastUsed)}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm font-medium">Expires:</span>
                      <span className="text-sm ml-2 text-gray-500">
                        {apiKey.expiresAt ? formatDate(apiKey.expiresAt) : 'Never'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Import and use the DeveloperGuide component */}
      <DeveloperGuide />
    </div>
  );
}
