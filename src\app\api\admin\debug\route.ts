import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    
    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Count users by role
    const usersByRole = await db.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });

    // Get all users with their roles
    const allUsers = await db.user.findMany({
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Count events by status
    const eventsByStatus = await db.event.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    // Get all events
    const allEvents = await db.event.findMany({
      select: {
        id: true,
        title: true,
        status: true,
        userId: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    // Get database stats
    const stats = {
      users: await db.user.count(),
      events: await db.event.count(),
      orders: await db.order.count(),
      tickets: await db.ticket.count(),
      financialTransactions: await db.financialTransaction.count()
    };

    return NextResponse.json({
      usersByRole,
      allUsers,
      eventsByStatus,
      allEvents,
      stats,
      currentUser: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role
      }
    });
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json(
      { error: 'Failed to fetch debug information' },
      { status: 500 }
    );
  }
}
