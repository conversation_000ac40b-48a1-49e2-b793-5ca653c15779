'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import {
  User,
  Mail,
  Calendar,
  Clock,
  MapPin,
  ChevronLeft,
  Settings,
  Ticket,
  Wallet,
  CreditCard,
  ShoppingBag,
  Users,
  Star,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { useCurrentUser } from '@/hooks/use-current-user';
import { format } from 'date-fns';
import { formatCurrency } from '@/lib/utils';

interface UserProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  image: string | null;
  emailVerified: Date | null;
  createdAt: Date;
  accountBalance: number;
}

export default function UserProfilePage() {
  const router = useRouter();
  const user = useCurrentUser();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchUserProfile = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/user/profile');

        if (!response.ok) {
          throw new Error('Failed to fetch user profile');
        }

        const userData = await response.json();
        setProfile(userData);
      } catch (error) {
        console.error('Error fetching user profile:', error);
        setError('Failed to load profile data. Please try again later.');
        toast({
          title: 'Error',
          description: 'Failed to load profile data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, []);

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <Button variant="ghost" asChild className="-ml-4">
          <Link href="/dashboard/user">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Dashboard
          </Link>
        </Button>

        <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <h1 className="text-3xl font-bold mb-2">My Profile</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View and manage your personal profile
            </p>
          </div>
          <Button className="mt-4 md:mt-0" asChild>
            <Link href="/dashboard/user/settings">
              <Settings className="mr-2 h-4 w-4" />
              Edit Profile
            </Link>
          </Button>
        </div>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-10">
              <div className="rounded-full bg-red-100 p-3 mb-4">
                <User className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Error Loading Profile</h3>
              <p className="text-gray-500 text-center mb-4">{error}</p>
              <Button onClick={() => router.refresh()}>Try Again</Button>
            </CardContent>
          </Card>
        ) : profile ? (
          <>
            {/* Profile Overview Card */}
            <Card>
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row gap-6">
                  <div className="flex flex-col items-center md:items-start">
                    <Avatar className="h-24 w-24 md:h-32 md:w-32">
                      <AvatarImage src={profile.image || undefined} alt={profile.name} />
                      <AvatarFallback className="text-2xl">{getInitials(profile.name)}</AvatarFallback>
                    </Avatar>
                    <div className="mt-4 text-center md:text-left">
                      <Badge className="mb-2">{profile.role}</Badge>
                      <h2 className="text-2xl font-bold">{profile.name}</h2>
                      <div className="flex items-center text-gray-500 mt-1">
                        <Mail className="h-4 w-4 mr-2" />
                        <span>{profile.email}</span>
                      </div>
                      <div className="flex items-center text-gray-500 mt-1">
                        <Calendar className="h-4 w-4 mr-2" />
                        <span>Member since {format(new Date(profile.createdAt), 'MMMM yyyy')}</span>
                      </div>
                    </div>
                  </div>

                  <Separator className="md:hidden" />

                  <div className="flex-1">
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-sm text-gray-500">Wallet Balance</p>
                            <p className="text-xl font-bold mt-1">{formatCurrency(profile.accountBalance)}</p>
                          </div>
                          <div className="bg-blue-100 p-2 rounded-full">
                            <Wallet className="h-5 w-5 text-blue-600" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Real Data Message */}
            <Card className="mt-6">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center">
                  <div className="bg-blue-100 p-3 rounded-full mb-4">
                    <User className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-medium mb-2">Real User Data Only</h3>
                  <p className="text-gray-500 max-w-md mb-4">
                    This profile page only displays real data from your account. Additional sections like activity, tickets, orders, and detailed wallet information will be available as you use the platform.
                  </p>
                  <div className="flex flex-wrap gap-3 justify-center">
                    <Button asChild>
                      <Link href="/dashboard/user/settings">
                        <Settings className="mr-2 h-4 w-4" />
                        Edit Profile
                      </Link>
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/dashboard/user/wallet">
                        <Wallet className="mr-2 h-4 w-4" />
                        Go to Wallet
                      </Link>
                    </Button>
                    <Button asChild variant="outline">
                      <Link href="/dashboard/user">
                        <ChevronLeft className="mr-2 h-4 w-4" />
                        Back to Dashboard
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        ) : (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-10">
              <div className="rounded-full bg-gray-100 p-3 mb-4">
                <User className="h-6 w-6 text-gray-600" />
              </div>
              <h3 className="text-lg font-medium mb-2">Profile Not Found</h3>
              <p className="text-gray-500 text-center mb-4">
                We couldn't find your profile information.
              </p>
              <Button onClick={() => router.refresh()}>Refresh</Button>
            </CardContent>
          </Card>
        )}
      </div>
    </RoleGate>
  );
}
