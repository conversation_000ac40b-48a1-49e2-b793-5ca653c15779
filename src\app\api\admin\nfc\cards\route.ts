import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/nfc/cards
 * Get all NFC cards with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const pageSize = parseInt(url.searchParams.get('pageSize') || '10');
    const status = url.searchParams.get('status') || 'all';
    const eventId = url.searchParams.get('event') || 'all';
    const search = url.searchParams.get('search') || '';
    const sortField = url.searchParams.get('sortField') || 'createdAt';
    const sortDirection = url.searchParams.get('sortDirection') || 'desc';

    // Build filter conditions
    const where: any = {};

    // Status filter
    if (status !== 'all') {
      where.status = status;
    }

    // Event filter
    if (eventId !== 'all') {
      where.eventId = eventId;
    }

    // Search filter
    if (search) {
      where.OR = [
        { id: { contains: search, mode: 'insensitive' } },
        { uid: { contains: search, mode: 'insensitive' } },
        { assignedTo: { contains: search, mode: 'insensitive' } },
        { user: { name: { contains: search, mode: 'insensitive' } } }
        // Note: We can't search by event title directly since there's no relation
        // We'll handle event title search separately if needed
      ];
    }

    // Build sort options
    const orderBy: any = {};

    // Map frontend sort fields to database fields
    const sortFieldMap: Record<string, string> = {
      'createdAt': 'createdAt',
      'balance': 'balance',
      'status': 'status',
      'uid': 'uid',
      'id': 'id',
      'lastUsed': 'lastUsed',
      'event': 'eventId', // Changed from event.title to eventId
      'user': 'user.name'
    };

    // Handle special cases for related fields
    if (sortField === 'event') {
      // We can't sort by event title directly since there's no relation
      // Default to sorting by eventId instead
      orderBy.eventId = sortDirection;
    } else if (sortField === 'user') {
      orderBy.user = { name: sortDirection };
    } else {
      // Use mapped field or default to the provided field
      const dbField = sortFieldMap[sortField] || sortField;
      orderBy[dbField] = sortDirection;
    }

    // Get total count for pagination
    const totalCount = await db.nFCCard.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSize);

    // Get cards with pagination
    const cards = await db.nFCCard.findMany({
      where,
      orderBy,
      skip: (page - 1) * pageSize,
      take: pageSize,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        _count: {
          select: {
            nfcTransactions: true
          }
        }
      }
    });

    // Get event information for the cards
    const eventIds = [...new Set(cards.map(card => card.eventId).filter(id => id))];
    const cardEvents = eventIds.length > 0 ? await db.event.findMany({
      where: {
        id: {
          in: eventIds
        }
      },
      select: {
        id: true,
        title: true
      }
    }) : [];

    // Calculate total balance
    const totalBalanceResult = await db.nFCCard.aggregate({
      where,
      _sum: {
        balance: true
      }
    });

    const totalBalance = totalBalanceResult._sum.balance || 0;

    // Format cards for the frontend
    const formattedCards = cards.map(card => {
      // Find the event for this card
      const event = cardEvents.find(e => e.id === card.eventId);

      return {
        id: card.id,
        uid: card.uid,
        isActive: card.isActive,
        status: card.status,
        balance: card.balance,
        assignedTo: card.assignedTo,
        lastUsed: card.lastUsed ? card.lastUsed.toISOString() : null,
        createdAt: card.createdAt.toISOString(),
        event: event ? {
          id: event.id,
          name: event.title
        } : null,
        user: card.user ? {
          id: card.user.id,
          name: card.user.name,
          email: card.user.email,
          image: card.user.image
        } : null,
        transactionCount: card._count.nfcTransactions
      };
    });

    // Get all events for filters
    // First get all events with NFC system settings
    const nfcSettings = await db.nFCSystemSettings.findMany({
      select: {
        eventId: true
      }
    });

    const events = await db.event.findMany({
      where: {
        id: {
          in: nfcSettings.map(setting => setting.eventId)
        }
      },
      select: {
        id: true,
        title: true
      },
      orderBy: {
        title: 'asc'
      }
    });

    return NextResponse.json({
      cards: formattedCards,
      totalCount,
      totalPages,
      totalBalance,
      currentPage: page,
      events: events.map(event => ({ id: event.id, name: event.title }))
    });
  } catch (error) {
    console.error('Error fetching NFC cards:', error);
    return NextResponse.json(
      { error: 'Failed to fetch NFC cards' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/nfc/cards
 * Create a new NFC card
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const { uid, eventId, userId, assignedTo, initialBalance = 0, isActive = true } = body;

    // Validate required fields
    if (!uid || !eventId) {
      return NextResponse.json(
        { error: 'UID and eventId are required' },
        { status: 400 }
      );
    }

    // Check if card with this UID already exists
    const existingCard = await db.nFCCard.findFirst({
      where: { uid }
    });

    if (existingCard) {
      return NextResponse.json(
        { error: 'A card with this UID already exists' },
        { status: 400 }
      );
    }

    // Check if event exists
    const event = await db.event.findUnique({
      where: { id: eventId }
    });

    if (!event) {
      return NextResponse.json(
        { error: 'Event not found' },
        { status: 404 }
      );
    }

    // Check if user exists (if provided)
    if (userId) {
      const userExists = await db.user.findUnique({
        where: { id: userId }
      });

      if (!userExists) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }
    }

    // Create the card
    const card = await db.nFCCard.create({
      data: {
        uid,
        eventId,
        userId: userId || null,
        assignedTo,
        balance: initialBalance,
        isActive,
        status: 'active'
      }
    });

    return NextResponse.json({
      message: 'Card created successfully',
      card: {
        id: card.id,
        uid: card.uid,
        eventId: card.eventId,
        userId: card.userId,
        assignedTo: card.assignedTo,
        balance: card.balance,
        isActive: card.isActive,
        status: card.status,
        createdAt: card.createdAt.toISOString()
      }
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating NFC card:', error);
    return NextResponse.json(
      { error: 'Failed to create NFC card' },
      { status: 500 }
    );
  }
}
