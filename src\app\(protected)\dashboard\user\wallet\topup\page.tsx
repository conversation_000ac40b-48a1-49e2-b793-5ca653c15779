'use client';

import React, { useState, useEffect } from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ChevronLeft, Wallet, AlertCircle, CreditCard, Phone, Building, Info, PlusCircle, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';
import { formatCurrency } from '@/lib/utils';
import { RoleGate } from '@/components/auth/role-gate';

interface WalletData {
  accountBalance: number;
  availableBalance: number;
  pendingWithdrawals: number;
}

export default function TopUpWalletPage() {
  const router = useRouter();
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [mobileNumber, setMobileNumber] = useState('');
  const [countryCode, setCountryCode] = useState('+260');
  const [cardNumber, setCardNumber] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [cardholderName, setCardholderName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [mobileError, setMobileError] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [walletData, setWalletData] = useState<WalletData>({
    accountBalance: 0,
    availableBalance: 0,
    pendingWithdrawals: 0
  });

  // Fetch wallet data
  useEffect(() => {
    const fetchWalletData = async () => {
      try {
        setIsLoading(true);
        const response = await fetch('/api/wallet/balance');

        if (!response.ok) {
          throw new Error('Failed to fetch wallet data');
        }

        const data = await response.json();
        setWalletData({
          accountBalance: data.accountBalance || 0,
          availableBalance: data.availableBalance || 0,
          pendingWithdrawals: data.pendingWithdrawals || 0
        });
      } catch (error) {
        console.error('Error fetching wallet data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load wallet data',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchWalletData();
  }, []);

  // Handle amount change
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and decimal point
    if (/^\d*\.?\d*$/.test(value)) {
      setAmount(value);
      setError('');
    }
  };

  // Handle mobile number change
  const handleMobileNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and spaces
    if (/^[0-9\s]*$/.test(value)) {
      setMobileNumber(value);
      setMobileError('');

      // Validate based on country code
      if (value.length > 0) {
        if (countryCode === '+260') { // Zambia
          // Check if it starts with valid Zambian mobile prefixes
          const cleanNumber = value.replace(/\s/g, '');
          const validPrefixes = ['76', '77', '95', '96', '97'];
          const prefix = cleanNumber.substring(0, 2);

          if (cleanNumber.length > 0 && !validPrefixes.includes(prefix) && cleanNumber.length >= 2) {
            setMobileError('Invalid mobile prefix for Zambia');
          } else if (cleanNumber.length > 0 && cleanNumber.length !== 9) {
            setMobileError(cleanNumber.length < 9 ? '' : 'Zambian numbers should be 9 digits');
          }
        }
        // Add validation for other country codes as needed
      }
    }
  };

  // Handle country code change
  const handleCountryCodeChange = (value: string) => {
    setCountryCode(value);
    setMobileError('');

    // Clear mobile number when changing country to avoid validation issues
    setMobileNumber('');
  };

  // Handle card number change
  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and spaces
    if (/^[0-9\s]*$/.test(value)) {
      setCardNumber(value);
      setError('');
    }
  };

  // Handle expiry date change
  const handleExpiryDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers and slash
    if (/^[0-9/]*$/.test(value)) {
      setExpiryDate(value);
      setError('');
    }
  };

  // Handle CVV change
  const handleCvvChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow numbers
    if (/^[0-9]*$/.test(value)) {
      setCvv(value);
      setError('');
    }
  };

  // Handle cardholder name change
  const handleCardholderNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCardholderName(e.target.value);
    setError('');
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate amount
    const numAmount = parseFloat(amount);
    if (!amount || isNaN(numAmount) || numAmount <= 0) {
      setError('Please enter a valid amount');
      return;
    }

    if (numAmount < 10) {
      setError('Minimum top-up amount is $10');
      return;
    }

    // Validate card details for Mastercard
    if (paymentMethod === 'card') {
      if (!cardNumber) {
        setError('Please enter your card number');
        return;
      }

      const cleanCardNumber = cardNumber.replace(/\s/g, '');
      if (cleanCardNumber.length !== 16) {
        setError('Please enter a valid 16-digit card number');
        return;
      }

      if (!expiryDate) {
        setError('Please enter the expiry date');
        return;
      }

      if (!expiryDate.includes('/') || expiryDate.length !== 5) {
        setError('Please enter a valid expiry date (MM/YY)');
        return;
      }

      if (!cvv) {
        setError('Please enter the CVV');
        return;
      }

      if (cvv.length !== 3) {
        setError('Please enter a valid 3-digit CVV');
        return;
      }

      if (!cardholderName) {
        setError('Please enter the cardholder name');
        return;
      }
    }

    // Validate mobile number for mobile money
    if (paymentMethod === 'mobile') {
      if (!mobileNumber) {
        setError('Please enter your mobile number');
        return;
      }

      if (mobileError) {
        setError(mobileError);
        return;
      }

      // Additional validation based on country code
      const cleanNumber = mobileNumber.replace(/\s/g, '');
      if (countryCode === '+260' && cleanNumber.length !== 9) {
        setError('Please enter a valid 9-digit Zambian mobile number');
        return;
      }
      // Add validation for other countries as needed
    }

    // Prepare payment details based on the selected method
    let paymentDetails = {};

    switch (paymentMethod) {
      case 'card':
        paymentDetails = {
          cardType: 'Mastercard',
          cardNumber: cardNumber.replace(/\s/g, ''),
          expiryDate: expiryDate,
          cvv: cvv,
          cardholderName: cardholderName
        };
        break;
      case 'mobile':
        paymentDetails = {
          mobileNumber: `${countryCode}${mobileNumber.replace(/\s/g, '')}`,
          provider: 'MTN', // This would come from the selected provider
        };
        break;
    }

    setIsSubmitting(true);

    try {
      // Call the real API endpoint
      const response = await fetch('/api/wallet/topup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: numAmount,
          paymentMethod,
          paymentDetails,
          useStripe: false, // For testing, we're not using Stripe
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to process top-up');
      }

      // Show success message
      toast({
        title: 'Success',
        description: 'Your wallet has been topped up successfully',
      });

      // Redirect to wallet page with success message
      router.push('/dashboard/user/wallet?success=true');
    } catch (error) {
      console.error('Error processing top-up:', error);
      setError(error instanceof Error ? error.message : 'Failed to process top-up');
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to process top-up',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <RoleGate allowedRole="USER">
      <div className="max-w-2xl mx-auto">
        <Button variant="ghost" asChild className="mb-4 -ml-4">
          <Link href="/dashboard/user/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>

        <h1 className="text-3xl font-bold mb-2">Top Up Wallet</h1>
        <p className="text-gray-500 dark:text-gray-400 mb-6">
          Add funds to your wallet using your preferred payment method
        </p>

        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Current Balance</CardTitle>
                <CardDescription>Your available wallet balance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-3xl font-bold">{formatCurrency(walletData.accountBalance)}</p>
                    {walletData.pendingWithdrawals > 0 && (
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Available: {formatCurrency(walletData.availableBalance)}
                      </p>
                    )}
                    <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                      Minimum top-up: {formatCurrency(10)}
                    </p>
                  </div>
                  <div className="bg-green-100 dark:bg-green-900 p-4 rounded-full">
                    <Wallet className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                </div>
              </CardContent>
            </Card>

          <Card>
            <form onSubmit={handleSubmit}>
              <CardHeader>
                <CardTitle>Top Up Details</CardTitle>
                <CardDescription>Enter the amount and payment method</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="amount">Amount</Label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                    <Input
                      id="amount"
                      placeholder="0.00"
                      className="pl-8"
                      value={amount}
                      onChange={handleAmountChange}
                    />
                  </div>
                  {error && (
                    <div className="flex items-center text-red-600 text-sm mt-1">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {error}
                    </div>
                  )}
                </div>

                <Tabs defaultValue="card" onValueChange={setPaymentMethod} className="w-full">
                  <TabsList className="grid grid-cols-2 w-full">
                    <TabsTrigger value="card" className="flex items-center gap-2">
                      <CreditCard className="h-4 w-4" />
                      Mastercard
                    </TabsTrigger>
                    <TabsTrigger value="mobile" className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      Mobile Money
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="card" className="space-y-4 mt-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between mb-4">
                        <Label>Credit Card Details</Label>
                        <div className="flex items-center space-x-2">
                          <div className="w-10 h-6 bg-red-500 rounded flex items-center justify-center">
                            <span className="text-white font-bold text-xs">MC</span>
                          </div>
                          <span className="text-sm text-gray-500">Mastercard</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cardNumber">Card Number</Label>
                        <Input
                          id="cardNumber"
                          placeholder="5555 5555 5555 5555"
                          className="font-mono"
                          value={cardNumber}
                          onChange={handleCardNumberChange}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="expiryDate">Expiry Date</Label>
                          <Input
                            id="expiryDate"
                            placeholder="MM/YY"
                            className="font-mono"
                            value={expiryDate}
                            onChange={handleExpiryDateChange}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="cvv">CVV</Label>
                          <Input
                            id="cvv"
                            placeholder="123"
                            className="font-mono"
                            type="password"
                            maxLength={3}
                            value={cvv}
                            onChange={handleCvvChange}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="cardholderName">Cardholder Name</Label>
                        <Input
                          id="cardholderName"
                          placeholder="John Doe"
                          value={cardholderName}
                          onChange={handleCardholderNameChange}
                        />
                      </div>

                      <div className="flex items-center space-x-2 mt-2">
                        <div className="p-1 bg-green-100 rounded-full">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-xs text-gray-500">Your payment information is secure and encrypted</span>
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="mobile" className="space-y-4 mt-4">
                    <div className="space-y-2">
                      <Label>Select Mobile Money Provider</Label>
                      <RadioGroup defaultValue="mtn" className="flex flex-col space-y-2">
                        <div className="flex items-center space-x-2 border p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <RadioGroupItem value="mtn" id="mtn" />
                          <Label htmlFor="mtn" className="flex-1 cursor-pointer">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-yellow-500 flex items-center justify-center mr-3 overflow-hidden">
                                  <span className="text-white font-bold text-xs">MTN</span>
                                </div>
                                <span>MTN Mobile Money</span>
                              </div>
                              <div className="bg-yellow-100 dark:bg-yellow-900 p-1.5 rounded-full">
                                <Phone className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <RadioGroupItem value="airtel" id="airtel" />
                          <Label htmlFor="airtel" className="flex-1 cursor-pointer">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-red-600 flex items-center justify-center mr-3 overflow-hidden">
                                  <span className="text-white font-bold text-xs">AIRTEL</span>
                                </div>
                                <span>Airtel Money</span>
                              </div>
                              <div className="bg-red-100 dark:bg-red-900 p-1.5 rounded-full">
                                <Phone className="h-4 w-4 text-red-600 dark:text-red-400" />
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <RadioGroupItem value="zamtel" id="zamtel" />
                          <Label htmlFor="zamtel" className="flex-1 cursor-pointer">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-green-600 flex items-center justify-center mr-3 overflow-hidden">
                                  <span className="text-white font-bold text-xs">ZT</span>
                                </div>
                                <span>Zamtel Money</span>
                              </div>
                              <div className="bg-green-100 dark:bg-green-900 p-1.5 rounded-full">
                                <Phone className="h-4 w-4 text-green-600 dark:text-green-400" />
                              </div>
                            </div>
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2 border p-3 rounded-md hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <RadioGroupItem value="other" id="other" />
                          <Label htmlFor="other" className="flex-1 cursor-pointer">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mr-3 overflow-hidden">
                                  <Phone className="h-4 w-4 text-gray-600 dark:text-gray-400" />
                                </div>
                                <span>Other Provider</span>
                              </div>
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="mobileNumber">Mobile Number</Label>
                      <div className="grid grid-cols-12 gap-2">
                        <div className="col-span-4 sm:col-span-3">
                          <Select value={countryCode} onValueChange={handleCountryCodeChange}>
                            <SelectTrigger>
                              <SelectValue placeholder="Code" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="+260">+260 (Zambia)</SelectItem>
                              <SelectItem value="+27">+27 (South Africa)</SelectItem>
                              <SelectItem value="+254">+254 (Kenya)</SelectItem>
                              <SelectItem value="+255">+255 (Tanzania)</SelectItem>
                              <SelectItem value="+256">+256 (Uganda)</SelectItem>
                              <SelectItem value="+263">+263 (Zimbabwe)</SelectItem>
                              <SelectItem value="+234">+234 (Nigeria)</SelectItem>
                              <SelectItem value="+233">+233 (Ghana)</SelectItem>
                              <SelectItem value="+251">+251 (Ethiopia)</SelectItem>
                              <SelectItem value="+20">+20 (Egypt)</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="col-span-8 sm:col-span-9">
                          <div className="relative">
                            <Input
                              id="mobileNumber"
                              placeholder="7X XXX XXXX"
                              value={mobileNumber}
                              onChange={handleMobileNumberChange}
                              className={mobileError ? 'border-red-500 focus-visible:ring-red-500' : ''}
                            />
                            {mobileError && (
                              <div className="absolute -bottom-5 left-0 text-xs text-red-500 flex items-center">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                {mobileError}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center text-xs text-gray-500">
                        <Info className="h-3 w-3 mr-1" />
                        <span>Enter the mobile number associated with your mobile money account</span>
                      </div>

                      {/* Number format examples */}
                      <div className="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                        <p className="text-xs font-medium mb-1">Example formats by provider:</p>
                        {countryCode === '+260' && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">MTN</span>
                              </div>
                              <span className="text-xs">76X XXX XXX or 77X XXX XXX</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-red-600 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">AIR</span>
                              </div>
                              <span className="text-xs">97X XXX XXX or 96X XXX XXX</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-green-600 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">ZT</span>
                              </div>
                              <span className="text-xs">95X XXX XXX</span>
                            </div>
                          </div>
                        )}
                        {countryCode === '+27' && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-yellow-500 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">MTN</span>
                              </div>
                              <span className="text-xs">83X XXX XXXX or 73X XXX XXXX</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-green-600 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">VOD</span>
                              </div>
                              <span className="text-xs">71X XXX XXXX or 82X XXX XXXX</span>
                            </div>
                          </div>
                        )}
                        {countryCode === '+254' && (
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-green-600 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">SAF</span>
                              </div>
                              <span className="text-xs">7XX XXX XXX</span>
                            </div>
                            <div className="flex items-center">
                              <div className="w-5 h-5 rounded-full bg-red-600 flex items-center justify-center mr-2 overflow-hidden">
                                <span className="text-white font-bold text-[8px]">AIR</span>
                              </div>
                              <span className="text-xs">1XX XXX XXX</span>
                            </div>
                          </div>
                        )}
                        {!['+260', '+27', '+254'].includes(countryCode) && (
                          <div className="text-xs text-gray-500">
                            Enter your mobile number without the country code.
                          </div>
                        )}
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>

                <div className="flex items-start p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
                  <Info className="h-5 w-5 text-blue-600 dark:text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                  <div className="text-sm text-blue-800 dark:text-blue-300">
                    <p className="font-medium">Processing Time</p>
                    <p className="mt-1">Bank transfers may take 1-3 business days to process. Mobile money transfers are usually instant.</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t px-6 py-4">
                <Button variant="outline" asChild>
                  <Link href="/dashboard/user/wallet">Cancel</Link>
                </Button>
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? 'Processing...' : 'Top Up Wallet'}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </div>
        )}
      </div>
    </RoleGate>
  );
}
