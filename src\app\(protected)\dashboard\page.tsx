'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useCurrentUser } from '@/hooks/use-current-user';
import { Skeleton } from '@/components/ui/skeleton';

export default function DashboardRedirectPage() {
  const router = useRouter();
  const user = useCurrentUser();

  useEffect(() => {
    if (user) {
      // Redirect based on user role
      switch (user.role) {
        case 'ADMIN':
        case 'SUPERADMIN':
          router.push('/admin/dashboard');
          break;
        case 'USER':
          router.push('/dashboard/user');
          break;
        case 'VENDOR':
          router.push('/dashboard/vendor');
          break;
        case 'ORGANIZER':
          router.push('/dashboard/organizer');
          break;
        case 'PARTNER':
          router.push('/dashboard/partner');
          break;
        case 'DEVELOPER':
          router.push('/dashboard/developer');
          break;
        default:
          // If user has no role or unrecognized role, redirect to role selection
          router.push('/auth/select-role');
          break;
      }
    } else {
      // If no user session, redirect to login
      router.push('/auth/login');
    }
  }, [user, router]);

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Skeleton className="h-10 w-64" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
      </div>
      <div className="text-center py-12">
        <p className="text-gray-500">Redirecting to your dashboard...</p>
      </div>
    </div>
  );
}