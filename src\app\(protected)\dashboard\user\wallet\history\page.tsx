'use client';

import React, { useState } from 'react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ChevronLeft, Download, Search, Filter, PlusCircle, Ticket, ArrowUpRight, ArrowDownRight } from 'lucide-react';
import Link from 'next/link';

export default function WalletHistoryPage() {
  const [typeFilter, setTypeFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Mock data for transactions
  const transactions = [
    {
      id: 'tx1',
      type: 'TOP_UP',
      amount: 100.00,
      date: '2023-06-15T10:30:00Z',
      description: 'Wallet top-up via Bank Transfer',
      method: 'Bank Transfer',
      status: 'COMPLETED'
    },
    {
      id: 'tx2',
      type: 'PURCHASE',
      amount: 75.00,
      date: '2023-06-10T14:45:00Z',
      description: 'Ticket purchase for Tech Conference 2023',
      event: 'Tech Conference 2023',
      status: 'COMPLETED'
    },
    {
      id: 'tx3',
      type: 'TOP_UP',
      amount: 200.00,
      date: '2023-06-05T09:15:00Z',
      description: 'Wallet top-up via Mobile Money',
      method: 'MTN Mobile Money',
      status: 'COMPLETED'
    },
    {
      id: 'tx4',
      type: 'PURCHASE',
      amount: 150.00,
      date: '2023-05-28T16:20:00Z',
      description: 'Ticket purchase for Summer Music Festival',
      event: 'Summer Music Festival',
      status: 'COMPLETED'
    },
    {
      id: 'tx5',
      type: 'REFUND',
      amount: 50.00,
      date: '2023-05-15T11:05:00Z',
      description: 'Refund for cancelled event',
      event: 'Art Exhibition',
      status: 'COMPLETED'
    },
    {
      id: 'tx6',
      type: 'TOP_UP',
      amount: 75.00,
      date: '2023-05-10T13:40:00Z',
      description: 'Wallet top-up via Bank Transfer',
      method: 'Bank Transfer',
      status: 'COMPLETED'
    },
    {
      id: 'tx7',
      type: 'TOP_UP',
      amount: 100.00,
      date: '2023-05-01T15:55:00Z',
      description: 'Wallet top-up via Mobile Money',
      method: 'Airtel Money',
      status: 'FAILED'
    }
  ];

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // Get transaction icon based on type
  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'TOP_UP':
        return <PlusCircle className="h-5 w-5 text-green-500" />;
      case 'PURCHASE':
        return <Ticket className="h-5 w-5 text-red-500" />;
      case 'REFUND':
        return <ArrowUpRight className="h-5 w-5 text-blue-500" />;
      default:
        return <ArrowDownRight className="h-5 w-5 text-gray-500" />;
    }
  };

  // Get transaction type label
  const getTransactionTypeLabel = (type: string) => {
    switch (type) {
      case 'TOP_UP':
        return 'Top Up';
      case 'PURCHASE':
        return 'Purchase';
      case 'REFUND':
        return 'Refund';
      default:
        return type;
    }
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Filter transactions based on type and search term
  const filteredTransactions = transactions.filter(transaction => {
    // Filter by type
    if (typeFilter !== 'all' && transaction.type !== typeFilter) {
      return false;
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        transaction.description.toLowerCase().includes(term) ||
        transaction.id.toLowerCase().includes(term) ||
        (transaction.event && transaction.event.toLowerCase().includes(term)) ||
        (transaction.method && transaction.method.toLowerCase().includes(term))
      );
    }

    return true;
  });

  // Calculate totals
  const totalTopUps = transactions
    .filter(tx => tx.type === 'TOP_UP' && tx.status === 'COMPLETED')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const totalSpent = transactions
    .filter(tx => tx.type === 'PURCHASE' && tx.status === 'COMPLETED')
    .reduce((sum, tx) => sum + tx.amount, 0);

  const totalRefunds = transactions
    .filter(tx => tx.type === 'REFUND' && tx.status === 'COMPLETED')
    .reduce((sum, tx) => sum + tx.amount, 0);

  return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <Button variant="ghost" asChild className="mb-4 -ml-4">
              <Link href="/dashboard/user/wallet">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Wallet
              </Link>
            </Button>
            <h1 className="text-3xl font-bold mb-2">Transaction History</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View all your wallet transactions
            </p>
          </div>
          <Button variant="outline" className="mt-4 md:mt-0">
            <Download className="mr-2 h-4 w-4" />
            Export History
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Top Ups</p>
                  <h3 className="text-2xl font-bold mt-1">{formatCurrency(totalTopUps)}</h3>
                </div>
                <div className="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                  <PlusCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Spent</p>
                  <h3 className="text-2xl font-bold mt-1">{formatCurrency(totalSpent)}</h3>
                </div>
                <div className="bg-red-100 dark:bg-red-900 p-3 rounded-full">
                  <Ticket className="h-5 w-5 text-red-600 dark:text-red-400" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="pt-6">
              <div className="flex justify-between items-start">
                <div>
                  <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Refunds</p>
                  <h3 className="text-2xl font-bold mt-1">{formatCurrency(totalRefunds)}</h3>
                </div>
                <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                  <ArrowUpRight className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>All Transactions</CardTitle>
            <CardDescription>Complete history of your wallet transactions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="TOP_UP">Top Ups</SelectItem>
                  <SelectItem value="PURCHASE">Purchases</SelectItem>
                  <SelectItem value="REFUND">Refunds</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left py-3 px-4">Type</th>
                    <th className="text-left py-3 px-4">Description</th>
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-right py-3 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTransactions.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="py-6 text-center text-gray-500 dark:text-gray-400">
                        No transactions found
                      </td>
                    </tr>
                  ) : (
                    filteredTransactions.map((transaction, index) => (
                      <tr key={index} className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="mr-3">
                              {getTransactionIcon(transaction.type)}
                            </div>
                            <span>{getTransactionTypeLabel(transaction.type)}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          {transaction.description}
                        </td>
                        <td className="py-3 px-4">
                          {formatDate(transaction.date)}
                        </td>
                        <td className="py-3 px-4">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-right font-medium">
                          <span className={transaction.type === 'PURCHASE' ? 'text-red-600' : transaction.type === 'TOP_UP' ? 'text-green-600' : 'text-blue-600'}>
                            {transaction.type === 'PURCHASE' ? '-' : '+'}
                            {formatCurrency(transaction.amount)}
                          </span>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
  );
}
