import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma-client';

/**
 * GET /api/organizer/events/:id/nfc-devices
 * Get all NFC devices for a specific event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ 
        error: 'Not authenticated'
      }, { status: 401 });
    }
    
    // Check if user is an organizer
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ 
        error: 'Unauthorized. Organizer access required'
      }, { status: 403 });
    }
    
    const { id: eventId } = await params;
    
    // Check if the event belongs to the organizer
    const event = await prisma.event.findUnique({
      where: {
        id: eventId,
        userId: user.id!
      }
    });
    
    if (!event) {
      return NextResponse.json({ 
        error: 'Event not found or does not belong to you'
      }, { status: 404 });
    }
    
    // Get all NFC devices associated with the event
    // For now, we'll return a mock response since we don't have the actual relationship yet
    // In a real implementation, you would query the database for devices linked to this event
    
    // Mock data for demonstration
    const mockDevices = [
      {
        id: 'device_1',
        type: 'CARD',
        status: 'ACTIVE',
        balance: 50.00,
        lastUsed: new Date().toISOString(),
        metadata: { color: 'blue' },
        userId: 'user_1'
      },
      {
        id: 'device_2',
        type: 'FABRIC_WRISTBAND',
        status: 'ACTIVE',
        balance: 25.50,
        lastUsed: new Date().toISOString(),
        metadata: { color: 'red' },
        userId: 'user_2'
      },
      {
        id: 'device_3',
        type: 'PAPER_WRISTBAND',
        status: 'DEACTIVATED',
        balance: 0.00,
        lastUsed: null,
        metadata: { color: 'green' },
        userId: 'user_3'
      },
      {
        id: 'device_4',
        type: 'SILICONE_WRISTBAND',
        status: 'LOST',
        balance: 10.25,
        lastUsed: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        metadata: { color: 'black' },
        userId: 'user_4'
      },
      {
        id: 'device_5',
        type: 'TAG',
        status: 'ACTIVE',
        balance: 75.00,
        lastUsed: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        metadata: null,
        userId: 'user_5'
      }
    ];
    
    return NextResponse.json(mockDevices);
    
  } catch (error) {
    console.error('Error fetching NFC devices for event:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch NFC devices'
    }, { status: 500 });
  }
}

/**
 * POST /api/organizer/events/:id/nfc-devices
 * Import NFC devices for a specific event
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ 
        error: 'Not authenticated'
      }, { status: 401 });
    }
    
    // Check if user is an organizer
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ 
        error: 'Unauthorized. Organizer access required'
      }, { status: 403 });
    }
    
    const { id: eventId } = await params;
    
    // Check if the event belongs to the organizer
    const event = await prisma.event.findUnique({
      where: {
        id: eventId,
        userId: user.id!
      }
    });
    
    if (!event) {
      return NextResponse.json({ 
        error: 'Event not found or does not belong to you'
      }, { status: 404 });
    }
    
    // Parse the request body
    const body = await request.json();
    const { devices, importType = 'add' } = body;
    
    if (!devices || !Array.isArray(devices) || devices.length === 0) {
      return NextResponse.json({ 
        error: 'No devices provided for import'
      }, { status: 400 });
    }
    
    // In a real implementation, you would:
    // 1. Validate each device
    // 2. Handle the import based on the importType (add, update, replace)
    // 3. Associate the devices with the event
    
    return NextResponse.json({
      success: true,
      message: `Successfully imported ${devices.length} NFC devices`,
      importType
    });
    
  } catch (error) {
    console.error('Error importing NFC devices for event:', error);
    return NextResponse.json({ 
      error: 'Failed to import NFC devices'
    }, { status: 500 });
  }
}
