[{"timestamp": "2025-04-16T11:35:34.994Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:35.020Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:35.036Z", "endpoint": "/tickets", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:35.051Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:35.066Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:35.158Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:35.235Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:35.250Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:35.357Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:35.433Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:35.494Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:35.601Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:35.663Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:35.771Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:35.878Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:35.955Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:36.047Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:36.092Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:36.168Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:36.213Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:36.275Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:36.351Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:36.443Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:36.458Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:36.473Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:36.504Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:36.519Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:36.595Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:36.610Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:36.671Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:36.732Z", "endpoint": "/tickets", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:36.823Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:36.884Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:36.899Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:36.930Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:36.960Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:37.006Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:37.083Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:37.144Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:37.175Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:37.221Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:37.282Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:37.329Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:37.359Z", "endpoint": "/tickets", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:37.421Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:37.436Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:37.466Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:37.512Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:37.619Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:37.680Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:37.756Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:37.787Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:37.849Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:37.895Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:37.986Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:38.047Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:38.078Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:38.139Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:38.170Z", "endpoint": "/tickets", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:38.261Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:38.276Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:38.308Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:38.414Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:38.491Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:38.582Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:38.612Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:38.658Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:38.766Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:38.812Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:38.858Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:38.919Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.027Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.058Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.150Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:39.195Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:39.211Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:39.226Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:39.256Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.271Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.302Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:39.378Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:39.455Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:39.516Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:39.546Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:39.638Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:39.714Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:39.791Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:39.897Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:39.943Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:40.035Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:40.140Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:40.217Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:40.279Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:40.355Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:40.401Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:40.416Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:40.492Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:40.584Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:40.630Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:40.645Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:40.737Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:40.784Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:40.830Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:40.922Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:40.953Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:40.998Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:41.075Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:41.151Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:41.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:41.229Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:41.305Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:41.366Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:41.412Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:41.474Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:41.550Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:41.581Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:41.643Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:41.719Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:41.766Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:41.873Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:41.933Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:41.964Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:42.041Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:42.087Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:42.149Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:42.225Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:42.272Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:42.287Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:42.349Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:42.380Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:42.441Z", "endpoint": "/events/published", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:42.457Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:42.534Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:42.595Z", "endpoint": "/tickets", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:42.656Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:42.732Z", "endpoint": "/orders", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:42.840Z", "endpoint": "/eventdetails/1", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:42.930Z", "endpoint": "/external/events", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.038Z", "endpoint": "/events", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.129Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.161Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.161Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.161Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.161Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.161Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.162Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-5", "apiKeyName": "Analytics Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.163Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-2", "apiKeyName": "Write Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/nonexistent", "method": "GET", "apiKeyId": "api-key-3", "apiKeyName": "Full Access Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.164Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.165Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.165Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.165Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.165Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.166Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.167Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.167Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.167Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.167Z", "endpoint": "/events/create", "method": "POST", "apiKeyId": "api-key-1", "apiKeyName": "Read Only Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.168Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.168Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.168Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.168Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.168Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.169Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.170Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.170Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.170Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.171Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.172Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.172Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.172Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.172Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.173Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.174Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.175Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.176Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.176Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.176Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.176Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.177Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.177Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.177Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.177Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.178Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.178Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.178Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.178Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.179Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.180Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.180Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.180Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.181Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.182Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.182Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.182Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.183Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.184Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.184Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.184Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.185Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.185Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.185Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.185Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.185Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.186Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.187Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.187Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.187Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.188Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.189Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.190Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.190Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.190Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.191Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.191Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.191Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.191Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.192Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.193Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.193Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.193Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.194Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.195Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.196Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.197Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.198Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.199Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.199Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.199Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.200Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.201Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.203Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.204Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.205Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.205Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.206Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.206Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.206Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.206Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.207Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.208Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.209Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.210Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.210Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.210Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.210Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.210Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "127.0.0.1"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "********"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.211Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "PostmanRuntime/7.28.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.212Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "node-fetch/2.6.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.213Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.213Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "2001:4860:4860::8888"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "axios/0.21.1", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "************"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15", "ipAddress": "2001:db8::1"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36", "ipAddress": "***********"}, {"timestamp": "2025-04-16T11:35:43.214Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "python-requests/2.25.1", "ipAddress": "**********"}, {"timestamp": "2025-04-16T11:35:43.215Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "*********"}, {"timestamp": "2025-04-16T11:35:43.215Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.215Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0", "ipAddress": "::1"}, {"timestamp": "2025-04-16T11:35:43.215Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "*******"}, {"timestamp": "2025-04-16T11:35:43.215Z", "endpoint": "/test", "method": "GET", "apiKeyId": "api-key-4", "apiKeyName": "Limited Rate Key", "status": 500, "error": "fetch is not a function", "userAgent": "curl/7.64.1", "ipAddress": "***********"}]