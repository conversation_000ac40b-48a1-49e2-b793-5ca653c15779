import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { startOfMonth, endOfMonth, subMonths, format } from 'date-fns';

/**
 * GET /api/admin/finance/dashboard
 * Get financial dashboard data for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'all'; // all, year, month, week

    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date(0); // Default to beginning of time

    if (period === 'year') {
      startDate = new Date(now.getFullYear(), 0, 1); // Start of current year
    } else if (period === 'month') {
      startDate = startOfMonth(now);
    } else if (period === 'week') {
      const day = now.getDay();
      startDate = new Date(now);
      startDate.setDate(now.getDate() - day);
    }

    // Get total revenue from orders
    const orderRevenue = await db.order.aggregate({
      where: {
        status: 'Completed',
        createdAt: { gte: startDate }
      },
      _sum: { totalPrice: true }
    });

    // Get total revenue from NFC transactions
    const nfcRevenue = await db.vendorNFCTransaction.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startDate }
      },
      _sum: { amount: true }
    });

    // Calculate total revenue
    const totalRevenue = (orderRevenue._sum.totalPrice || 0) + (nfcRevenue._sum.amount || 0);

    // Get total transactions count
    const totalOrders = await db.order.count({
      where: {
        status: 'Completed',
        createdAt: { gte: startDate }
      }
    });

    const totalNfcTransactions = await db.vendorNFCTransaction.count({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: startDate }
      }
    });

    const totalTransactions = totalOrders + totalNfcTransactions;

    // Get active organizers count
    const activeOrganizers = await db.user.count({
      where: {
        role: 'ORGANIZER',
        events: {
          some: {
            status: 'Published',
            endDate: { gt: now }
          }
        }
      }
    });

    // Get active vendors count
    const activeVendors = await db.vendorProfile.count({
      where: {
        verificationStatus: 'APPROVED',
        nfcTransactions: {
          some: {
            createdAt: { gte: startDate }
          }
        }
      }
    });

    // Get revenue by month for the last 6 months
    const revenueByMonth = await Promise.all(
      Array.from({ length: 6 }, (_, i) => {
        const monthStart = startOfMonth(subMonths(now, i));
        const monthEnd = endOfMonth(subMonths(now, i));

        return Promise.all([
          // Order revenue for the month
          db.order.aggregate({
            where: {
              status: 'Completed',
              createdAt: {
                gte: monthStart,
                lte: monthEnd
              }
            },
            _sum: { totalPrice: true }
          }),

          // NFC transaction revenue for the month
          db.vendorNFCTransaction.aggregate({
            where: {
              status: 'COMPLETED',
              createdAt: {
                gte: monthStart,
                lte: monthEnd
              }
            },
            _sum: { amount: true }
          })
        ]).then(([orderRev, nfcRev]) => ({
          name: format(monthStart, 'MMM'),
          platformFees: (orderRev._sum.totalPrice || 0) * 0.06, // 6% platform fee
          processingFees: (orderRev._sum.totalPrice || 0) * 0.035 + (nfcRev._sum.amount || 0) * 0.035, // 3.5% processing fee
          posRentalFees: activeVendors * 1000 // K1000 per vendor
        }));
      })
    );

    // Calculate fee distribution based on actual transactions

    // Get platform commission from ticket sales
    const ticketSalesTotal = await db.order.aggregate({
      where: {
        status: 'Completed',
        createdAt: { gte: startDate }
      },
      _sum: { totalPrice: true }
    });

    const platformCommissionRate = 0.06; // 6% platform fee
    const platformCommission = (ticketSalesTotal._sum.totalPrice || 0) * platformCommissionRate;

    // Get processing fees from all transactions
    const allTransactionsTotal = totalRevenue;
    const processingFeeRate = 0.035; // 3.5% processing fee
    const processingFees = allTransactionsTotal * processingFeeRate;

    // Get POS rental fees from active vendors
    const posRentalFeePerVendor = 1000; // K1000 per vendor
    const posRentalFees = activeVendors * posRentalFeePerVendor;

    const feeDistribution = [
      { name: 'Platform Commission', value: platformCommission },
      { name: 'Processing Fees', value: processingFees },
      { name: 'POS Rental Fees', value: posRentalFees }
    ];

    // Get recent transactions
    const recentOrders = await db.order.findMany({
      where: {
        status: 'Completed'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5,
      include: {
        event: {
          select: {
            title: true,
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    });

    const recentNfcTransactions = await db.vendorNFCTransaction.findMany({
      where: {
        status: 'COMPLETED'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5,
      include: {
        vendor: {
          select: {
            businessName: true
          }
        },
        event: {
          select: {
            title: true
          }
        }
      }
    });

    // Format recent transactions
    const recentTransactions = [
      ...recentOrders.map(order => ({
        id: order.id,
        type: 'PLATFORM_COMMISSION',
        amount: order.totalPrice * 0.06, // 6% platform fee
        date: order.createdAt,
        user: order.event.user.name || 'Unknown',
        event: order.event.title,
        status: 'COMPLETED'
      })),
      ...recentOrders.map(order => ({
        id: `proc-${order.id}`,
        type: 'PROCESSING_FEE',
        amount: order.totalPrice * 0.035, // 3.5% processing fee
        date: order.createdAt,
        user: order.event.user.name || 'Unknown',
        event: order.event.title,
        status: 'COMPLETED'
      })),
      ...recentNfcTransactions.map(tx => ({
        id: tx.id,
        type: 'PROCESSING_FEE',
        amount: tx.amount * 0.035, // 3.5% processing fee
        date: tx.createdAt,
        user: tx.vendor.businessName || 'Unknown Vendor',
        event: tx.event.title,
        status: 'COMPLETED'
      }))
    ]
    .sort((a, b) => b.date.getTime() - a.date.getTime())
    .slice(0, 5);

    // Calculate monthly revenue
    const currentMonthStart = startOfMonth(now);
    const currentMonthOrderRevenue = await db.order.aggregate({
      where: {
        status: 'Completed',
        createdAt: { gte: currentMonthStart }
      },
      _sum: { totalPrice: true }
    });

    const currentMonthNfcRevenue = await db.vendorNFCTransaction.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: { gte: currentMonthStart }
      },
      _sum: { amount: true }
    });

    const monthlyRevenue = (currentMonthOrderRevenue._sum.totalPrice || 0) +
                          (currentMonthNfcRevenue._sum.amount || 0);

    // Calculate previous month revenue for comparison
    const prevMonthStart = startOfMonth(subMonths(now, 1));
    const prevMonthEnd = endOfMonth(subMonths(now, 1));

    const prevMonthOrderRevenue = await db.order.aggregate({
      where: {
        status: 'Completed',
        createdAt: {
          gte: prevMonthStart,
          lte: prevMonthEnd
        }
      },
      _sum: { totalPrice: true }
    });

    const prevMonthNfcRevenue = await db.vendorNFCTransaction.aggregate({
      where: {
        status: 'COMPLETED',
        createdAt: {
          gte: prevMonthStart,
          lte: prevMonthEnd
        }
      },
      _sum: { amount: true }
    });

    const prevMonthRevenue = (prevMonthOrderRevenue._sum.totalPrice || 0) +
                            (prevMonthNfcRevenue._sum.amount || 0);

    // Calculate month-over-month growth percentage
    let monthlyGrowth = 0;
    if (prevMonthRevenue > 0) {
      monthlyGrowth = ((monthlyRevenue - prevMonthRevenue) / prevMonthRevenue) * 100;
    }

    // Return the dashboard data
    return NextResponse.json({
      totalRevenue,
      monthlyRevenue,
      totalTransactions,
      activeOrganizers,
      activeVendors,
      revenueByMonth: revenueByMonth.reverse(), // Most recent month first
      feeDistribution,
      recentTransactions,
      monthlyGrowth
    });
  } catch (error) {
    console.error('Error fetching financial dashboard data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial dashboard data' },
      { status: 500 }
    );
  }
}
