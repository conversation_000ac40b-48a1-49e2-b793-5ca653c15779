import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = await currentUser();
    
    // Check if user is admin or superadmin
    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const period = searchParams.get('period') || 'all'; // all, today, week, month, year
    
    // Calculate date range based on period
    const now = new Date();
    let startDate = new Date(0); // Default to beginning of time
    
    if (period === 'today') {
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    } else if (period === 'week') {
      const day = now.getDay();
      startDate = new Date(now);
      startDate.setDate(now.getDate() - day);
      startDate.setHours(0, 0, 0, 0);
    } else if (period === 'month') {
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
    } else if (period === 'year') {
      startDate = new Date(now.getFullYear(), 0, 1);
    }

    // Get total users count
    const totalUsers = await db.user.count();
    
    // Get users by role
    const usersByRole = await db.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });
    
    // Format users by role
    const formattedUsersByRole = usersByRole.reduce((acc, item) => {
      acc[item.role] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Get total events count
    const totalEvents = await db.event.count({
      where: {
        createdAt: {
          gte: startDate
        }
      }
    });
    
    // Get events by status
    const eventsByStatus = await db.event.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    });
    
    // Format events by status
    const formattedEventsByStatus = eventsByStatus.reduce((acc, item) => {
      acc[item.status] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Get total orders count
    const totalOrders = await db.order.count({
      where: {
        createdAt: {
          gte: startDate
        }
      }
    });
    
    // Get orders by status
    const ordersByStatus = await db.order.groupBy({
      by: ['status'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    });
    
    // Format orders by status
    const formattedOrdersByStatus = ordersByStatus.reduce((acc, item) => {
      acc[item.status] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Get total revenue
    const revenue = await db.order.aggregate({
      where: {
        status: 'Completed',
        createdAt: {
          gte: startDate
        }
      },
      _sum: {
        totalPrice: true
      }
    });

    // Get total tickets sold
    const ticketsSold = await db.ticket.aggregate({
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _sum: {
        quantity: true
      }
    });

    // Get recent events
    const recentEvents = await db.event.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Get recent orders
    const recentOrders = await db.order.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        },
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Get recent users
    const recentUsers = await db.user.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        createdAt: true
      }
    });

    // Get events by category
    const eventsByCategory = await db.event.groupBy({
      by: ['category'],
      where: {
        createdAt: {
          gte: startDate
        }
      },
      _count: {
        id: true
      }
    });

    // Format events by category
    const formattedEventsByCategory = eventsByCategory.reduce((acc, item) => {
      acc[item.category] = item._count.id;
      return acc;
    }, {} as Record<string, number>);

    // Get revenue by month (for the current year)
    const currentYear = now.getFullYear();
    const revenueByMonth = await Promise.all(
      Array.from({ length: 12 }, async (_, month) => {
        const startOfMonth = new Date(currentYear, month, 1);
        const endOfMonth = new Date(currentYear, month + 1, 0);
        
        const monthlyRevenue = await db.order.aggregate({
          where: {
            status: 'Completed',
            createdAt: {
              gte: startOfMonth,
              lte: endOfMonth
            }
          },
          _sum: {
            totalPrice: true
          }
        });
        
        return {
          month: month + 1,
          revenue: monthlyRevenue._sum.totalPrice || 0
        };
      })
    );

    return NextResponse.json({
      users: {
        total: totalUsers,
        byRole: formattedUsersByRole,
        recent: recentUsers.map(user => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          joinedAt: user.createdAt.toISOString()
        }))
      },
      events: {
        total: totalEvents,
        byStatus: formattedEventsByStatus,
        byCategory: formattedEventsByCategory,
        recent: recentEvents.map(event => ({
          id: event.id,
          title: event.title,
          organizer: event.user.name,
          status: event.status,
          startDate: event.startDate.toISOString(),
          createdAt: event.createdAt.toISOString()
        }))
      },
      orders: {
        total: totalOrders,
        byStatus: formattedOrdersByStatus,
        recent: recentOrders.map(order => ({
          id: order.id,
          eventTitle: order.event.title,
          customerName: order.customerName,
          amount: order.totalPrice,
          status: order.status,
          createdAt: order.createdAt.toISOString()
        }))
      },
      revenue: {
        total: revenue._sum.totalPrice || 0,
        byMonth: revenueByMonth
      },
      ticketsSold: ticketsSold._sum.quantity || 0,
      period
    });
  } catch (error) {
    console.error('Error fetching admin dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch admin dashboard stats' },
      { status: 500 }
    );
  }
}
