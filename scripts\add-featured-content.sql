-- Add featured status to some existing vendors
UPDATE "VendorProfile" 
SET featured = true 
WHERE "verificationStatus" = 'APPROVED' 
AND id IN (
  SELECT id FROM "VendorProfile" 
  WHERE "verificationStatus" = 'APPROVED' 
  LIMIT 6
);

-- Add featured status to some existing partners
UPDATE "Partner" 
SET featured = true 
WHERE "isVerified" = true 
AND id IN (
  SELECT id FROM "Partner" 
  WHERE "isVerified" = true 
  LIMIT 6
);

-- Add featured status to some existing events
UPDATE "Event" 
SET featured = true 
WHERE status = 'Published' 
AND "endDate" > NOW()
AND id IN (
  SELECT id FROM "Event" 
  WHERE status = 'Published' 
  AND "endDate" > NOW()
  LIMIT 6
);

-- Verify the updates
SELECT 'Featured Vendors' as type, COUNT(*) as count FROM "VendorProfile" WHERE featured = true
UNION ALL
SELECT 'Featured Partners' as type, COUNT(*) as count FROM "Partner" WHERE featured = true
UNION ALL
SELECT 'Featured Events' as type, COUNT(*) as count FROM "Event" WHERE featured = true;
