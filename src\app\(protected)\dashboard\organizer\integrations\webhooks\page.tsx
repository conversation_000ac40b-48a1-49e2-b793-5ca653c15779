'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Clipboard, Copy, Link, Plus, Trash2, Loader2, AlertCircle, RefreshCw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { RoleGate } from '@/components/auth/role-gate';
import { toast } from '@/components/ui/use-toast';
import { format } from 'date-fns';

// Available webhook events
const availableEvents = [
  { value: 'order.created', label: 'Order Created' },
  { value: 'order.updated', label: 'Order Updated' },
  { value: 'order.cancelled', label: 'Order Cancelled' },
  { value: 'payment.succeeded', label: 'Payment Succeeded' },
  { value: 'payment.failed', label: 'Payment Failed' },
  { value: 'ticket.created', label: 'Ticket Created' },
  { value: 'ticket.updated', label: 'Ticket Updated' },
  { value: 'ticket.scanned', label: 'Ticket Scanned' },
  { value: 'event.published', label: 'Event Published' },
  { value: 'event.updated', label: 'Event Updated' },
  { value: 'event.cancelled', label: 'Event Cancelled' }
];

interface Webhook {
  id: string;
  name: string;
  url: string;
  events: string[];
  active: boolean;
  createdAt: string;
  lastTriggered?: string;
  status: string;
  secret: string;
}

export default function WebhooksPage() {
  const [webhooks, setWebhooks] = useState<Webhook[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newWebhook, setNewWebhook] = useState({
    name: '',
    url: '',
    events: [] as string[]
  });
  const [selectedEvents, setSelectedEvents] = useState<string[]>([]);
  const [showSecret, setShowSecret] = useState(false);

  // Fetch webhooks
  const fetchWebhooks = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/organizer/webhooks');

      if (!response.ok) {
        throw new Error('Failed to fetch webhooks');
      }

      const data = await response.json();
      setWebhooks(data);
    } catch (error) {
      console.error('Error fetching webhooks:', error);
      setError('Failed to load webhooks. Please try again.');
      toast({
        title: 'Error',
        description: 'Failed to load webhooks',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load webhooks on component mount
  useEffect(() => {
    fetchWebhooks();
  }, []);

  const handleAddWebhook = async () => {
    if (!newWebhook.name || !newWebhook.url || selectedEvents.length === 0) {
      toast({
        title: 'Validation Error',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const response = await fetch('/api/organizer/webhooks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newWebhook.name,
          url: newWebhook.url,
          events: selectedEvents,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create webhook');
      }

      const webhook = await response.json();

      // Add the new webhook to the list
      setWebhooks([webhook, ...webhooks]);

      // Reset form
      setNewWebhook({
        name: '',
        url: '',
        events: []
      });
      setSelectedEvents([]);

      toast({
        title: 'Success',
        description: 'Webhook created successfully',
      });
    } catch (error) {
      console.error('Error creating webhook:', error);
      setError(error instanceof Error ? error.message : 'Failed to create webhook');
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create webhook',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const toggleWebhookStatus = async (id: string) => {
    const webhook = webhooks.find(w => w.id === id);
    if (!webhook) return;

    try {
      const response = await fetch(`/api/organizer/webhooks/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          active: !webhook.active,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update webhook');
      }

      const updatedWebhook = await response.json();

      // Update the webhook in the list
      setWebhooks(
        webhooks.map(w => w.id === id ? updatedWebhook : w)
      );

      toast({
        title: 'Success',
        description: `Webhook ${!webhook.active ? 'activated' : 'deactivated'} successfully`,
      });
    } catch (error) {
      console.error('Error updating webhook:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update webhook',
        variant: 'destructive',
      });
    }
  };

  const deleteWebhook = async (id: string) => {
    if (!confirm('Are you sure you want to delete this webhook?')) {
      return;
    }

    try {
      const response = await fetch(`/api/organizer/webhooks/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete webhook');
      }

      // Remove the webhook from the list
      setWebhooks(webhooks.filter(webhook => webhook.id !== id));

      toast({
        title: 'Success',
        description: 'Webhook deleted successfully',
      });
    } catch (error) {
      console.error('Error deleting webhook:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete webhook',
        variant: 'destructive',
      });
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: 'Copied to clipboard',
    });
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM d, yyyy h:mm a');
    } catch (error) {
      return 'Invalid date';
    }
  };

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Webhooks</h1>
          <p className="text-gray-500 mt-1">Receive real-time notifications about events in your account</p>
        </div>

        <Tabs defaultValue="webhooks">
          <TabsList>
            <TabsTrigger value="webhooks">Your Webhooks</TabsTrigger>
            <TabsTrigger value="create">Create Webhook</TabsTrigger>
            <TabsTrigger value="docs">Documentation</TabsTrigger>
          </TabsList>

          <TabsContent value="webhooks" className="space-y-4">
            {isLoading ? (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : error ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <AlertCircle className="h-10 w-10 text-red-500 mx-auto mb-4" />
                  <p className="text-gray-500 mb-4">{error}</p>
                  <Button variant="outline" onClick={fetchWebhooks}>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                </CardContent>
              </Card>
            ) : webhooks.length === 0 ? (
              <Card>
                <CardContent className="pt-6 text-center">
                  <p className="text-gray-500 mb-4">You haven&apos;t created any webhooks yet</p>
                  <Button variant="outline" onClick={() => (document.querySelector('[data-value="create"]') as HTMLElement)?.click()}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Your First Webhook
                  </Button>
                </CardContent>
              </Card>
            ) : (
              webhooks.map(webhook => (
                <Card key={webhook.id}>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-lg">{webhook.name}</CardTitle>
                        <CardDescription className="mt-1 flex items-center">
                          <Link className="h-3 w-3 mr-1" />
                          {webhook.url}
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-6 w-6 ml-1"
                            onClick={() => copyToClipboard(webhook.url)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </CardDescription>
                      </div>
                      <Badge variant={webhook.active ? "default" : "outline"}>
                        {webhook.active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-500 mb-1">Events</p>
                        <div className="space-y-1">
                          {webhook.events.map(event => (
                            <Badge key={event} variant="secondary" className="mr-1">
                              {availableEvents.find(e => e.value === event)?.label || event}
                            </Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <p className="text-gray-500 mb-1">Last Triggered</p>
                        <p>{webhook.lastTriggered ? formatDate(webhook.lastTriggered) : 'Never'}</p>
                      </div>
                      <div>
                        <p className="text-gray-500 mb-1">Status</p>
                        <Badge variant={webhook.status === 'success' ? 'success' : webhook.status === 'failed' ? 'destructive' : 'outline'}>
                          {webhook.status === 'success' ? 'Success' : webhook.status === 'failed' ? 'Failed' : 'Pending'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between border-t pt-4">
                    <div className="flex items-center">
                      <Switch
                        id={`webhook-status-${webhook.id}`}
                        checked={webhook.active}
                        onCheckedChange={() => toggleWebhookStatus(webhook.id)}
                      />
                      <Label htmlFor={`webhook-status-${webhook.id}`} className="ml-2">
                        {webhook.active ? 'Enabled' : 'Disabled'}
                      </Label>
                    </div>
                    <Button variant="destructive" size="sm" onClick={() => deleteWebhook(webhook.id)}>
                      <Trash2 className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </CardFooter>
                </Card>
              ))
            )}
          </TabsContent>

          <TabsContent value="create">
            <Card>
              <CardHeader>
                <CardTitle>Create a New Webhook</CardTitle>
                <CardDescription>
                  Webhooks allow your application to receive real-time updates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="webhook-name">Webhook Name</Label>
                    <Input
                      id="webhook-name"
                      placeholder="e.g., Order Notifications"
                      value={newWebhook.name}
                      onChange={(e) => setNewWebhook({...newWebhook, name: e.target.value})}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhook-url">Endpoint URL</Label>
                    <Input
                      id="webhook-url"
                      placeholder="https://example.com/webhook"
                      value={newWebhook.url}
                      onChange={(e) => setNewWebhook({...newWebhook, url: e.target.value})}
                    />
                    <p className="text-xs text-gray-500">
                      This URL will receive HTTP POST requests when the selected events occur
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Label>Events to Subscribe</Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {availableEvents.map(event => (
                        <div key={event.value} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            id={`event-${event.value}`}
                            checked={selectedEvents.includes(event.value)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedEvents([...selectedEvents, event.value]);
                              } else {
                                setSelectedEvents(selectedEvents.filter(e => e !== event.value));
                              }
                            }}
                            className="rounded border-gray-300"
                          />
                          <Label htmlFor={`event-${event.value}`}>{event.label}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between border-t pt-4">
                <div className="flex items-center">
                  <Switch id="webhook-active" defaultChecked />
                  <Label htmlFor="webhook-active" className="ml-2">
                    Enable webhook upon creation
                  </Label>
                </div>
                <Button onClick={handleAddWebhook} disabled={isSubmitting}>
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Webhook'
                  )}
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>

          <TabsContent value="docs">
            <Card>
              <CardHeader>
                <CardTitle>Webhook Documentation</CardTitle>
                <CardDescription>
                  Learn how to use webhooks to integrate with our platform
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">What are Webhooks?</h3>
                  <p className="text-gray-600">
                    Webhooks allow your application to receive real-time notifications when certain events occur in your account.
                    Instead of polling our API for changes, webhooks push data to your application as events happen.
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Authentication</h3>
                  <p className="text-gray-600 mb-2">
                    Each webhook request includes a signature header that allows you to verify the request came from our servers.
                  </p>
                  <div className="bg-gray-50 p-3 rounded-md">
                    <div className="flex justify-between items-center mb-2">
                      <p className="font-mono text-sm">Your Webhook Secret</p>
                      <div className="flex items-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSecret(!showSecret)}
                        >
                          {showSecret ? 'Hide' : 'Show'}
                        </Button>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8"
                          onClick={() => {
                            if (webhooks.length > 0) {
                              copyToClipboard(showSecret ? webhooks[0].secret : '••••••••••••••••••••••••••');
                            }
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <p className="font-mono text-sm bg-gray-100 p-2 rounded">
                      {webhooks.length > 0
                        ? (showSecret ? webhooks[0].secret : '••••••••••••••••••••••••••')
                        : 'No webhooks created yet'
                      }
                    </p>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-2">Payload Format</h3>
                  <p className="text-gray-600 mb-2">
                    All webhook payloads are sent as JSON in the request body. Here&apos;s an example of an order.created event:
                  </p>
                  <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto text-sm">
{`{
  "id": "evt_123456789",
  "type": "order.created",
  "created": "2023-10-15T10:30:00Z",
  "data": {
    "id": "ord_123456",
    "customer": {
      "id": "cus_123456",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "amount": 4999,
    "currency": "usd",
    "status": "paid",
    "items": [
      {
        "id": "item_123456",
        "name": "Conference Ticket",
        "quantity": 1,
        "price": 4999
      }
    ]
  }
}`}
                  </pre>
                </div>

                <Alert>
                  <AlertTitle>Best Practices</AlertTitle>
                  <AlertDescription>
                    <ul className="list-disc pl-5 space-y-1 mt-2">
                      <li>Respond to webhook requests with a 2xx status code as quickly as possible</li>
                      <li>Implement retry logic in case your endpoint is temporarily unavailable</li>
                      <li>Verify the signature of each webhook request to ensure it came from our servers</li>
                      <li>Process webhooks asynchronously to avoid timeouts</li>
                    </ul>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
