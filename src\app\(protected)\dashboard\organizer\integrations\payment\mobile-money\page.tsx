'use client';

import { useState } from 'react';

// Define the provider type
type MobileMoneyProvider = 'mpesa' | 'mtn' | 'airtel' | 'orange' | 'vodacom' | 'other';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Copy, Eye, EyeOff, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

export default function MobileMoneyPage() {
  const [showSecretKey, setShowSecretKey] = useState(false);
  const [mobileMoneyForm, setMobileMoneyForm] = useState<{
    provider: MobileMoneyProvider;
    apiKey: string;
    apiSecret: string;
    shortcode: string;
    callbackUrl: string;
  }>({
    provider: 'mpesa',
    apiKey: '',
    apiSecret: '',
    shortcode: '',
    callbackUrl: 'https://yourdomain.com/api/payments/mobile-money/callback'
  });

  const handleConnectMobileMoney = () => {
    if (!mobileMoneyForm.apiKey || !mobileMoneyForm.apiSecret || !mobileMoneyForm.shortcode) {
      return;
    }

    // Handle connection logic
    alert(`Connected to ${mobileMoneyForm.provider} with shortcode ${mobileMoneyForm.shortcode}`);

    // Reset form
    setMobileMoneyForm({
      ...mobileMoneyForm,
      apiKey: '',
      apiSecret: ''
    });
  };

  return (
    <div className="container mx-auto py-6">
      <div className="mb-6">
        <Link href="/dashboard/organizer/integrations/payment" className="flex items-center text-sm text-gray-500 hover:text-gray-900 mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Payment Gateways
        </Link>
        <h1 className="text-3xl font-bold">Mobile Money Integration</h1>
        <p className="text-gray-500 mt-1">Connect to popular mobile money services to accept payments</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Connect Mobile Money</CardTitle>
              <CardDescription>
                Accept payments via popular mobile money services like M-Pesa, MTN Mobile Money, and more
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="mobile-money-provider">Mobile Money Provider</Label>
                  <Select
                    value={mobileMoneyForm.provider}
                    onValueChange={(value: MobileMoneyProvider) => setMobileMoneyForm({...mobileMoneyForm, provider: value})}
                  >
                    <SelectTrigger id="mobile-money-provider">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="mpesa">M-Pesa</SelectItem>
                      <SelectItem value="mtn">MTN Mobile Money</SelectItem>
                      <SelectItem value="airtel">Airtel Money</SelectItem>
                      <SelectItem value="orange">Orange Money</SelectItem>
                      <SelectItem value="vodacom">Vodacom M-Pesa</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile-money-api-key">API Key</Label>
                  <Input
                    id="mobile-money-api-key"
                    placeholder="Enter your API key"
                    value={mobileMoneyForm.apiKey}
                    onChange={(e) => setMobileMoneyForm({...mobileMoneyForm, apiKey: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile-money-secret">API Secret</Label>
                  <div className="relative">
                    <Input
                      id="mobile-money-secret"
                      type={showSecretKey ? "text" : "password"}
                      placeholder="Enter your API secret"
                      value={mobileMoneyForm.apiSecret}
                      onChange={(e) => setMobileMoneyForm({...mobileMoneyForm, apiSecret: e.target.value})}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      className="absolute right-0 top-0 h-full px-3"
                      onClick={() => setShowSecretKey(!showSecretKey)}
                    >
                      {showSecretKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile-money-shortcode">Business Shortcode/Paybill</Label>
                  <Input
                    id="mobile-money-shortcode"
                    placeholder="Enter your business shortcode or paybill number"
                    value={mobileMoneyForm.shortcode}
                    onChange={(e) => setMobileMoneyForm({...mobileMoneyForm, shortcode: e.target.value})}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="mobile-money-callback">Callback URL</Label>
                  <div className="flex">
                    <Input
                      id="mobile-money-callback"
                      value="https://yourdomain.com/api/payments/mobile-money/callback"
                      readOnly
                      className="rounded-r-none"
                    />
                    <Button
                      type="button"
                      variant="secondary"
                      className="rounded-l-none"
                      onClick={() => navigator.clipboard.writeText("https://yourdomain.com/api/payments/mobile-money/callback")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    Use this URL in your mobile money provider dashboard for transaction callbacks
                  </p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between border-t pt-4">
              <Button variant="outline" onClick={() => {
                // Open documentation based on selected provider
                const docUrls: Record<MobileMoneyProvider, string> = {
                  mpesa: 'https://developer.safaricom.co.ke/',
                  mtn: 'https://momodeveloper.mtn.com/',
                  airtel: 'https://developers.airtel.africa/',
                  orange: 'https://developer.orange.com/apis/orange-money',
                  vodacom: 'https://developers.vodacom.co.tz/',
                  other: 'https://developer.safaricom.co.ke/'
                };
                window.open(docUrls[mobileMoneyForm.provider as MobileMoneyProvider], '_blank');
              }}>
                Provider Documentation
              </Button>
              <Button onClick={handleConnectMobileMoney}>
                Connect Mobile Money
              </Button>
            </CardFooter>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Mobile Money Providers</CardTitle>
              <CardDescription>
                Supported mobile money services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-green-600 font-bold">M</span>
                  </div>
                  <div>
                    <p className="font-medium">M-Pesa</p>
                    <p className="text-xs text-gray-500">Kenya, Tanzania</p>
                  </div>
                </li>
                <li className="flex items-center">
                  <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-yellow-600 font-bold">M</span>
                  </div>
                  <div>
                    <p className="font-medium">MTN Mobile Money</p>
                    <p className="text-xs text-gray-500">Ghana, Uganda, Rwanda</p>
                  </div>
                </li>
                <li className="flex items-center">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-red-600 font-bold">A</span>
                  </div>
                  <div>
                    <p className="font-medium">Airtel Money</p>
                    <p className="text-xs text-gray-500">Multiple African countries</p>
                  </div>
                </li>
                <li className="flex items-center">
                  <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                    <span className="text-orange-600 font-bold">O</span>
                  </div>
                  <div>
                    <p className="font-medium">Orange Money</p>
                    <p className="text-xs text-gray-500">West and Central Africa</p>
                  </div>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
