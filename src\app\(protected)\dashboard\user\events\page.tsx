'use client';

import React, { useState } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { CalendarDays, Search, Clock, MapPin, ArrowRight } from 'lucide-react';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';

export default function UserEventsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('upcoming');

  // Mock data for events
  const events = [
    {
      id: 'event1',
      title: 'Summer Music Festival',
      date: '2023-07-15T18:00:00Z',
      location: 'Central Park, New York',
      status: 'UPCOMING',
      image: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
      description: 'A weekend of amazing music performances featuring top artists from around the world.'
    },
    {
      id: 'event2',
      title: 'Tech Conference 2023',
      date: '2023-08-10T09:00:00Z',
      location: 'Convention Center, San Francisco',
      status: 'UPCOMING',
      image: 'https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
      description: 'Join industry leaders and innovators for the biggest tech event of the year.'
    },
    {
      id: 'event3',
      title: 'Art Exhibition',
      date: '2023-06-20T10:00:00Z',
      location: 'Modern Art Gallery, Chicago',
      status: 'PAST',
      image: 'https://images.unsplash.com/photo-1531058020387-3be344556be6?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
      description: 'Explore contemporary art from emerging and established artists.'
    },
    {
      id: 'event4',
      title: 'Food & Wine Festival',
      date: '2023-05-25T12:00:00Z',
      location: 'Waterfront Park, Seattle',
      status: 'PAST',
      image: 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
      description: 'Sample delicious cuisine and fine wines from top chefs and wineries.'
    },
    {
      id: 'event5',
      title: 'Charity Gala',
      date: '2023-09-05T19:00:00Z',
      location: 'Grand Hotel, Boston',
      status: 'UPCOMING',
      image: 'https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800&auto=format&fit=crop&q=60&ixlib=rb-4.0.3',
      description: 'An elegant evening supporting local charities with dinner, dancing, and auctions.'
    }
  ];

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Filter events based on tab and search term
  const filteredEvents = events.filter(event => {
    // Filter by tab
    if (activeTab === 'upcoming' && event.status !== 'UPCOMING') {
      return false;
    }
    if (activeTab === 'past' && event.status !== 'PAST') {
      return false;
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      return (
        event.title.toLowerCase().includes(term) ||
        event.location.toLowerCase().includes(term) ||
        event.description.toLowerCase().includes(term)
      );
    }

    return true;
  });

  return (
    <RoleGate allowedRole="USER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">My Events</h1>
            <p className="text-gray-500 dark:text-gray-400">
              View all events you&apos;re attending
            </p>
          </div>
          <Button className="mt-4 md:mt-0" asChild>
            <Link href="/dashboard/user/browse-events">
              <CalendarDays className="mr-2 h-4 w-4" />
              Browse Events
            </Link>
          </Button>
        </div>

        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
            <Input
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <Tabs defaultValue="upcoming" value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming</TabsTrigger>
            <TabsTrigger value="past">Past</TabsTrigger>
            <TabsTrigger value="saved">Saved</TabsTrigger>
          </TabsList>

          <TabsContent value="upcoming" className="space-y-6">
            {filteredEvents.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <CalendarDays className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No upcoming events</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You don&apos;t have any upcoming events. Browse events to find something you&apos;re interested in.</p>
                  <Button asChild>
                    <Link href="/dashboard/user/browse-events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredEvents.map((event, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="h-40 relative">
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge className="bg-green-500 hover:bg-green-600">Upcoming</Badge>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-bold mb-2">{event.title}</h3>
                      <div className="flex items-center text-gray-500 dark:text-gray-400 mb-1">
                        <Clock className="h-4 w-4 mr-2" />
                        <span className="text-sm">{formatDate(event.date)}</span>
                      </div>
                      <div className="flex items-start text-gray-500 dark:text-gray-400 mb-4">
                        <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                        <span className="text-sm">{event.location}</span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">{event.description}</p>
                      <div className="flex justify-between items-center">
                        <Button variant="outline" asChild>
                          <Link href={`/events/${event.id}`}>
                            View Details
                          </Link>
                        </Button>
                        <Button asChild>
                          <Link href={`/dashboard/user/tickets?event=${event.id}`}>
                            My Tickets
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="past" className="space-y-6">
            {filteredEvents.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                    <CalendarDays className="h-8 w-8 text-gray-500" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No past events</h3>
                  <p className="text-gray-500 dark:text-gray-400 mb-4">You haven&apos;t attended any events yet.</p>
                  <Button asChild>
                    <Link href="/dashboard/user/browse-events">Browse Events</Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {filteredEvents.map((event, index) => (
                  <Card key={index} className="overflow-hidden">
                    <div className="h-40 relative">
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-full object-cover grayscale"
                      />
                      <div className="absolute top-2 right-2">
                        <Badge variant="outline" className="bg-gray-500 hover:bg-gray-600 text-white">Past</Badge>
                      </div>
                    </div>
                    <CardContent className="p-6">
                      <h3 className="text-lg font-bold mb-2">{event.title}</h3>
                      <div className="flex items-center text-gray-500 dark:text-gray-400 mb-1">
                        <Clock className="h-4 w-4 mr-2" />
                        <span className="text-sm">{formatDate(event.date)}</span>
                      </div>
                      <div className="flex items-start text-gray-500 dark:text-gray-400 mb-4">
                        <MapPin className="h-4 w-4 mr-2 mt-0.5" />
                        <span className="text-sm">{event.location}</span>
                      </div>
                      <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-2">{event.description}</p>
                      <div className="flex justify-between items-center">
                        <Button variant="outline" asChild>
                          <Link href={`/events/${event.id}`}>
                            View Details
                          </Link>
                        </Button>
                        <Button variant="secondary" asChild>
                          <Link href={`/dashboard/user/tickets?event=${event.id}`}>
                            View Tickets
                          </Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="saved" className="space-y-6">
            <Card>
              <CardContent className="p-8 text-center">
                <div className="mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
                  <CalendarDays className="h-8 w-8 text-gray-500" />
                </div>
                <h3 className="text-lg font-medium mb-2">No saved events</h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">You haven&apos;t saved any events yet. Browse events and save the ones you&apos;re interested in.</p>
                <Button asChild>
                  <Link href="/dashboard/user/browse-events">Browse Events</Link>
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </RoleGate>
  );
}
