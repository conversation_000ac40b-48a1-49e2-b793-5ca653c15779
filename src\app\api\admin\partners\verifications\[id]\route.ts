import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * PATCH /api/admin/partners/verifications/:id
 * Approve or reject a partner verification request
 */
export async function PATCH(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = await currentUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is an admin
    if (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN') {
      return NextResponse.json(
        { error: 'You do not have permission to perform this action' },
        { status: 403 }
      );
    }

    const resolvedParams = await params;
    const partnerId = resolvedParams.id;
    const { action, notes } = await req.json();

    // Validate the action
    if (!action || !['approve', 'reject'].includes(action)) {
      return NextResponse.json(
        { error: 'Invalid action. Must be "approve" or "reject".' },
        { status: 400 }
      );
    }

    // Get the partner
    const partner = await db.partner.findUnique({
      where: { id: partnerId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    if (!partner) {
      return NextResponse.json(
        { error: 'Partner not found' },
        { status: 404 }
      );
    }

    // Check if partner is already verified
    if (partner.isVerified && action === 'approve') {
      return NextResponse.json(
        { error: 'Partner is already verified' },
        { status: 400 }
      );
    }

    if (action === 'approve') {
      // Approve the partner
      const updatedPartner = await db.partner.update({
        where: { id: partnerId },
        data: {
          isVerified: true,
          verifiedAt: new Date()
        }
      });

      return NextResponse.json({
        message: 'Partner verification approved successfully',
        partner: updatedPartner
      });

    } else if (action === 'reject') {
      // Reject the partner verification
      const updatedPartner = await db.partner.update({
        where: { id: partnerId },
        data: {
          isVerified: false,
          verifiedAt: null
        }
      });

      return NextResponse.json({
        message: 'Partner verification rejected successfully',
        partner: updatedPartner
      });
    }

  } catch (error) {
    console.error('Error processing verification:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
