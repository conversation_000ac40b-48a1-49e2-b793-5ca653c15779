'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
// import { Calendar } from '@/components/ui/calendar';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, Download, RefreshCw } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

// Define report types
type ReportType = 'sales' | 'users' | 'events' | 'tickets';
type PeriodType = 'week' | 'month' | 'quarter' | 'year' | 'custom';

// Basic report data interface
interface ReportData {
  reportType: ReportType;
  dateRange: {
    start: string;
    end: string;
  };
  summary: Record<string, any>;
  [key: string]: any;
}

export default function AdminReportsPage() {
  // State for report parameters
  const [reportType, setReportType] = useState<ReportType>('sales');
  const [period, setPeriod] = useState<PeriodType>('month');
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // State for report data
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D', '#FFC658'];

  // Fetch report data
  const fetchReport = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('type', reportType);

      if (period === 'custom' && startDate && endDate) {
        params.append('startDate', startDate.toISOString());
        params.append('endDate', endDate.toISOString());
      } else {
        params.append('period', period);
      }

      // Fetch report from API
      const response = await fetch(`/api/admin/reports?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setReportData(data);
      setLoading(false);
    } catch (err) {
      console.error('Error fetching report:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch report');
      setLoading(false);
    }
  }, [reportType, period, startDate, endDate]);

  // Fetch report when parameters change
  useEffect(() => {
    // Only fetch if not custom period (since we removed the date pickers)
    if (period !== 'custom') {
      fetchReport();
    }
  }, [reportType, period, fetchReport]);

  // Format currency
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(value);
  };

  // Format percentage
  const formatPercentage = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 1,
      maximumFractionDigits: 1,
    }).format(value);
  };

  // Format date range for display
  const formatDateRange = () => {
    if (!reportData) return '';

    const start = new Date(reportData.dateRange.start);
    const end = new Date(reportData.dateRange.end);

    return `${format(start, 'MMM d, yyyy')} - ${format(end, 'MMM d, yyyy')}`;
  };

  // Export report as CSV
  const exportReport = () => {
    if (!reportData) return;

    let csvContent = '';

    // Add report type and date range
    csvContent += `Report Type,${reportData.reportType}\n`;
    csvContent += `Date Range,${formatDateRange()}\n\n`;

    // Add summary data
    csvContent += 'Summary\n';
    Object.entries(reportData.summary).forEach(([key, value]) => {
      csvContent += `${key},${value}\n`;
    });
    csvContent += '\n';

    // Add chart data based on report type
    if (reportType === 'sales' && reportData.dailySales) {
      csvContent += 'Daily Sales\n';
      csvContent += 'Date,Revenue,Orders,Tickets\n';
      reportData.dailySales.forEach((item: any) => {
        csvContent += `${item.date},${item.revenue},${item.orders},${item.tickets}\n`;
      });
    } else if (reportType === 'users' && reportData.dailyUsers) {
      csvContent += 'Daily Users\n';
      csvContent += 'Date,Count,Verified\n';
      reportData.dailyUsers.forEach((item: any) => {
        csvContent += `${item.date},${item.count},${item.verified}\n`;
      });
    }

    // Create and download CSV file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${reportType}_report_${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: 'Report Exported',
      description: 'The report has been exported as a CSV file.',
    });
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <h1 className="text-3xl font-bold">Reports</h1>

        <div className="flex flex-col sm:flex-row gap-4 mt-4 md:mt-0">
          <Button
            variant="outline"
            onClick={fetchReport}
            disabled={loading || period === 'custom'}
          >
            {loading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={exportReport}
            disabled={!reportData || loading}
          >
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        </div>
      </div>

      {/* Report Controls */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Report Settings</CardTitle>
          <CardDescription>Configure your report parameters</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <label className="block text-sm font-medium mb-2">Report Type</label>
              <Select value={reportType} onValueChange={(value) => setReportType(value as ReportType)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sales">Sales Report</SelectItem>
                  <SelectItem value="users">User Report</SelectItem>
                  <SelectItem value="events">Event Report</SelectItem>
                  <SelectItem value="tickets">Ticket Report</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Time Period</label>
              <Select value={period} onValueChange={(value) => setPeriod(value as PeriodType)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select time period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="week">Last 7 Days</SelectItem>
                  <SelectItem value="month">Last 30 Days</SelectItem>
                  <SelectItem value="quarter">Last 90 Days</SelectItem>
                  <SelectItem value="year">Last 365 Days</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Custom date range option removed for now */}
          </div>
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-6 my-6">
          <h3 className="text-lg font-semibold mb-2">Error Loading Report</h3>
          <p>{error}</p>
          <Button
            onClick={fetchReport}
            className="mt-4"
            disabled={loading || period === 'custom'}
          >
            Try Again
          </Button>
        </div>
      )}

      {/* Report Display */}
      {reportData && !error && (
        <div className="space-y-8">
          {/* Report Header */}
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                <div>
                  <CardTitle className="capitalize">{reportType} Report</CardTitle>
                  <CardDescription>{formatDateRange()}</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {/* Dynamic summary metrics based on report type */}
                {reportType === 'sales' && (
                  <>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Revenue</p>
                      <p className="text-2xl font-bold">{formatCurrency(reportData.summary.totalRevenue)}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Total Orders</p>
                      <p className="text-2xl font-bold">{reportData.summary.totalOrders}</p>
                    </div>
                    <div className="bg-amber-50 p-4 rounded-lg">
                      <p className="text-sm text-amber-600 font-medium">Total Tickets</p>
                      <p className="text-2xl font-bold">{reportData.summary.totalTickets}</p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Avg. Order Value</p>
                      <p className="text-2xl font-bold">{formatCurrency(reportData.summary.averageOrderValue)}</p>
                    </div>
                  </>
                )}

                {reportType === 'users' && (
                  <>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Users</p>
                      <p className="text-2xl font-bold">{reportData.summary.totalUsers}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Verified Users</p>
                      <p className="text-2xl font-bold">{reportData.summary.verifiedUsers}</p>
                    </div>
                    <div className="bg-amber-50 p-4 rounded-lg">
                      <p className="text-sm text-amber-600 font-medium">Verification Rate</p>
                      <p className="text-2xl font-bold">{formatPercentage(reportData.summary.verificationRate)}</p>
                    </div>
                  </>
                )}

                {reportType === 'events' && (
                  <>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Events</p>
                      <p className="text-2xl font-bold">{reportData.summary.totalEvents}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Published Events</p>
                      <p className="text-2xl font-bold">{reportData.summary.publishedEvents}</p>
                    </div>
                    <div className="bg-amber-50 p-4 rounded-lg">
                      <p className="text-sm text-amber-600 font-medium">Total Revenue</p>
                      <p className="text-2xl font-bold">{formatCurrency(reportData.summary.totalRevenue)}</p>
                    </div>
                  </>
                )}

                {reportType === 'tickets' && (
                  <>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <p className="text-sm text-blue-600 font-medium">Total Tickets</p>
                      <p className="text-2xl font-bold">{reportData.summary.totalTickets}</p>
                    </div>
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Sold Tickets</p>
                      <p className="text-2xl font-bold">{reportData.summary.soldTickets}</p>
                    </div>
                    <div className="bg-amber-50 p-4 rounded-lg">
                      <p className="text-sm text-amber-600 font-medium">Available Tickets</p>
                      <p className="text-2xl font-bold">{reportData.summary.availableTickets}</p>
                    </div>
                    <div className="bg-purple-50 p-4 rounded-lg">
                      <p className="text-sm text-purple-600 font-medium">Sales Rate</p>
                      <p className="text-2xl font-bold">{formatPercentage(reportData.summary.salesRate)}</p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Time Series Chart */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {reportType === 'sales' && 'Daily Sales'}
                  {reportType === 'users' && 'User Registrations'}
                  {reportType === 'events' && 'Event Creation'}
                  {reportType === 'tickets' && 'Ticket Sales'}
                </CardTitle>
                <CardDescription>Trend over time</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={
                      reportType === 'sales' ? reportData.dailySales :
                      reportType === 'users' ? reportData.dailyUsers :
                      reportType === 'events' ? reportData.dailyEvents :
                      reportData.dailyTickets
                    }
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    {reportType === 'sales' && (
                      <>
                        <Bar dataKey="revenue" name="Revenue ($)" fill="#0088FE" />
                        <Bar dataKey="orders" name="Orders" fill="#00C49F" />
                      </>
                    )}
                    {reportType === 'users' && (
                      <>
                        <Bar dataKey="count" name="Total Users" fill="#0088FE" />
                        <Bar dataKey="verified" name="Verified Users" fill="#00C49F" />
                      </>
                    )}
                    {reportType === 'events' && (
                      <>
                        <Bar dataKey="count" name="Total Events" fill="#0088FE" />
                        <Bar dataKey="published" name="Published Events" fill="#00C49F" />
                      </>
                    )}
                    {reportType === 'tickets' && (
                      <>
                        <Bar dataKey="total" name="Total Tickets" fill="#0088FE" />
                        <Bar dataKey="sold" name="Sold Tickets" fill="#00C49F" />
                      </>
                    )}
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Distribution Chart */}
            <Card>
              <CardHeader>
                <CardTitle>
                  {reportType === 'sales' && 'Sales by Category'}
                  {reportType === 'users' && 'Users by Role'}
                  {reportType === 'events' && 'Events by Category'}
                  {reportType === 'tickets' && 'Tickets by Category'}
                </CardTitle>
                <CardDescription>Distribution breakdown</CardDescription>
              </CardHeader>
              <CardContent className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={
                        reportType === 'sales' ? reportData.categorySales :
                        reportType === 'users' ? Object.entries(reportData.usersByRole).map(([role, count]) => ({ name: role, value: count as number })) :
                        reportType === 'events' ? Object.entries(reportData.eventsByCategory).map(([category, count]) => ({ name: category, value: count as number })) :
                        reportData.categoryTickets
                      }
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      nameKey={
                        reportType === 'sales' ? 'category' :
                        reportType === 'users' ? 'name' :
                        reportType === 'events' ? 'name' :
                        'category'
                      }
                      label={({ name, percent }: { name: string; percent: number }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {(
                        reportType === 'sales' ? reportData.categorySales :
                        reportType === 'users' ? Object.entries(reportData.usersByRole).map(([role, count]) => ({ name: role, value: count as number })) :
                        reportType === 'events' ? Object.entries(reportData.eventsByCategory).map(([category, count]) => ({ name: category, value: count as number })) :
                        reportData.categoryTickets
                      ).map((entry: { name: string; value: number; category?: string }, index: number) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Top Items Table */}
          {(reportType === 'sales' || reportType === 'events') && (
            <Card>
              <CardHeader>
                <CardTitle>
                  {reportType === 'sales' && 'Top Selling Events'}
                  {reportType === 'events' && 'Top Performing Events'}
                </CardTitle>
                <CardDescription>
                  {reportType === 'sales' && 'Events with the highest revenue'}
                  {reportType === 'events' && 'Events with the highest ticket sales'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Event</th>
                        <th className="text-right py-3 px-4">Revenue</th>
                        <th className="text-right py-3 px-4">Orders</th>
                        <th className="text-right py-3 px-4">Tickets</th>
                      </tr>
                    </thead>
                    <tbody>
                      {(reportType === 'sales' ? reportData.topEvents : reportData.topEvents).map((event: { eventId: string; eventTitle: string; revenue: number; orders?: number; tickets?: number; ticketsSold?: number }, index: number) => (
                        <tr key={event.eventId} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                          <td className="py-3 px-4">{event.eventTitle}</td>
                          <td className="text-right py-3 px-4">{formatCurrency(event.revenue)}</td>
                          <td className="text-right py-3 px-4">{event.orders}</td>
                          <td className="text-right py-3 px-4">{event.tickets || event.ticketsSold}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recent Users Table */}
          {reportType === 'users' && reportData.recentUsers && (
            <Card>
              <CardHeader>
                <CardTitle>Recent Users</CardTitle>
                <CardDescription>Latest user registrations</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4">Name</th>
                        <th className="text-left py-3 px-4">Email</th>
                        <th className="text-left py-3 px-4">Role</th>
                        <th className="text-left py-3 px-4">Registered</th>
                        <th className="text-left py-3 px-4">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      {reportData.recentUsers.map((user: { id: string; name: string; email: string; role: string; createdAt: string; isVerified: boolean }, index: number) => (
                        <tr key={user.id} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                          <td className="py-3 px-4">{user.name}</td>
                          <td className="py-3 px-4">{user.email}</td>
                          <td className="py-3 px-4">{user.role}</td>
                          <td className="py-3 px-4">{format(new Date(user.createdAt), 'MMM d, yyyy')}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              user.isVerified
                                ? 'bg-green-100 text-green-800'
                                : 'bg-amber-100 text-amber-800'
                            }`}>
                              {user.isVerified ? 'Verified' : 'Unverified'}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
