import { NextRequest, NextResponse } from 'next/server';
import { currentUser } from '@/lib/auth';
import { prisma } from '@/lib/prisma-client';

/**
 * GET /api/organizer/events/:id/nfc-transactions
 * Get all NFC transactions for a specific event
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Get the authenticated user from the session
    const user = await currentUser();
    
    if (!user?.id) {
      return NextResponse.json({ 
        error: 'Not authenticated'
      }, { status: 401 });
    }
    
    // Check if user is an organizer
    if (user.role !== 'ORGANIZER') {
      return NextResponse.json({ 
        error: 'Unauthorized. Organizer access required'
      }, { status: 403 });
    }
    
    const { id: eventId } = await params;
    
    // Check if the event belongs to the organizer
    const event = await prisma.event.findUnique({
      where: {
        id: eventId,
        userId: user.id!
      }
    });
    
    if (!event) {
      return NextResponse.json({ 
        error: 'Event not found or does not belong to you'
      }, { status: 404 });
    }
    
    // Get all NFC transactions associated with the event
    // For now, we'll return a mock response since we don't have the actual relationship yet
    // In a real implementation, you would query the database for transactions linked to this event
    
    // Mock data for demonstration
    const mockTransactions = [
      {
        id: 'txn_1',
        deviceId: 'device_1',
        amount: 50.00,
        type: 'TOP_UP',
        description: 'Initial top-up',
        timestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_2',
        deviceId: 'device_1',
        amount: -15.50,
        type: 'PAYMENT',
        description: 'Food purchase',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_3',
        deviceId: 'device_2',
        amount: 25.00,
        type: 'TOP_UP',
        description: 'Initial top-up',
        timestamp: new Date(Date.now() - 86400000 * 1.5).toISOString(), // 1.5 days ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_4',
        deviceId: 'device_2',
        amount: -10.00,
        type: 'PAYMENT',
        description: 'Merchandise purchase',
        timestamp: new Date(Date.now() - 43200000).toISOString(), // 12 hours ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_5',
        deviceId: 'device_5',
        amount: 75.00,
        type: 'TOP_UP',
        description: 'Initial top-up',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_6',
        deviceId: 'device_5',
        amount: -25.00,
        type: 'PAYMENT',
        description: 'Drink purchase',
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_7',
        deviceId: 'device_4',
        amount: 20.00,
        type: 'TOP_UP',
        description: 'Initial top-up',
        timestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_8',
        deviceId: 'device_4',
        amount: -9.75,
        type: 'PAYMENT',
        description: 'Food purchase',
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_9',
        deviceId: 'device_3',
        amount: 30.00,
        type: 'TOP_UP',
        description: 'Initial top-up',
        timestamp: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
        status: 'COMPLETED'
      },
      {
        id: 'txn_10',
        deviceId: 'device_3',
        amount: -30.00,
        type: 'REFUND',
        description: 'Full refund on deactivation',
        timestamp: new Date(Date.now() - 86400000 * 2).toISOString(), // 2 days ago
        status: 'COMPLETED'
      }
    ];
    
    return NextResponse.json(mockTransactions);
    
  } catch (error) {
    console.error('Error fetching NFC transactions for event:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch NFC transactions'
    }, { status: 500 });
  }
}
