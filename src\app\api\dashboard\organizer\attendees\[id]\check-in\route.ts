import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

// POST: Check in an attendee
export async function POST(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await props.params;
    const { id  } = await params;

    // Get events for this organizer
    const events = await db.event.findMany({
      where: { userId: user.id },
      select: { id: true }
    });
    
    const eventIds = events.map(event => event.id);

    // Fetch the order (attendee)
    const order = await db.order.findUnique({
      where: { id },
      include: {
        event: {
          select: {
            id: true,
            userId: true
          }
        }
      }
    });

    if (!order) {
      return NextResponse.json({ error: 'Attendee not found' }, { status: 404 });
    }

    // Check if the order belongs to an event organized by the current user
    if (!eventIds.includes(order.event.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update check-in status
    const updatedOrder = await db.order.update({
      where: { id },
      data: {
        checkInStatus: 'CHECKED_IN',
        checkInTime: new Date()
      }
    });

    return NextResponse.json({
      message: 'Attendee checked in successfully',
      checkInStatus: updatedOrder.checkInStatus,
      checkInTime: updatedOrder.checkInTime
    });
  } catch (error) {
    console.error('Error checking in attendee:', error);
    return NextResponse.json(
      { error: 'Failed to check in attendee' },
      { status: 500 }
    );
  }
}

// DELETE: Cancel check-in
export async function DELETE(request: NextRequest, props: { params: Promise<{ id: string }> }) {
  try {
    const user = await currentUser();
    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const params = await props.params;
    const { id  } = await params;

    // Get events for this organizer
    const events = await db.event.findMany({
      where: { userId: user.id },
      select: { id: true }
    });
    
    const eventIds = events.map(event => event.id);

    // Fetch the order (attendee)
    const order = await db.order.findUnique({
      where: { id },
      include: {
        event: {
          select: {
            id: true,
            userId: true
          }
        }
      }
    });

    if (!order) {
      return NextResponse.json({ error: 'Attendee not found' }, { status: 404 });
    }

    // Check if the order belongs to an event organized by the current user
    if (!eventIds.includes(order.event.id)) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Update check-in status
    const updatedOrder = await db.order.update({
      where: { id },
      data: {
        checkInStatus: 'NOT_CHECKED_IN',
        checkInTime: null
      }
    });

    return NextResponse.json({
      message: 'Check-in cancelled successfully',
      checkInStatus: updatedOrder.checkInStatus
    });
  } catch (error) {
    console.error('Error cancelling check-in:', error);
    return NextResponse.json(
      { error: 'Failed to cancel check-in' },
      { status: 500 }
    );
  }
}
