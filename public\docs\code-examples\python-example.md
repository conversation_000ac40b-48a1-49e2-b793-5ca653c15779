# Python API Examples

This guide provides examples of how to use the QuickTime Events API with Python.

## Prerequisites

- Python 3.6 or higher
- `requests` library (install with `pip install requests`)

## Authentication

```python
import requests

API_KEY = "your-api-key"
BASE_URL = "https://your-domain.com/api"

headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}
```

## Get Published Events

```python
import requests

API_KEY = "your-api-key"
BASE_URL = "https://your-domain.com/api"

headers = {
    "X-API-Key": API_KEY
}

def get_published_events():
    response = requests.get(f"{BASE_URL}/events/published", headers=headers)
    
    if response.status_code == 200:
        events = response.json()
        return events
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None

events = get_published_events()
if events:
    for event in events:
        print(f"Event: {event['title']}")
        print(f"Date: {event['startDate']}")
        print(f"Location: {event['location']}")
        print("---")
```

## Create an Event

```python
import requests
import json
from datetime import datetime, timedelta

API_KEY = "your-api-key"
BASE_URL = "https://your-domain.com/api"

headers = {
    "X-API-Key": API_KEY,
    "Content-Type": "application/json"
}

def create_event(event_data):
    response = requests.post(
        f"{BASE_URL}/events/create", 
        headers=headers,
        json=event_data
    )
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None

# Create an event starting tomorrow
tomorrow = datetime.now() + timedelta(days=1)
day_after = tomorrow + timedelta(days=1)

event_data = {
    "title": "Python Meetup",
    "description": "A meetup for Python enthusiasts",
    "startDate": tomorrow.isoformat(),
    "endDate": day_after.isoformat(),
    "location": "San Francisco",
    "venue": "Tech Hub",
    "category": "TECHNOLOGY",
    "eventType": "WORKSHOP"
}

new_event = create_event(event_data)
if new_event:
    print(f"Event created with ID: {new_event['id']}")
```

## Get Event Details

```python
import requests

API_KEY = "your-api-key"
BASE_URL = "https://your-domain.com/api"

headers = {
    "X-API-Key": API_KEY
}

def get_event_details(event_id):
    response = requests.get(f"{BASE_URL}/eventdetails/{event_id}", headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
        return None

event_id = "event-id-here"
event = get_event_details(event_id)
if event:
    print(f"Event: {event['title']}")
    print(f"Description: {event['description']}")
    print(f"Date: {event['startDate']} to {event['endDate']}")
    print(f"Location: {event['location']}, {event['venue']}")
    print(f"Category: {event['category']}")
    print(f"Type: {event['eventType']}")
```

## Handling Rate Limits

```python
import requests
import time

API_KEY = "your-api-key"
BASE_URL = "https://your-domain.com/api"

headers = {
    "X-API-Key": API_KEY
}

def make_request_with_rate_limit_handling(url, method="GET", data=None):
    max_retries = 5
    retries = 0
    
    while retries < max_retries:
        try:
            if method == "GET":
                response = requests.get(url, headers=headers)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            # Check for rate limit headers
            limit = response.headers.get("X-RateLimit-Limit")
            remaining = response.headers.get("X-RateLimit-Remaining")
            reset = response.headers.get("X-RateLimit-Reset")
            
            if limit and remaining:
                print(f"Rate limit: {remaining}/{limit} requests remaining")
            
            # If rate limited, wait and retry
            if response.status_code == 429:
                retry_after = int(response.headers.get("Retry-After", 60))
                print(f"Rate limited. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
                retries += 1
                continue
            
            return response
        
        except Exception as e:
            print(f"Error: {e}")
            retries += 1
            wait_time = 2 ** retries  # Exponential backoff
            print(f"Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
    
    raise Exception("Max retries exceeded")

# Example usage
try:
    response = make_request_with_rate_limit_handling(f"{BASE_URL}/events/published")
    if response.status_code == 200:
        events = response.json()
        print(f"Found {len(events)} events")
    else:
        print(f"Error: {response.status_code}")
        print(response.text)
except Exception as e:
    print(f"Failed to get events: {e}")
```

## Complete Example: Event Manager

```python
import requests
import time
import json
from datetime import datetime, timedelta

class EventAPIClient:
    def __init__(self, api_key, base_url="https://your-domain.com/api"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }
    
    def _make_request(self, endpoint, method="GET", data=None, params=None):
        url = f"{self.base_url}/{endpoint}"
        max_retries = 5
        retries = 0
        
        while retries < max_retries:
            try:
                if method == "GET":
                    response = requests.get(url, headers=self.headers, params=params)
                elif method == "POST":
                    response = requests.post(url, headers=self.headers, json=data)
                elif method == "PUT":
                    response = requests.put(url, headers=self.headers, json=data)
                elif method == "DELETE":
                    response = requests.delete(url, headers=self.headers)
                else:
                    raise ValueError(f"Unsupported method: {method}")
                
                # Check for rate limit headers
                limit = response.headers.get("X-RateLimit-Limit")
                remaining = response.headers.get("X-RateLimit-Remaining")
                
                if limit and remaining:
                    print(f"Rate limit: {remaining}/{limit} requests remaining")
                
                # If rate limited, wait and retry
                if response.status_code == 429:
                    retry_after = int(response.headers.get("Retry-After", 60))
                    print(f"Rate limited. Waiting {retry_after} seconds...")
                    time.sleep(retry_after)
                    retries += 1
                    continue
                
                return response
            
            except Exception as e:
                print(f"Error: {e}")
                retries += 1
                wait_time = 2 ** retries  # Exponential backoff
                print(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
        
        raise Exception("Max retries exceeded")
    
    def get_published_events(self, page=1, limit=10):
        params = {
            "page": page,
            "limit": limit
        }
        response = self._make_request("events/published", params=params)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return None
    
    def get_event_details(self, event_id):
        response = self._make_request(f"eventdetails/{event_id}")
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return None
    
    def create_event(self, event_data):
        response = self._make_request("events/create", method="POST", data=event_data)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return None
    
    def update_event(self, event_id, event_data):
        response = self._make_request(f"events/{event_id}", method="PUT", data=event_data)
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return None
    
    def delete_event(self, event_id):
        response = self._make_request(f"events/{event_id}", method="DELETE")
        
        if response.status_code == 200:
            return True
        else:
            print(f"Error: {response.status_code}")
            print(response.text)
            return False

# Example usage
if __name__ == "__main__":
    client = EventAPIClient("your-api-key")
    
    # Get published events
    events = client.get_published_events()
    if events:
        print(f"Found {len(events)} events")
        for event in events[:3]:  # Show first 3 events
            print(f"- {event['title']} ({event['startDate']})")
    
    # Create a new event
    tomorrow = datetime.now() + timedelta(days=1)
    day_after = tomorrow + timedelta(days=1)
    
    new_event_data = {
        "title": "Python API Workshop",
        "description": "Learn how to use APIs with Python",
        "startDate": tomorrow.isoformat(),
        "endDate": day_after.isoformat(),
        "location": "Online",
        "venue": "Zoom",
        "category": "TECHNOLOGY",
        "eventType": "WORKSHOP"
    }
    
    new_event = client.create_event(new_event_data)
    if new_event:
        print(f"Created new event: {new_event['title']} (ID: {new_event['id']})")
        
        # Get the event details
        event_details = client.get_event_details(new_event['id'])
        if event_details:
            print(f"Event details: {json.dumps(event_details, indent=2)}")
        
        # Update the event
        update_data = {
            "title": "Updated: Python API Workshop",
            "description": "Updated description: Learn how to use APIs with Python"
        }
        updated_event = client.update_event(new_event['id'], update_data)
        if updated_event:
            print(f"Updated event title to: {updated_event['title']}")
        
        # Delete the event
        if client.delete_event(new_event['id']):
            print(f"Deleted event with ID: {new_event['id']}")
```
