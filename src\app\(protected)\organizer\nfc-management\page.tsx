'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { Loader2, CreditCard, Tag, Watch, Search, Download, Upload, BarChart3, Settings, AlertTriangle, Info } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';

// Types
interface NFCDevice {
  id: string;
  type: string;
  status: string;
  balance: number;
  lastUsed: string | null;
  metadata: any;
  userId: string;
}

interface NFCTransaction {
  id: string;
  deviceId: string;
  amount: number;
  type: string;
  description: string;
  timestamp: string;
  status: string;
}

interface Event {
  id: string;
  title: string;
  startDate: string;
  endDate: string;
  location: string;
}

export default function OrganizerNFCManagementPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [events, setEvents] = useState<Event[]>([]);
  const [selectedEventId, setSelectedEventId] = useState<string>('');
  const [devices, setDevices] = useState<NFCDevice[]>([]);
  const [transactions, setTransactions] = useState<NFCTransaction[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSettingsDialogOpen, setIsSettingsDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);

  // Redirect if not organizer
  useEffect(() => {
    if (status === 'authenticated' && session?.user?.role !== 'ORGANIZER') {
      router.push('/dashboard');
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
    }
  }, [status, session, router]);

  // Fetch organizer events
  useEffect(() => {
    const fetchEvents = async () => {
      if (status !== 'authenticated' || session?.user?.role !== 'ORGANIZER') return;

      setIsLoading(true);

      try {
        const response = await fetch('/api/organizer/events');

        if (response.ok) {
          const data = await response.json();
          setEvents(data);

          // Select the first event by default if available
          if (data.length > 0 && !selectedEventId) {
            setSelectedEventId(data[0].id);
          }
        } else {
          throw new Error('Failed to fetch events');
        }
      } catch (error) {
        console.error('Error fetching events:', error);
        toast({
          title: 'Error',
          description: 'Failed to load events',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchEvents();
  }, [status, session]);

  // Fetch NFC devices for selected event
  useEffect(() => {
    const fetchDevices = async () => {
      if (!selectedEventId) return;

      setIsLoading(true);

      try {
        const response = await fetch(`/api/organizer/events/${selectedEventId}/nfc-devices`);

        if (response.ok) {
          const data = await response.json();
          setDevices(data);
        } else {
          throw new Error('Failed to fetch NFC devices');
        }
      } catch (error) {
        console.error('Error fetching NFC devices:', error);
        toast({
          title: 'Error',
          description: 'Failed to load NFC devices',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (selectedEventId) {
      fetchDevices();
    }
  }, [selectedEventId]);

  // Fetch NFC transactions for selected event
  useEffect(() => {
    const fetchTransactions = async () => {
      if (!selectedEventId) return;

      setIsLoading(true);

      try {
        const response = await fetch(`/api/organizer/events/${selectedEventId}/nfc-transactions`);

        if (response.ok) {
          const data = await response.json();
          setTransactions(data);
        } else {
          throw new Error('Failed to fetch NFC transactions');
        }
      } catch (error) {
        console.error('Error fetching NFC transactions:', error);
        toast({
          title: 'Error',
          description: 'Failed to load NFC transactions',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (selectedEventId) {
      fetchTransactions();
    }
  }, [selectedEventId]);

  // Get icon for device type
  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'CARD':
        return <CreditCard className="h-5 w-5" />;
      case 'FABRIC_WRISTBAND':
      case 'PAPER_WRISTBAND':
      case 'SILICONE_WRISTBAND':
        return <Watch className="h-5 w-5" />;
      case 'TAG':
        return <Tag className="h-5 w-5" />;
      default:
        return <CreditCard className="h-5 w-5" />;
    }
  };

  // Format device type for display
  const formatDeviceType = (type: string) => {
    return type
      .replace('_', ' ')
      .split('_')
      .map(word => word.charAt(0) + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';

    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Filter devices by search query
  const filteredDevices = devices.filter(device => {
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      device.id.toLowerCase().includes(query) ||
      device.type.toLowerCase().includes(query) ||
      device.status.toLowerCase().includes(query) ||
      (device.metadata?.color && device.metadata.color.toLowerCase().includes(query))
    );
  });

  // Loading state
  if (status === 'loading' || (isLoading && !devices.length)) {
    return (
      <div className="container mx-auto py-10 max-w-6xl">
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="ml-2">Loading...</span>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    router.push('/auth/login?callbackUrl=/organizer/nfc-management');
    return null;
  }

  if (session?.user?.role !== 'ORGANIZER') {
    return null; // Already redirecting in useEffect
  }

  return (
    <div className="container mx-auto py-10 max-w-6xl">
      <div className="flex justify-between items-center mb-6 bg-gradient-to-r from-orange-50 to-blue-50 p-4 rounded-lg">
        <div>
          <h1 className="text-2xl font-bold text-blue-700">NFC Management</h1>
          <p className="text-gray-600">Manage NFC devices and transactions for your events</p>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedEventId} onValueChange={setSelectedEventId}>
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder="Select an event" />
            </SelectTrigger>
            <SelectContent>
              {events.map(event => (
                <SelectItem key={event.id} value={event.id}>
                  {event.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsSettingsDialogOpen(true)}
            className="border-orange-500 text-orange-600 hover:bg-orange-50"
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {!selectedEventId ? (
        <Card className="border-t-4 border-t-orange-500 border-b-4 border-b-blue-500">
          <CardContent className="flex flex-col items-center justify-center py-12 bg-gradient-to-r from-orange-50 to-blue-50">
            <AlertTriangle className="h-12 w-12 text-orange-500 mb-4" />
            <h3 className="text-xl font-medium text-blue-700">No Event Selected</h3>
            <p className="text-gray-600 mt-2 text-center">
              Please select an event to manage NFC devices and transactions.
            </p>
          </CardContent>
        </Card>
      ) : (
        <Tabs defaultValue="devices">
          <TabsList className="grid w-full grid-cols-3 mb-8 bg-gray-100">
            <TabsTrigger value="devices" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Devices</TabsTrigger>
            <TabsTrigger value="transactions" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Transactions</TabsTrigger>
            <TabsTrigger value="analytics" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="devices">
            <Card className="border-t-4 border-t-orange-500">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-blue-50">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-blue-700">NFC Devices</CardTitle>
                    <CardDescription>
                      Manage NFC devices for your event
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="relative">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                      <Input
                        placeholder="Search devices..."
                        className="pl-8 w-[250px]"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsImportDialogOpen(true)}
                      className="border-orange-500 text-orange-600 hover:bg-orange-50"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Import
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-blue-500 text-blue-600 hover:bg-blue-50"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {filteredDevices.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <Info className="h-12 w-12 text-blue-600 mb-4" />
                    <h3 className="text-xl font-medium text-gray-700">No NFC Devices Found</h3>
                    <p className="text-gray-500 mt-2 text-center">
                      {searchQuery ? 'No devices match your search criteria.' : 'There are no NFC devices associated with this event yet.'}
                    </p>
                    {!searchQuery && (
                      <Button
                        className="mt-4 bg-blue-600 hover:bg-blue-700"
                        onClick={() => setIsImportDialogOpen(true)}
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        Import Devices
                      </Button>
                    )}
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="w-[50px]"></TableHead>
                        <TableHead>Device ID</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Balance</TableHead>
                        <TableHead>Last Used</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredDevices.map((device) => (
                        <TableRow key={device.id}>
                          <TableCell>
                            {getDeviceIcon(device.type)}
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {device.id.substring(0, 8)}...
                          </TableCell>
                          <TableCell>
                            {formatDeviceType(device.type)}
                            {device.metadata?.color && (
                              <Badge variant="outline" className="ml-2">
                                {device.metadata.color}
                              </Badge>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                device.status === 'ACTIVE'
                                  ? 'bg-green-100 text-green-800'
                                  : device.status === 'DEACTIVATED'
                                  ? 'bg-gray-100 text-gray-800'
                                  : device.status === 'LOST'
                                  ? 'bg-red-100 text-red-800'
                                  : 'bg-orange-100 text-orange-800'
                              }
                            >
                              {device.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            {formatCurrency(device.balance)}
                          </TableCell>
                          <TableCell>
                            {formatDate(device.lastUsed)}
                          </TableCell>
                          <TableCell className="text-right">
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="transactions">
            <Card className="border-t-4 border-t-blue-500">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-orange-50">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="text-orange-600">NFC Transactions</CardTitle>
                    <CardDescription>
                      View all NFC transactions for your event
                    </CardDescription>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-blue-500 text-blue-600 hover:bg-blue-50"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {transactions.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-8">
                    <Info className="h-12 w-12 text-blue-600 mb-4" />
                    <h3 className="text-xl font-medium text-gray-700">No Transactions Found</h3>
                    <p className="text-gray-500 mt-2 text-center">
                      There are no NFC transactions for this event yet.
                    </p>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Transaction ID</TableHead>
                        <TableHead>Device ID</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead className="text-right">Amount</TableHead>
                        <TableHead>Timestamp</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {transactions.map((transaction) => (
                        <TableRow key={transaction.id}>
                          <TableCell className="font-mono text-sm">
                            {transaction.id.substring(0, 8)}...
                          </TableCell>
                          <TableCell className="font-mono text-sm">
                            {transaction.deviceId.substring(0, 8)}...
                          </TableCell>
                          <TableCell>
                            {transaction.type}
                          </TableCell>
                          <TableCell>
                            {transaction.description}
                          </TableCell>
                          <TableCell className="text-right">
                            <span className={transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}>
                              {formatCurrency(transaction.amount)}
                            </span>
                          </TableCell>
                          <TableCell>
                            {formatDate(transaction.timestamp)}
                          </TableCell>
                          <TableCell>
                            <Badge
                              className={
                                transaction.status === 'COMPLETED'
                                  ? 'bg-green-100 text-green-800'
                                  : transaction.status === 'PENDING'
                                  ? 'bg-orange-100 text-orange-800'
                                  : 'bg-red-100 text-red-800'
                              }
                            >
                              {transaction.status}
                            </Badge>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics">
            <Card className="border-t-4 border-t-orange-500 border-b-4 border-b-blue-500">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-blue-50">
                <CardTitle className="text-blue-700">NFC Analytics</CardTitle>
                <CardDescription>
                  View analytics and insights for NFC usage at your event
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card className="border-l-4 border-l-orange-500">
                    <CardHeader className="pb-2 bg-orange-50">
                      <CardTitle className="text-sm font-medium text-orange-700">Total Devices</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">{devices.length}</div>
                    </CardContent>
                  </Card>

                  <Card className="border-t-4 border-t-blue-500">
                    <CardHeader className="pb-2 bg-blue-50">
                      <CardTitle className="text-sm font-medium text-blue-700">Total Transactions</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-blue-600">{transactions.length}</div>
                    </CardContent>
                  </Card>

                  <Card className="border-r-4 border-r-orange-500">
                    <CardHeader className="pb-2 bg-gradient-to-r from-blue-50 to-orange-50">
                      <CardTitle className="text-sm font-medium text-blue-700">Total Volume</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="text-2xl font-bold text-orange-600">
                        {formatCurrency(
                          transactions.reduce((sum, transaction) => sum + Math.abs(transaction.amount), 0)
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                <div className="mt-8 flex items-center justify-center h-64 bg-gradient-to-r from-blue-50 to-orange-50 rounded-lg border border-blue-200 border-dashed">
                  <div className="flex flex-col items-center">
                    <BarChart3 className="h-12 w-12 text-blue-500 mb-4" />
                    <p className="text-blue-700 font-medium">Analytics charts will be displayed here</p>
                    <p className="text-orange-600 text-sm mt-2">Visualize your NFC usage data</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      {/* Settings Dialog */}
      <Dialog open={isSettingsDialogOpen} onOpenChange={setIsSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[500px] border-t-4 border-t-blue-500 border-b-4 border-b-orange-500">
          <DialogHeader className="bg-gradient-to-r from-blue-50 to-orange-50 rounded-t-lg pb-4">
            <DialogTitle className="text-blue-700 text-xl">NFC Settings</DialogTitle>
            <DialogDescription>
              Configure NFC settings for your event
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="enable-nfc">Enable NFC Payments</Label>
              <Switch id="enable-nfc" defaultChecked className="data-[state=checked]:bg-blue-600" />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="offline-mode">Offline Mode</Label>
              <Switch id="offline-mode" className="data-[state=checked]:bg-orange-500" />
            </div>

            <div className="flex items-center justify-between">
              <Label htmlFor="auto-sync">Auto Sync</Label>
              <Switch id="auto-sync" defaultChecked className="data-[state=checked]:bg-blue-600" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="max-transaction">Maximum Transaction Amount</Label>
              <Input id="max-transaction" type="number" defaultValue="500" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sync-interval">Sync Interval (minutes)</Label>
              <Input id="sync-interval" type="number" defaultValue="15" />
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-blue-50 to-orange-50 rounded-b-lg pt-4">
            <Button
              variant="outline"
              onClick={() => setIsSettingsDialogOpen(false)}
              className="border-orange-500 text-orange-600 hover:bg-orange-50"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                toast({
                  title: 'Settings Saved',
                  description: 'Your NFC settings have been saved successfully',
                });
                setIsSettingsDialogOpen(false);
              }}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Import Dialog */}
      <Dialog open={isImportDialogOpen} onOpenChange={setIsImportDialogOpen}>
        <DialogContent className="sm:max-w-[500px] border-t-4 border-t-orange-500 border-b-4 border-b-blue-500">
          <DialogHeader className="bg-gradient-to-r from-orange-50 to-blue-50 rounded-t-lg pb-4">
            <DialogTitle className="text-orange-600 text-xl">Import NFC Devices</DialogTitle>
            <DialogDescription>
              Import NFC devices for your event
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="import-file">Upload CSV File</Label>
              <Input id="import-file" type="file" accept=".csv" />
              <p className="text-sm text-gray-500">
                CSV file should contain columns: device_id, type, status, balance
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="import-type">Import Type</Label>
              <Select defaultValue="add">
                <SelectTrigger id="import-type">
                  <SelectValue placeholder="Select import type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="add">Add new devices only</SelectItem>
                  <SelectItem value="update">Update existing devices</SelectItem>
                  <SelectItem value="replace">Replace all devices</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="bg-gradient-to-r from-orange-50 to-blue-50 rounded-b-lg pt-4">
            <Button
              variant="outline"
              onClick={() => setIsImportDialogOpen(false)}
              className="border-blue-500 text-blue-600 hover:bg-blue-50"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                toast({
                  title: 'Import Started',
                  description: 'Your NFC devices are being imported',
                });
                setIsImportDialogOpen(false);
              }}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Import Devices
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
