// app/events/[id]/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import Image from 'next/image'; // Add Image import
import {
  Calendar, MapPin, Users, Clock, Tag,
  Share2, Edit, Trash2, ArrowLeft, AlertTriangle,
  Car, UserCheck, Send, CheckCircle, Loader2, Star, DollarSign, LayoutGrid, User, Ticket
} from 'lucide-react';
import Link from 'next/link';
import { SimpleFeatureButton } from '@/components/organizer/simple-feature-button';

// Types
interface Event {
  id: string;
  title: string;
  description: string;
  status: string;
  startDate: string;
  endDate: string;
  startTime: string;
  endTime: string;
  category: string;
  eventType: string;
  location: string;
  venue?: string;
  virtualPlatform?: string;
  virtualLink?: string;
  imagePath?: string;
  isFree?: boolean;
  regularPrice?: number;
  vipPrice?: number;
  regularSeats?: number;
  vipSeats?: number;
  vvipSeats?: number;
  venueCapacity?: number;
  userId: string;
  user: {
    id: string;
    name: string;
    email?: string;
  };
  ageRestriction?: {
    id?: string;
    minAge?: number;
    maxAge?: number;
    ageGroups?: string;
    description?: string;
  } | null;
  ParkingManagement?: {
    id?: string;
    totalSpaces?: number;
    reservedSpaces?: number;
    pricePerHour?: number;
    isFree?: boolean;
    reservationRequired?: boolean;
    description?: string;
  } | null;
  seoSettings?: {
    id?: string;
    title?: string;
    description?: string;
    keywords?: string[];
  } | null;
  socialSettings?: {
    id?: string;
    facebookTitle?: string;
    facebookDescription?: string;
    twitterTitle?: string;
    twitterDescription?: string;
    ogImage?: string;
  } | null;
  tickets?: any[];
  createdAt: string;
  updatedAt?: string;
}

export default function EventDetailPage() {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const params = useParams();
  const router = useRouter();
  const { data: session } = useSession();

  const isOrganizer = session?.user?.role === 'ORGANIZER';
  const isOwner = event && session?.user?.id === event.userId;

  // Fetch event details
  useEffect(() => {
    const fetchEvent = async () => {
      try {
        // Use the dashboard API endpoint to get all event details
        const response = await fetch(`/api/dashboard/organiser/events/${params?.id}`, {
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Event not found');
          }
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        console.log('Event data:', data);
        console.log('Age restriction:', data.ageRestriction);
        console.log('Parking management:', data.ParkingManagement);
        setEvent(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch event details');
      } finally {
        setLoading(false);
      }
    };

    if (params?.id) {
      fetchEvent();
    }
  }, [params?.id]);

  // Handle delete event
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      const response = await fetch(`/api/dashboard/organiser/events/delete?eventId=${event?.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Error: ${response.status}`);
      }

      router.push('/dashboard/organizer/events/myEvents');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete event');
    }
  };

  // Handle publish event
  const handlePublish = async () => {
    if (!event) return;

    if (!confirm('Are you sure you want to publish this event? It will be visible to the public immediately.')) return;

    try {
      setIsSubmitting(true);
      setError(null);
      setSubmitSuccess(false);

      console.log('Publishing event with ID:', event.id);
      const response = await fetch(`/api/eventdetails/${event.id}`, {
        method: 'PUT',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'Published' })
      });

      console.log('Response status:', response.status);

      // Get the response as text first
      const responseText = await response.text();
      console.log('Response text length:', responseText.length);

      // Check if it looks like HTML (contains <!DOCTYPE or <html)
      if (responseText.includes('<!DOCTYPE') || responseText.includes('<html')) {
        console.error('Received HTML response instead of JSON');
        throw new Error(`Received HTML instead of JSON. Status: ${response.status}. Please check the server logs.`);
      }

      // Try to parse as JSON
      let data;
      try {
        data = JSON.parse(responseText);
        console.log('Parsed JSON data:', data);
      } catch (parseError) {
        console.error('Failed to parse response as JSON:', parseError);
        throw new Error(`Invalid JSON response. Status: ${response.status}. Response starts with: ${responseText.substring(0, 100)}...`);
      }

      if (!response.ok) {
        const errorMessage = data.error || data.details || `Error: ${response.status}`;
        throw new Error(errorMessage);
      }

      // Update the local event state with the new status from the response
      const newStatus = data.event?.status || 'UnderReview';
      console.log('Setting event status to:', newStatus);

      setEvent(prev => prev ? { ...prev, status: newStatus } : null);
      setSubmitSuccess(true);

      // Show success message for 3 seconds, then refresh the page
      setTimeout(() => {
        console.log('Refreshing page...');
        router.refresh();
      }, 3000);

    } catch (err) {
      console.error('Error submitting event:', err);
      setError(err instanceof Error ? err.message : 'Failed to submit event for review');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Event status badge component
  const StatusBadge = ({ status }: { status: string }) => {
    const getStatusClasses = () => {
      switch (status) {
        case 'Published':
          return 'bg-green-100 text-green-800';
        case 'Draft':
          return 'bg-yellow-100 text-yellow-800';
        case 'Cancelled':
          return 'bg-red-100 text-red-800';
        case 'Pending':
          return 'bg-blue-100 text-blue-800';
        case 'UnderReview':
          return 'bg-purple-100 text-purple-800';
        case 'Approved':
          return 'bg-emerald-100 text-emerald-800';
        case 'Rejected':
          return 'bg-pink-100 text-pink-800';
        case 'Completed':
          return 'bg-gray-100 text-gray-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    // Format status for display (add spaces between camelCase words)
    const formatStatus = (status: string) => {
      return status
        .replace(/([A-Z])/g, ' $1') // Add space before capital letters
        .replace(/^./, (str) => str.toUpperCase()); // Capitalize first letter
    };

    return (
      <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusClasses()}`}>
        {formatStatus(status)}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-16 flex justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error || !event) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-2xl font-bold text-red-700 mb-2">
            {error || 'Event not found'}
          </h2>
          <p className="text-red-600 mb-6">
            We couldn&apos;t find the event you&apos;re looking for.
          </p>
          <Link href="/events" className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  // Block access to draft events for non-owners
  if (event.status === 'Draft' && !isOwner) {
    return (
      <div className="container mx-auto px-4 py-16">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
          <AlertTriangle className="mx-auto h-12 w-12 text-yellow-500 mb-4" />
          <h2 className="text-2xl font-bold text-yellow-700 mb-2">
            Access Restricted
          </h2>
          <p className="text-yellow-600 mb-6">
            This event is currently in draft mode and is only viewable by the organizer.
          </p>
          <Link href="/events" className="inline-flex items-center bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            <ArrowLeft className="mr-2 h-5 w-5" />
            Back to Events
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Back navigation */}
          <div className="mb-6">
            <Link
              href="/dashboard/organizer/events/myEvents"
              className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors duration-200 group"
            >
              <ArrowLeft className="mr-2 h-5 w-5 group-hover:-translate-x-1 transition-transform duration-200" />
              <span className="font-medium">Back to My Events</span>
            </Link>
          </div>

          {/* Success message */}
          {submitSuccess && (
            <div className="bg-emerald-50 border border-emerald-200 text-emerald-800 px-6 py-4 rounded-xl mb-6 flex items-center shadow-sm">
              <CheckCircle className="h-5 w-5 mr-3 text-emerald-600 flex-shrink-0" />
              <p className="font-medium">Your event has been successfully submitted for review. You will be notified once it is approved or rejected.</p>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-800 px-6 py-4 rounded-xl mb-6 flex items-center shadow-sm">
              <AlertTriangle className="h-5 w-5 mr-3 text-red-600 flex-shrink-0" />
              <p className="font-medium">{error}</p>
            </div>
          )}

          {/* Event header */}
          <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-3">
                <h1 className="text-4xl font-bold text-gray-900 leading-tight">{event.title}</h1>
                <StatusBadge status={event.status} />
              </div>
              <div className="flex items-center gap-2 text-gray-600">
                <User className="h-4 w-4" />
                <span className="font-medium">Organized by {event.user.name}</span>
              </div>
            </div>

            {/* Actions */}
            {isOwner && (
              <div className="flex flex-wrap gap-3 lg:flex-nowrap">
                {/* Show Edit button only for Draft status */}
                {event.status === 'Draft' && (
                  <Link
                    href={`/dashboard/organizer/events/edit/${event.id}`}
                    className="inline-flex items-center bg-amber-500 text-white px-6 py-3 rounded-xl hover:bg-amber-600 transition-colors duration-200 font-medium shadow-sm"
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Event
                  </Link>
                )}

                {/* Show Publish button only for Draft status */}
                {event.status === 'Draft' && (
                  <button
                    onClick={handlePublish}
                    disabled={isSubmitting}
                    className="inline-flex items-center bg-green-600 text-white px-6 py-3 rounded-xl hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200 font-medium shadow-sm"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Publishing...
                      </>
                    ) : (
                      <>
                        <Send className="mr-2 h-4 w-4" />
                        Publish Event
                      </>
                    )}
                  </button>
                )}

                {/* Show status for suspended events */}
                {event.status === 'Suspended' && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4">
                    <div className="flex items-center">
                      <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                      <span className="text-red-700 font-medium">This event has been suspended by an administrator</span>
                    </div>
                  </div>
                )}

                {/* Feature button - only for published events */}
                {event.status === 'Published' && (
                  <SimpleFeatureButton
                    eventId={event.id}
                    className="inline-flex items-center bg-yellow-500 text-white px-6 py-3 rounded-xl hover:bg-yellow-600 transition-colors duration-200 font-medium shadow-sm"
                  />
                )}

                {/* Delete button */}
                <button
                  onClick={handleDelete}
                  className="inline-flex items-center bg-red-500 text-white px-6 py-3 rounded-xl hover:bg-red-600 transition-colors duration-200 font-medium shadow-sm"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
          {/* Event details */}
          <div className="lg:col-span-2 space-y-12">
            {/* Event image */}
            {event.imagePath ? (
              <div className="relative w-full h-96 rounded-3xl overflow-hidden shadow-xl">
                <Image
                  src={event.imagePath}
                  alt={event.title}
                  fill
                  sizes="(max-width: 768px) 100vw, 66vw"
                  priority
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent" />
              </div>
            ) : (
              <div className="w-full h-96 bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl flex items-center justify-center shadow-xl border border-gray-200">
                <div className="text-center">
                  <Calendar className="h-20 w-20 text-gray-400 mx-auto mb-6" />
                  <p className="text-gray-500 font-medium text-lg">No image uploaded</p>
                </div>
              </div>
            )}

            {/* Main Event Information */}
            <div className="bg-white rounded-3xl shadow-sm border border-gray-200 p-10">
              {/* Description */}
              <div className="mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-8 tracking-tight">About This Event</h2>
                <div className="prose prose-gray max-w-none">
                  <p className="whitespace-pre-line text-gray-700 leading-7 text-lg">
                    {event.description}
                  </p>
                </div>
              </div>

              {/* Event Requirements Section */}
              <div className="border-t border-gray-200 pt-12">
                <h3 className="text-2xl font-bold text-gray-900 mb-8 tracking-tight">Event Requirements & Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
                  {/* Age Requirements */}
                  <div>
                    <div className="flex items-center mb-6">
                      <div className="p-3 bg-indigo-50 rounded-xl mr-4">
                        <UserCheck className="h-6 w-6 text-indigo-600" />
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 tracking-tight">Age Requirements</h4>
                    </div>
                    <div className="space-y-4 ml-16">
                      {event.ageRestriction?.minAge ? (
                        <div className="flex items-center justify-between py-2">
                          <span className="text-gray-700 font-medium">Minimum age</span>
                          <span className="font-bold text-gray-900">{event.ageRestriction?.minAge} years</span>
                        </div>
                      ) : (
                        <div className="flex items-center text-emerald-700 py-2">
                          <CheckCircle className="h-5 w-5 mr-3" />
                          <span className="font-medium">No minimum age requirement</span>
                        </div>
                      )}

                      {event.ageRestriction?.maxAge !== undefined && event.ageRestriction.maxAge > 0 && (
                        <div className="flex items-center justify-between py-2">
                          <span className="text-gray-700 font-medium">Maximum age</span>
                          <span className="font-bold text-gray-900">{event.ageRestriction?.maxAge} years</span>
                        </div>
                      )}

                      {event.ageRestriction?.ageGroups && (
                        <div className="py-2">
                          <span className="text-gray-700 font-medium">Age groups: </span>
                          <span className="font-bold text-gray-900">{event.ageRestriction?.ageGroups}</span>
                        </div>
                      )}

                      {event.ageRestriction?.description && (
                        <div className="py-3 px-4 bg-gray-50 rounded-xl">
                          <p className="text-gray-600 text-sm leading-6 italic">{event.ageRestriction?.description}</p>
                        </div>
                      )}

                      {event.ageRestriction?.minAge !== undefined && event.ageRestriction.minAge > 0 && (
                        <div className="flex items-center text-amber-700 py-2">
                          <AlertTriangle className="h-5 w-5 mr-3" />
                          <span className="font-medium">ID verification may be required</span>
                        </div>
                      )}

                      {(!event.ageRestriction?.minAge || event.ageRestriction?.minAge <= 0) &&
                       (!event.ageRestriction?.maxAge || event.ageRestriction?.maxAge <= 0) &&
                       (!event.ageRestriction?.ageGroups || event.ageRestriction?.ageGroups === '') &&
                       (!event.ageRestriction?.description || event.ageRestriction?.description === '') && (
                        <div className="flex items-center text-emerald-700 py-2">
                          <CheckCircle className="h-5 w-5 mr-3" />
                          <span className="font-medium">No age restrictions</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Parking Information */}
                  <div>
                    <div className="flex items-center mb-6">
                      <div className="p-3 bg-indigo-50 rounded-xl mr-4">
                        <Car className="h-6 w-6 text-indigo-600" />
                      </div>
                      <h4 className="text-xl font-semibold text-gray-900 tracking-tight">Parking Information</h4>
                    </div>
                    <div className="space-y-4 ml-16">
                      {event.ParkingManagement ? (
                        <>
                          <div className="flex items-center">
                            {event.ParkingManagement?.totalSpaces !== undefined && event.ParkingManagement.totalSpaces > 0 ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                                <span className="text-green-700 font-medium">Parking available</span>
                              </>
                            ) : (
                              <>
                                <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                                <span className="text-red-700 font-medium">No parking available</span>
                              </>
                            )}
                          </div>

                          {event.ParkingManagement?.totalSpaces !== undefined && event.ParkingManagement.totalSpaces > 0 && (
                            <>
                              <div className="flex items-center justify-between">
                                <span className="text-gray-700">Total spaces</span>
                                <span className="font-semibold text-gray-900">{event.ParkingManagement?.totalSpaces}</span>
                              </div>

                              {event.ParkingManagement?.reservedSpaces !== undefined && event.ParkingManagement.reservedSpaces > 0 && (
                                <div className="flex items-center justify-between">
                                  <span className="text-gray-700">Reserved spaces</span>
                                  <span className="font-semibold text-gray-900">{event.ParkingManagement?.reservedSpaces}</span>
                                </div>
                              )}

                              <div className="flex items-center">
                                <DollarSign className={`h-4 w-4 mr-2 ${
                                  event.ParkingManagement?.isFree ? 'text-green-600' : 'text-amber-600'
                                }`} />
                                {event.ParkingManagement?.isFree ? (
                                  <span className="text-green-700 font-medium">Free parking</span>
                                ) : (
                                  <span className="text-amber-700">
                                    Paid parking
                                    {event.ParkingManagement?.pricePerHour && (
                                      <span className="font-semibold"> (${event.ParkingManagement?.pricePerHour}/hour)</span>
                                    )}
                                  </span>
                                )}
                              </div>

                              {event.ParkingManagement?.reservationRequired && (
                                <div className="flex items-center text-amber-700">
                                  <Clock className="h-4 w-4 mr-2" />
                                  <span>Reservation required</span>
                                </div>
                              )}
                            </>
                          )}

                          {event.ParkingManagement?.description && (
                            <p className="text-gray-600 text-sm italic">{event.ParkingManagement?.description}</p>
                          )}
                        </>
                      ) : (
                        <div className="flex items-center text-red-700">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          <span>No parking information available</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Marketing & SEO Settings */}
            <div className="bg-white rounded-3xl shadow-sm border border-gray-200 p-10">
              <h3 className="text-3xl font-bold text-gray-900 mb-10 tracking-tight">Marketing & SEO</h3>

              <div className="space-y-12">
                {/* SEO Section */}
                <div>
                  <div className="flex items-center mb-6">
                    <div className="p-3 bg-purple-50 rounded-xl mr-4">
                      <Star className="h-6 w-6 text-purple-600" />
                    </div>
                    <h4 className="text-xl font-semibold text-gray-900 tracking-tight">Search Engine Optimization</h4>
                  </div>
                  <div className="ml-16 space-y-6">
                    {event.seoSettings ? (
                      <>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <div className="text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide">SEO Title</div>
                          <div className="text-gray-900 font-medium leading-6">{event.seoSettings.title || event.title}</div>
                        </div>
                        <div className="p-4 bg-gray-50 rounded-xl">
                          <div className="text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide">SEO Description</div>
                          <div className="text-gray-900 leading-6">{event.seoSettings.description || 'No SEO description'}</div>
                        </div>
                        {event.seoSettings.keywords && event.seoSettings.keywords.length > 0 && (
                          <div className="p-4 bg-gray-50 rounded-xl">
                            <div className="text-sm font-semibold text-gray-600 mb-3 uppercase tracking-wide">Keywords</div>
                            <div className="flex flex-wrap gap-2">
                              {event.seoSettings.keywords.map((keyword, index) => (
                                <span key={index} className="bg-purple-100 text-purple-800 text-sm px-4 py-2 rounded-full font-medium">
                                  {keyword}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="flex items-center text-gray-500 p-4 bg-gray-50 rounded-xl">
                        <AlertTriangle className="h-5 w-5 mr-3" />
                        <span className="font-medium">No SEO settings configured</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Social Media Section */}
                <div className="border-t border-gray-200 pt-8">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-blue-50 rounded-lg mr-3">
                      <Share2 className="h-5 w-5 text-blue-600" />
                    </div>
                    <h4 className="text-lg font-semibold text-gray-900">Social Media</h4>
                  </div>
                  <div className="ml-12 space-y-6">
                    {event.socialSettings ? (
                      <>
                        <div>
                          <div className="flex items-center mb-3">
                            <div className="w-6 h-6 bg-blue-600 rounded-md flex items-center justify-center mr-2">
                              <span className="text-white text-xs font-bold">f</span>
                            </div>
                            <h5 className="font-semibold text-gray-900">Facebook</h5>
                          </div>
                          <div className="ml-8 space-y-2">
                            <div>
                              <span className="text-sm text-gray-500">Title: </span>
                              <span className="text-gray-900">{event.socialSettings.facebookTitle || event.title}</span>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500">Description: </span>
                              <span className="text-gray-900">{event.socialSettings.facebookDescription || 'No Facebook description'}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <div className="flex items-center mb-3">
                            <div className="w-6 h-6 bg-sky-500 rounded-md flex items-center justify-center mr-2">
                              <span className="text-white text-xs font-bold">𝕏</span>
                            </div>
                            <h5 className="font-semibold text-gray-900">Twitter / X</h5>
                          </div>
                          <div className="ml-8 space-y-2">
                            <div>
                              <span className="text-sm text-gray-500">Title: </span>
                              <span className="text-gray-900">{event.socialSettings.twitterTitle || event.title}</span>
                            </div>
                            <div>
                              <span className="text-sm text-gray-500">Description: </span>
                              <span className="text-gray-900">{event.socialSettings.twitterDescription || 'No Twitter description'}</span>
                            </div>
                          </div>
                        </div>

                        {event.socialSettings.ogImage && (
                          <div>
                            <div className="text-sm font-medium text-gray-500 mb-1">Open Graph Image</div>
                            <div className="text-gray-900 truncate">{event.socialSettings.ogImage}</div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="flex items-center text-gray-500">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        <span>No social media settings configured</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Ticket Information */}
            {event.tickets && event.tickets.length > 0 && (
              <div className="bg-white rounded-3xl shadow-sm border border-gray-200 p-10">
                <div className="flex items-center mb-8">
                  <div className="p-3 bg-emerald-50 rounded-xl mr-4">
                    <Ticket className="h-6 w-6 text-emerald-600" />
                  </div>
                  <h2 className="text-3xl font-bold text-gray-900 tracking-tight">Ticket Information</h2>
                </div>
                <div className="grid gap-6">
                  {event.tickets.map((ticket, index) => (
                    <div key={ticket.id || index} className="border border-gray-200 rounded-2xl p-8 hover:shadow-lg hover:border-gray-300 transition-all duration-300">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-3 tracking-tight">{ticket.name || ticket.type}</h3>
                          {ticket.description && (
                            <p className="text-gray-600 mb-4 leading-6">{ticket.description}</p>
                          )}
                          <div className="flex flex-wrap gap-6">
                            <div className="flex items-center">
                              <DollarSign className="h-5 w-5 text-emerald-600 mr-2" />
                              <span className="font-semibold text-gray-900">
                                {ticket.price ? `$${ticket.price.toFixed(2)}` : 'Free'}
                              </span>
                            </div>
                            <div className="flex items-center">
                              <Users className="h-5 w-5 text-indigo-600 mr-2" />
                              <span className="font-semibold text-gray-900">
                                {ticket.quantity} available
                              </span>
                            </div>
                          </div>
                        </div>
                        {ticket.specialGuestType && ticket.specialGuestName && ticket.specialGuestType !== 'None' && (
                          <div className="bg-purple-50 rounded-2xl p-6 min-w-0 md:min-w-[240px]">
                            <div className="flex items-center mb-2">
                              <Star className="h-5 w-5 text-purple-600 mr-2" />
                              <span className="font-semibold text-purple-700">Special Guest</span>
                            </div>
                            <div className="text-purple-900">
                              <span className="font-bold">{ticket.specialGuestType}:</span> {ticket.specialGuestName}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-3xl shadow-sm border border-gray-200 overflow-hidden sticky top-6">
              {/* Event Details */}
              <div className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-8 tracking-tight">Event Details</h3>

                <div className="space-y-8">
                  {/* Date & Time */}
                  <div className="flex items-start">
                    <div className="p-3 bg-indigo-50 rounded-xl mr-5 flex-shrink-0">
                      <Calendar className="h-6 w-6 text-indigo-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-bold text-gray-900 mb-2 text-lg">Date & Time</h4>
                      <p className="text-gray-900 font-semibold text-base">{formatDate(event.startDate)}</p>
                      <p className="text-gray-600 font-medium">
                        {formatTime(event.startDate)}
                        {event.endDate && ` - ${formatTime(event.endDate)}`}
                      </p>
                    </div>
                  </div>

                  {/* Location */}
                  <div className="flex items-start">
                    <div className="p-3 bg-rose-50 rounded-xl mr-5 flex-shrink-0">
                      <MapPin className="h-6 w-6 text-rose-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-bold text-gray-900 mb-2 text-lg">Location</h4>
                      <p className="text-gray-700 font-medium leading-6">{event.location}</p>
                    </div>
                  </div>

                  {/* Category */}
                  <div className="flex items-start">
                    <div className="p-3 bg-emerald-50 rounded-xl mr-5 flex-shrink-0">
                      <Tag className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-bold text-gray-900 mb-3 text-lg">Category</h4>
                      <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold bg-emerald-100 text-emerald-800">
                        {event.category}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status & Management */}
              <div className="border-t border-gray-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-6">Status & Management</h3>

                <div className="space-y-6">
                  {/* Status */}
                  <div className="flex items-start">
                    <div className="p-2 bg-purple-50 rounded-lg mr-4 flex-shrink-0">
                      <AlertTriangle className="h-5 w-5 text-purple-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-900 mb-2">Status</h4>
                      <div className="mb-2">
                        <StatusBadge status={event.status} />
                      </div>
                      <p className="text-sm text-gray-600 leading-relaxed">
                        {event.status === 'Draft' && 'Your event is in draft mode. Only you can see it. Submit it for review when ready.'}
                        {event.status === 'Pending' && 'Your event is pending review by our team. You will be notified once it is approved or rejected.'}
                        {event.status === 'UnderReview' && 'Your event is currently being reviewed by our team. You will be notified once it is approved or rejected.'}
                        {event.status === 'Approved' && 'Your event has been approved but is not yet published. You can now publish it.'}
                        {event.status === 'Published' && 'Your event is live and visible to the public.'}
                        {event.status === 'Rejected' && 'Your event has been rejected. Please check your email for details.'}
                        {event.status === 'Cancelled' && 'This event has been cancelled.'}
                        {event.status === 'Completed' && 'This event has already taken place.'}
                      </p>
                    </div>
                  </div>

                  {/* Capacity */}
                  <div className="flex items-start">
                    <div className="p-2 bg-blue-50 rounded-lg mr-4 flex-shrink-0">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-900 mb-3">Capacity</h4>
                      <div className="space-y-2">
                        {event.venueCapacity !== undefined && event.venueCapacity > 0 && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-700">Venue capacity</span>
                            <span className="font-semibold text-gray-900">{event.venueCapacity} people</span>
                          </div>
                        )}
                        {event.regularSeats !== undefined && event.regularSeats > 0 && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-700">Regular seats</span>
                            <span className="font-semibold text-gray-900">{event.regularSeats}</span>
                          </div>
                        )}
                        {event.vipSeats !== undefined && event.vipSeats > 0 && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-700">VIP seats</span>
                            <span className="font-semibold text-gray-900">{event.vipSeats}</span>
                          </div>
                        )}
                        {event.vvipSeats !== undefined && event.vvipSeats > 0 && (
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-700">VVIP seats</span>
                            <span className="font-semibold text-gray-900">{event.vvipSeats}</span>
                          </div>
                        )}
                        {(!event.venueCapacity || event.venueCapacity <= 0) &&
                         (!event.regularSeats || event.regularSeats <= 0) &&
                         (!event.vipSeats || event.vipSeats <= 0) &&
                         (!event.vvipSeats || event.vvipSeats <= 0) && (
                          <span className="text-sm text-gray-600">Not specified</span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Created Date */}
                  <div className="flex items-start">
                    <div className="p-2 bg-gray-50 rounded-lg mr-4 flex-shrink-0">
                      <Clock className="h-5 w-5 text-gray-600" />
                    </div>
                    <div className="min-w-0 flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">Created</h4>
                      <p className="text-gray-700 text-sm">{new Date(event.createdAt).toLocaleDateString()}</p>
                      {event.updatedAt && event.updatedAt !== event.createdAt && (
                        <p className="text-gray-600 text-xs mt-1">Updated: {new Date(event.updatedAt).toLocaleDateString()}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="border-t border-gray-200 p-6">
                <h3 className="text-lg font-bold text-gray-900 mb-4">Quick Actions</h3>
                <div className="space-y-3">
                  {/* Feature button */}
                  {event.status === 'Published' && (
                    <SimpleFeatureButton
                      eventId={event.id}
                      className="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-3 px-4 rounded-xl flex items-center justify-center font-medium transition-colors duration-200"
                    />
                  )}

                  {/* Seating Setup button */}
                  <Link
                    href={`/dashboard/organizer/events/${event.id}/seating-setup`}
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 px-4 rounded-xl flex items-center justify-center font-medium transition-colors duration-200"
                  >
                    <LayoutGrid className="mr-2 h-4 w-4" />
                    Manage Seating
                  </Link>

                  {/* Share button */}
                  <button
                    onClick={() => {
                      if (navigator.share) {
                        navigator.share({
                          title: event.title,
                          text: `Check out this event: ${event.title}`,
                          url: window.location.href,
                        });
                      } else {
                        navigator.clipboard.writeText(window.location.href);
                        alert('Link copied to clipboard!');
                      }
                    }}
                    className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-xl flex items-center justify-center font-medium transition-colors duration-200"
                  >
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Event
                  </button>

                  {/* Payout button - only for published or completed events */}
                  {(event.status === 'Published' || event.status === 'Completed') && (
                    <Link
                      href={`/dashboard/organizer/events/payouts/${event.id}`}
                      className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-xl flex items-center justify-center font-medium transition-colors duration-200"
                    >
                      <DollarSign className="mr-2 h-4 w-4" />
                      View Payout
                    </Link>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}