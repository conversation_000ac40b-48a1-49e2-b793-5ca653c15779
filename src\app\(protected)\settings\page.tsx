
'use client'

import { useForm } from "react-hook-form"
import { useSession } from "next-auth/react"
import { useState, useTransition, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { settings } from "@/actions/settings"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { SettingsSchema, settingsSchema } from "@/schemas"
import { Input } from "@/components/ui/input"
import { useCurrentUser } from "@/hooks/use-current-user"
import { FormError } from "@/components/form-error"
import { FormSuccess } from "@/components/form-success"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import ProfileImageUploader from "@/components/ui/profile-image-uploader"
import { RoleChangeDialog } from "@/components/ui/role-change-dialog"
import { UserRole } from "@prisma/client"

export default function SettingsPage() {
  const user = useCurrentUser()
  const [error, setError] = useState<string | undefined>()
  const [success, setSuccess] = useState<string | undefined>()
  const [pending, startTransition] = useTransition()
  const [imagePreview, setImagePreview] = useState<string | null>(null)
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [showRoleDialog, setShowRoleDialog] = useState(false)
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null)
  const { update } = useSession()

  const form = useForm<SettingsSchema>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      name: user?.name ?? undefined,
      email: user?.email ?? undefined,
      password: undefined,
      newPassword: undefined,
      role: user?.role ?? undefined,
      isTwoFactorEnabled: user?.isTwoFactorEnabled ?? undefined,

    }
  })

  function onSubmit(values: SettingsSchema) {
    setError('')
    setSuccess('')

    // Add the image file to the form values if it exists
    if (imageFile) {
      values.image = imageFile;
    }

    startTransition(() => {
      settings(values)
        .then((data) => {
          if (data.error) {
            setError(data.error)
          }

          if (data.success) {
            update()
            setSuccess(data.success)

            // Reset image preview after successful update
            if (imageFile) {
              // Keep the preview visible since it's now the current image
              setImageFile(null);
            }
          }
        })
        .catch(() => setError('Something went wrong!'))
    })
  }

  function onImageChange(file: File) {
    setImageFile(file)
    setImagePreview(URL.createObjectURL(file))
  }

  // Handle role change confirmation
  const handleRoleChangeConfirm = () => {
    if (selectedRole && user) {
      form.setValue('role', selectedRole);
      setShowRoleDialog(false);
    }
  };

  return (
    <>
      {/* Role change confirmation dialog */}
      {showRoleDialog && user?.role && selectedRole && (
        <RoleChangeDialog
          isOpen={showRoleDialog}
          onClose={() => setShowRoleDialog(false)}
          onConfirm={handleRoleChangeConfirm}
          currentRole={user.role as UserRole}
          newRole={selectedRole}
        />
      )}

      <Card className="w-full">
      <CardHeader>
        <p className="text-2xl font-semibold text-center">
          ⚙ Settings
        </p>
      </CardHeader>
      <ProfileImageUploader
        imagePreview={imagePreview}
        onImageChange={onImageChange}
        currentImage={user?.image || null}
        name={user?.name || 'User'}
      />
      <CardContent>
        <Form {...form}>
          <form className="space-y-6" onSubmit={form.handleSubmit(onSubmit)}>
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="John Doe"
                        disabled={pending}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {user?.isOAuth === false && (
                <>
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Email</FormLabel>
                        <FormControl>
                          <Input
                            type="text"
                            placeholder="<EMAIL>"
                            disabled={pending}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="******"
                            disabled={pending}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="newPassword"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>New Password</FormLabel>
                        <FormControl>
                          <Input
                            type="password"
                            placeholder="******"
                            disabled={pending}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </>
              )}

              {/* Role Selection - Only show if not ADMIN or SUPERADMIN */}
              {user?.role !== 'ADMIN' && user?.role !== 'SUPERADMIN' && (
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Account Type</FormLabel>
                      <Select
                        disabled={pending}
                        onValueChange={(value) => {
                          // Show confirmation dialog when role changes
                          if (value !== user?.role) {
                            setSelectedRole(value as UserRole);
                            setShowRoleDialog(true);
                          } else {
                            field.onChange(value);
                          }
                        }}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select account type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="USER">User</SelectItem>
                          <SelectItem value="ORGANIZER">Organizer</SelectItem>
                          <SelectItem value="VENDOR">Vendor</SelectItem>
                          <SelectItem value="DEVELOPER">Developer</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Change your account type to access different features.
                        <span className="block mt-1 text-amber-600 text-xs">
                          Note: Changing your role will affect your access to certain features and may require additional verification.
                        </span>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {user?.isOAuth === false && (
                <FormField
                  control={form.control}
                  name="isTwoFactorEnabled"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Two Factor Authentication</FormLabel>
                        <FormDescription>
                          Enable two factor authentication for you account. <br />
                          A code will arrive in your email on Log In.
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch disabled={pending} checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
              />
              )}

            </div>
            <FormError message={error} />
            <FormSuccess message={success} />
            <Button type="submit" disabled={pending}>Save</Button>
          </form>
        </Form>
      </CardContent>
    </Card>
    </>
  )
}
