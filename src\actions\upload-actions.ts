'use server';

import { writeFile, mkdir, unlink, access } from 'fs/promises';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { currentUser } from '@/lib/auth';

/**
 * Server action to upload a file
 */
export async function uploadFile(formData: FormData) {
  try {
    // Check if user is authenticated
    const user = await currentUser();

    if (!user?.id) {
      throw new Error('Unauthorized');
    }

    const file = formData.get('file') as File;
    const directory = formData.get('directory') as string || 'events';

    if (!file) {
      throw new Error('No file provided');
    }

    // Validate file type
    const fileExtension = path.extname(file.name).toLowerCase();

    // Define allowed extensions based on directory
    let allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

    // Add SVG for sponsor logos
    if (directory === 'sponsors') {
      allowedExtensions.push('.svg');
    }

    if (!allowedExtensions.includes(fileExtension)) {
      throw new Error(`Invalid file type. Allowed types: ${allowedExtensions.join(', ')}`);
    }

    // Validate file size (max 2MB)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (file.size > maxSize) {
      throw new Error('File size exceeds the 2MB limit');
    }

    // Create unique filename
    const uniqueId = uuidv4();
    const sanitizedFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const filename = `${uniqueId}-${sanitizedFileName}`;

    // Define upload directory and path
    const uploadDir = path.join(process.cwd(), 'public', 'uploads', directory);

    // Ensure directory exists
    await mkdir(uploadDir, { recursive: true });

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);

    // Write file to disk
    const filePath = path.join(uploadDir, filename);
    await writeFile(filePath, buffer);

    // Return the path relative to the public directory
    return {
      success: true,
      url: `/uploads/${directory}/${filename}`
    };

  } catch (error) {
    console.error('Error uploading file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Server action to delete a file
 */
export async function deleteFile(filePath: string) {
  try {
    // Check if user is authenticated
    const user = await currentUser();

    if (!user?.id) {
      throw new Error('Unauthorized');
    }

    if (!filePath) {
      return { success: true }; // Nothing to delete
    }

    // Ensure the path is within the uploads directory for security
    if (!filePath.startsWith('/uploads/')) {
      throw new Error('Invalid file path');
    }

    const fullPath = path.join(process.cwd(), 'public', filePath);

    try {
      // Check if file exists before attempting to delete
      await access(fullPath);

      // Delete the file
      await unlink(fullPath);

      return { success: true };
    } catch (error) {
      // If the file doesn't exist, we don't need to throw an error
      if (error instanceof Error && error.message.includes('ENOENT')) {
        console.warn(`File not found: ${filePath}`);
        return { success: true };
      }
      throw error;
    }
  } catch (error) {
    console.error('Error deleting file:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}
