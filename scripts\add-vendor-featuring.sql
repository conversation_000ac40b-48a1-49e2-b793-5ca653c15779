-- Add VendorFeaturing table to support vendor featuring payments
-- This script should be run after updating the Prisma schema

-- The table will be created automatically by Prisma migration
-- This is just a reference for the structure

/*
CREATE TABLE "VendorFeaturing" (
    "id" TEXT NOT NULL,
    "vendorId" TEXT NOT NULL,
    "tier" "FeaturingTier" NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3) NOT NULL,
    "status" "FeaturingStatus" NOT NULL DEFAULT 'PENDING',
    "paymentAmount" DECIMAL(65,30) NOT NULL,
    "paymentId" TEXT,
    "metadata" JSONB,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "VendorFeaturing_pkey" PRIMARY KEY ("id")
);

-- Create indexes for better performance
CREATE INDEX "VendorFeaturing_vendorId_idx" ON "VendorFeaturing"("vendorId");
CREATE INDEX "VendorFeaturing_tier_idx" ON "VendorFeaturing"("tier");
CREATE INDEX "VendorFeaturing_status_idx" ON "VendorFeaturing"("status");
CREATE INDEX "VendorFeaturing_startDate_idx" ON "VendorFeaturing"("startDate");
CREATE INDEX "VendorFeaturing_endDate_idx" ON "VendorFeaturing"("endDate");

-- Add foreign key constraint
ALTER TABLE "VendorFeaturing" ADD CONSTRAINT "VendorFeaturing_vendorId_fkey" FOREIGN KEY ("vendorId") REFERENCES "VendorProfile"("id") ON DELETE CASCADE ON UPDATE CASCADE;
*/

-- To apply this migration, run:
-- npx prisma db push
-- or
-- npx prisma migrate dev --name add-vendor-featuring
