/**
 * API Usage Data Generator
 * 
 * This script generates realistic API usage data by making requests to various endpoints
 * with different API keys, simulating real-world usage patterns.
 * 
 * Usage:
 * node generate-api-data.js <number-of-requests>
 * 
 * Example:
 * node generate-api-data.js 1000
 */

const fetch = require('node-fetch');
const fs = require('fs');
const path = require('path');

// Configuration
const BASE_URL = 'http://localhost:3000/api';
const LOG_FILE = path.join(__dirname, 'api-usage-log.json');
const DEFAULT_REQUESTS = 500;

// API keys (these will be created during the script execution)
let apiKeys = [];

// Endpoints to test
const endpoints = [
  { path: '/test', method: 'GET', weight: 20 },
  { path: '/events/published', method: 'GET', weight: 30 },
  { path: '/eventdetails/1', method: 'GET', weight: 15 },
  { path: '/external/events', method: 'GET', weight: 10 },
  { path: '/events', method: 'GET', weight: 8 },
  { path: '/tickets', method: 'GET', weight: 7 },
  { path: '/orders', method: 'GET', weight: 5 },
  { path: '/events/create', method: 'POST', weight: 3, body: {
    title: 'Test Event',
    description: 'Test Description',
    startDate: new Date(Date.now() + 86400000).toISOString(),
    endDate: new Date(Date.now() + 172800000).toISOString(),
    location: 'Test Location',
    venue: 'Test Venue',
    category: 'MUSIC',
    eventType: 'CONCERT'
  }},
  { path: '/nonexistent', method: 'GET', weight: 2 }, // Will generate 404s
];

// User agents to simulate different clients
const userAgents = [
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
  'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1',
  'Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36',
  'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/20100101 Firefox/89.0',
  'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59 Safari/537.36',
  'PostmanRuntime/7.28.0',
  'curl/7.64.1',
  'axios/0.21.1',
  'node-fetch/2.6.1',
  'python-requests/2.25.1',
];

// IP addresses to simulate different clients
const ipAddresses = [
  '***********',
  '********',
  '**********',
  '*******',
  '*******',
  '***********',
  '************',
  '*********',
  '127.0.0.1',
  '::1',
  '2001:db8::1',
  '2001:4860:4860::8888',
];

// Function to create API keys with different permissions and rate limits
async function createApiKeys() {
  console.log('Creating API keys...');
  
  const keys = [
    { name: 'Read Only Key', permissions: ['read:events', 'read:tickets', 'read:orders'], rateLimit: 100 },
    { name: 'Write Only Key', permissions: ['write:events', 'write:tickets', 'write:orders'], rateLimit: 50 },
    { name: 'Full Access Key', permissions: ['read:events', 'write:events', 'read:tickets', 'write:tickets', 'read:orders', 'write:orders', 'read:analytics'], rateLimit: 200 },
    { name: 'Limited Rate Key', permissions: ['read:events', 'read:tickets'], rateLimit: 10 },
    { name: 'Analytics Key', permissions: ['read:analytics'], rateLimit: 30 },
  ];
  
  // First, get a session by logging in
  const email = '<EMAIL>';
  const password = 'password123';
  
  try {
    // Try to log in (this is just a simulation, we won't actually log in)
    console.log('Simulating login...');
    
    // Instead, we'll create some mock API keys
    apiKeys = keys.map((key, index) => ({
      id: `api-key-${index + 1}`,
      key: `key_${Math.random().toString(36).substring(2, 15)}`,
      name: key.name,
      permissions: key.permissions,
      rateLimit: key.rateLimit,
    }));
    
    console.log(`Created ${apiKeys.length} API keys`);
    return apiKeys;
  } catch (error) {
    console.error('Error creating API keys:', error);
    // Create some mock API keys anyway
    apiKeys = keys.map((key, index) => ({
      id: `api-key-${index + 1}`,
      key: `key_${Math.random().toString(36).substring(2, 15)}`,
      name: key.name,
      permissions: key.permissions,
      rateLimit: key.rateLimit,
    }));
    
    console.log(`Created ${apiKeys.length} mock API keys`);
    return apiKeys;
  }
}

// Function to make a request with an API key
async function makeRequest(endpoint, apiKey) {
  const url = `${BASE_URL}${endpoint.path}`;
  const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
  const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
  
  const options = {
    method: endpoint.method,
    headers: {
      'X-API-Key': apiKey.key,
      'User-Agent': userAgent,
      'X-Forwarded-For': ipAddress,
      'Content-Type': 'application/json',
    },
  };
  
  if (endpoint.body && endpoint.method !== 'GET') {
    options.body = JSON.stringify(endpoint.body);
  }
  
  try {
    const startTime = Date.now();
    const response = await fetch(url, options);
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    // Get rate limit headers
    const rateLimit = response.headers.get('X-RateLimit-Limit');
    const rateLimitRemaining = response.headers.get('X-RateLimit-Remaining');
    const rateLimitReset = response.headers.get('X-RateLimit-Reset');
    
    // Log the request
    const logEntry = {
      timestamp: new Date().toISOString(),
      endpoint: endpoint.path,
      method: endpoint.method,
      apiKeyId: apiKey.id,
      apiKeyName: apiKey.name,
      status: response.status,
      responseTime,
      userAgent,
      ipAddress,
      rateLimit,
      rateLimitRemaining,
      rateLimitReset,
    };
    
    return logEntry;
  } catch (error) {
    // Log the error
    const logEntry = {
      timestamp: new Date().toISOString(),
      endpoint: endpoint.path,
      method: endpoint.method,
      apiKeyId: apiKey.id,
      apiKeyName: apiKey.name,
      status: 500,
      error: error.message,
      userAgent,
      ipAddress,
    };
    
    return logEntry;
  }
}

// Function to select a random endpoint based on weights
function selectRandomEndpoint() {
  const totalWeight = endpoints.reduce((sum, endpoint) => sum + endpoint.weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const endpoint of endpoints) {
    random -= endpoint.weight;
    if (random <= 0) {
      return endpoint;
    }
  }
  
  return endpoints[0]; // Fallback
}

// Function to select a random API key
function selectRandomApiKey() {
  return apiKeys[Math.floor(Math.random() * apiKeys.length)];
}

// Function to generate a random date within the last 30 days
function getRandomDate(days = 30) {
  const now = new Date();
  const pastDate = new Date(now.getTime() - Math.random() * days * 24 * 60 * 60 * 1000);
  return pastDate;
}

// Function to simulate a burst of requests (to trigger rate limiting)
async function simulateBurst(apiKey, endpoint, count) {
  console.log(`Simulating burst of ${count} requests with ${apiKey.name}...`);
  
  const logs = [];
  
  for (let i = 0; i < count; i++) {
    const log = await makeRequest(endpoint, apiKey);
    logs.push(log);
    
    // No delay between requests to ensure rate limiting
  }
  
  return logs;
}

// Function to simulate normal usage
async function simulateNormalUsage(count) {
  console.log(`Simulating ${count} normal requests...`);
  
  const logs = [];
  
  for (let i = 0; i < count; i++) {
    const endpoint = selectRandomEndpoint();
    const apiKey = selectRandomApiKey();
    
    const log = await makeRequest(endpoint, apiKey);
    logs.push(log);
    
    // Add a small random delay between requests
    await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
    
    // Log progress
    if (i > 0 && i % 50 === 0) {
      console.log(`Completed ${i}/${count} requests`);
    }
  }
  
  return logs;
}

// Function to simulate error scenarios
async function simulateErrors(count) {
  console.log(`Simulating ${count} error scenarios...`);
  
  const logs = [];
  
  // Simulate 404 errors
  for (let i = 0; i < count * 0.4; i++) {
    const apiKey = selectRandomApiKey();
    const log = await makeRequest({ path: '/nonexistent', method: 'GET', weight: 1 }, apiKey);
    logs.push(log);
  }
  
  // Simulate 403 errors (permission denied)
  for (let i = 0; i < count * 0.3; i++) {
    // Find a read-only key
    const readOnlyKey = apiKeys.find(key => 
      key.permissions.includes('read:events') && 
      !key.permissions.includes('write:events')
    ) || apiKeys[0];
    
    // Try to create an event with a read-only key
    const log = await makeRequest({ 
      path: '/events/create', 
      method: 'POST', 
      weight: 1,
      body: {
        title: 'Test Event',
        description: 'Test Description',
        startDate: new Date(Date.now() + 86400000).toISOString(),
        endDate: new Date(Date.now() + 172800000).toISOString(),
        location: 'Test Location',
        venue: 'Test Venue',
        category: 'MUSIC',
        eventType: 'CONCERT'
      }
    }, readOnlyKey);
    
    logs.push(log);
  }
  
  // Simulate 429 errors (rate limit exceeded)
  for (let i = 0; i < count * 0.3; i++) {
    // Find the key with the lowest rate limit
    const limitedKey = apiKeys.reduce((prev, curr) => 
      prev.rateLimit < curr.rateLimit ? prev : curr
    );
    
    // Make a burst of requests to exceed the rate limit
    const burstLogs = await simulateBurst(
      limitedKey, 
      { path: '/test', method: 'GET', weight: 1 }, 
      limitedKey.rateLimit + 5
    );
    
    logs.push(...burstLogs);
  }
  
  return logs;
}

// Function to simulate usage patterns over time
async function simulateUsageOverTime(totalRequests) {
  console.log('Simulating usage patterns over time...');
  
  // Allocate requests to different time periods
  const normalRequests = Math.floor(totalRequests * 0.7);
  const errorRequests = Math.floor(totalRequests * 0.3);
  
  // Simulate normal usage
  const normalLogs = await simulateNormalUsage(normalRequests);
  
  // Simulate error scenarios
  const errorLogs = await simulateErrors(errorRequests);
  
  // Combine logs
  const logs = [...normalLogs, ...errorLogs];
  
  // Sort logs by timestamp
  logs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
  
  return logs;
}

// Function to save logs to a file
function saveLogs(logs) {
  try {
    fs.writeFileSync(LOG_FILE, JSON.stringify(logs, null, 2));
    console.log(`Saved ${logs.length} logs to ${LOG_FILE}`);
  } catch (error) {
    console.error('Error saving logs:', error);
  }
}

// Function to insert logs into the database
async function insertLogsIntoDatabase(logs) {
  console.log('Inserting logs into the database...');
  console.log('This is a simulation - in a real implementation, this would insert the logs into the database.');
  
  // In a real implementation, this would insert the logs into the database
  // For now, we'll just log some information
  console.log(`Would insert ${logs.length} logs into the database`);
  console.log('Sample log entry:', logs[0]);
  
  // Return a mock result
  return {
    success: true,
    inserted: logs.length,
  };
}

// Main function
async function main() {
  try {
    // Get the number of requests from command line arguments
    const numRequests = parseInt(process.argv[2]) || DEFAULT_REQUESTS;
    
    // Create API keys
    await createApiKeys();
    
    // Simulate usage patterns
    const logs = await simulateUsageOverTime(numRequests);
    
    // Save logs to a file
    saveLogs(logs);
    
    // Insert logs into the database (simulation)
    await insertLogsIntoDatabase(logs);
    
    console.log('Done!');
  } catch (error) {
    console.error('Error in main function:', error);
  }
}

// Run the main function
main();
