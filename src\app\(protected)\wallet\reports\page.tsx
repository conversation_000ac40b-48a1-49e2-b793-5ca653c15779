import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Button } from '@/components/ui/button';
import { ChevronLeft } from 'lucide-react';
import Link from 'next/link';
import { FinancialReports } from '@/components/wallet/financial-reports';

export const metadata = {
  title: 'Financial Reports | QuickTimeEvents',
  description: 'View detailed financial reports and analytics',
};

export default async function FinancialReportsPage() {
  const session = await getSession();

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-6">
        <Button variant="ghost" asChild>
          <Link href="/wallet">
            <ChevronLeft className="mr-2 h-4 w-4" />
            Back to Wallet
          </Link>
        </Button>
      </div>

      <FinancialReports 
        userId={session.user.id} 
        userRole={session.user.role || 'USER'} 
      />
    </div>
  );
}
