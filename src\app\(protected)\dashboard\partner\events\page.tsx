import { Suspense } from 'react';
import { redirect } from 'next/navigation';
import { getSession } from '@/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock,
  Eye,
  ExternalLink,
  TrendingUp,
  DollarSign,
  Star,
  CheckCircle
} from 'lucide-react';

// Define session type
type UserSession = {
  user?: {
    id?: string;
    role?: string;
    name?: string;
    email?: string;
  };
};

// Mock event partnership data
const mockEventPartnerships = [
  {
    id: '1',
    eventTitle: 'Tech Innovation Summit 2024',
    eventDescription: 'Join industry leaders and innovators for a day of cutting-edge technology discussions.',
    location: 'San Francisco Convention Center',
    startDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000 + 8 * 60 * 60 * 1000),
    expectedAttendees: 500,
    partnershipType: 'Accommodation',
    status: 'active',
    revenue: 12500,
    bookings: 45,
    rating: 4.8
  },
  {
    id: '2',
    eventTitle: 'Global Finance Conference',
    eventDescription: 'Explore the future of finance with blockchain, AI, and digital transformation.',
    location: 'New York Financial District',
    startDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() + 47 * 24 * 60 * 60 * 1000),
    expectedAttendees: 800,
    partnershipType: 'Accommodation',
    status: 'upcoming',
    revenue: 0,
    bookings: 12,
    rating: null
  },
  {
    id: '3',
    eventTitle: 'Music Festival 2024',
    eventDescription: 'Three days of amazing music, food, and entertainment.',
    location: 'Central Park Amphitheater',
    startDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    endDate: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000),
    expectedAttendees: 2000,
    partnershipType: 'Accommodation',
    status: 'completed',
    revenue: 28750,
    bookings: 125,
    rating: 4.9
  }
];

export default async function PartnerEventsPage() {
  const session = await getSession() as UserSession;

  if (!session?.user?.id) {
    redirect('/auth/login');
  }

  // Only partners can access this page
  if (session.user.role !== 'PARTNER') {
    redirect('/dashboard');
  }

  const activeEvents = mockEventPartnerships.filter(e => e.status === 'active');
  const upcomingEvents = mockEventPartnerships.filter(e => e.status === 'upcoming');
  const completedEvents = mockEventPartnerships.filter(e => e.status === 'completed');

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case 'upcoming':
        return <Badge className="bg-blue-100 text-blue-800">Upcoming</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800">Completed</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const totalRevenue = mockEventPartnerships.reduce((sum, e) => sum + e.revenue, 0);
  const totalBookings = mockEventPartnerships.reduce((sum, e) => sum + e.bookings, 0);

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold tracking-tight">Event Partnerships</h1>
        <p className="text-gray-500 mt-1">
          Manage your partnerships with event organizers and track performance
        </p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Partnerships</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeEvents.length}</div>
            <p className="text-xs text-muted-foreground">Currently running</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Bookings</CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalBookings}</div>
            <p className="text-xs text-muted-foreground">Across all events</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">K{totalRevenue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">+18% from last month</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Rating</CardTitle>
            <Star className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">4.8</div>
            <p className="text-xs text-muted-foreground">From customer reviews</p>
          </CardContent>
        </Card>
      </div>

      {/* Events Tabs */}
      <Tabs defaultValue="active" className="space-y-6">
        <TabsList>
          <TabsTrigger value="active">Active ({activeEvents.length})</TabsTrigger>
          <TabsTrigger value="upcoming">Upcoming ({upcomingEvents.length})</TabsTrigger>
          <TabsTrigger value="completed">Completed ({completedEvents.length})</TabsTrigger>
          <TabsTrigger value="all">All ({mockEventPartnerships.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-4">
          {activeEvents.map((event) => (
            <Card key={event.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      {event.eventTitle}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {event.eventDescription}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(event.status)}
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{formatDate(event.startDate)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.expectedAttendees} attendees</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.partnershipType}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                  <div>
                    <p className="text-sm font-medium">Current Bookings</p>
                    <p className="text-2xl font-bold text-blue-600">{event.bookings}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Revenue Generated</p>
                    <p className="text-2xl font-bold text-green-600">K{event.revenue.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Customer Rating</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      <p className="text-2xl font-bold">{event.rating}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="upcoming" className="space-y-4">
          {upcomingEvents.map((event) => (
            <Card key={event.id}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      {event.eventTitle}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {event.eventDescription}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(event.status)}
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{formatDate(event.startDate)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.expectedAttendees} expected</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.partnershipType}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                  <div>
                    <p className="text-sm font-medium">Pre-bookings</p>
                    <p className="text-2xl font-bold text-blue-600">{event.bookings}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Days Until Event</p>
                    <p className="text-2xl font-bold text-orange-600">
                      {Math.ceil((event.startDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="completed" className="space-y-4">
          {completedEvents.map((event) => (
            <Card key={event.id} className="opacity-90">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <CheckCircle className="h-5 w-5" />
                      {event.eventTitle}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {event.eventDescription}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(event.status)}
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.location}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{formatDate(event.startDate)}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.expectedAttendees} attendees</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{event.partnershipType}</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
                  <div>
                    <p className="text-sm font-medium">Total Bookings</p>
                    <p className="text-2xl font-bold text-blue-600">{event.bookings}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Final Revenue</p>
                    <p className="text-2xl font-bold text-green-600">K{event.revenue.toLocaleString()}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium">Final Rating</p>
                    <div className="flex items-center gap-1">
                      <Star className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                      <p className="text-2xl font-bold">{event.rating}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          {mockEventPartnerships.map((event) => (
            <Card key={event.id} className={event.status === 'completed' ? 'opacity-90' : ''}>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      <Calendar className="h-5 w-5" />
                      {event.eventTitle}
                    </CardTitle>
                    <CardDescription className="mt-2">
                      {event.eventDescription}
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(event.status)}
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </TabsContent>
      </Tabs>
    </div>
  );
}
