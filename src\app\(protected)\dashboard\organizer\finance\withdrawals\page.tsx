'use client';

import React, { useState, useEffect } from 'react';
import { RoleGate } from '@/components/auth/role-gate';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { ChevronLeft, Download, Wallet, AlertCircle, Clock, CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/components/ui/use-toast';

interface Withdrawal {
  id: string;
  amount: number;
  status: string;
  date: string;
  reference: string;
  method: string;
  notes?: string;
  processedDate?: string | null;
}

interface WithdrawalSummary {
  totalWithdrawn: number;
  availableBalance: number;
  pendingBalance: number;
}

export default function OrganizerWithdrawalsPage() {
  const [activeTab, setActiveTab] = useState('all');
  const [withdrawals, setWithdrawals] = useState<Withdrawal[]>([]);
  const [summary, setSummary] = useState<WithdrawalSummary>({
    totalWithdrawn: 0,
    availableBalance: 0,
    pendingBalance: 0
  });
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchWithdrawals = async () => {
      try {
        setLoading(true);
        const status = activeTab !== 'all' ? activeTab : '';
        const response = await fetch(`/api/organizer/finance/withdrawals?status=${status}`);

        if (!response.ok) {
          throw new Error('Failed to fetch withdrawals');
        }

        const data = await response.json();
        setWithdrawals(data.withdrawals || []);
        setSummary(data.summary || {
          totalWithdrawn: 0,
          availableBalance: 0,
          pendingBalance: 0
        });
      } catch (error) {
        console.error('Error fetching withdrawals:', error);
        toast({
          title: 'Error',
          description: 'Failed to load withdrawal data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    fetchWithdrawals();
  }, [activeTab, toast]);

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'FAILED':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  // Filter withdrawals based on active tab
  const filteredWithdrawals = withdrawals.filter(withdrawal => {
    if (activeTab === 'all') return true;
    return withdrawal.status.toLowerCase() === activeTab.toLowerCase();
  });

  return (
    <RoleGate allowedRole="ORGANIZER">
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <Button variant="ghost" asChild className="mb-4 -ml-4">
              <Link href="/dashboard/organizer/finance">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Finance Overview
              </Link>
            </Button>
            <h1 className="text-3xl font-bold mb-2">Withdrawals</h1>
            <p className="text-gray-500 dark:text-gray-400">
              Manage your fund withdrawals
            </p>
          </div>
          <div className="mt-4 md:mt-0 flex gap-3">
            <Button variant="outline">
              <Download className="mr-2 h-4 w-4" />
              Export History
            </Button>
            <Button asChild>
              <Link href="/dashboard/organizer/finance/withdrawals/new">
                <Wallet className="mr-2 h-4 w-4" />
                Withdraw Funds
              </Link>
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="w-16 h-16 border-4 border-t-blue-500 border-b-blue-700 border-l-blue-500 border-r-blue-700 rounded-full animate-spin mx-auto mb-4"></div>
              <p className="text-gray-500 dark:text-gray-400">Loading withdrawal data...</p>
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Withdrawn</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(summary.totalWithdrawn)}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Lifetime</p>
                    </div>
                    <div className="bg-green-100 dark:bg-green-900 p-3 rounded-full">
                      <Wallet className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Available Balance</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(summary.availableBalance)}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Available for withdrawal</p>
                    </div>
                    <div className="bg-blue-100 dark:bg-blue-900 p-3 rounded-full">
                      <Wallet className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="pt-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Balance</p>
                      <h3 className="text-2xl font-bold mt-1">{formatCurrency(summary.pendingBalance)}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">Available in 7-14 days</p>
                    </div>
                    <div className="bg-yellow-100 dark:bg-yellow-900 p-3 rounded-full">
                      <Clock className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

        <Card>
          <CardHeader>
            <CardTitle>Withdrawal History</CardTitle>
            <CardDescription>Track all your withdrawal requests</CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="mb-6">
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="completed">Completed</TabsTrigger>
                <TabsTrigger value="pending">Pending</TabsTrigger>
                <TabsTrigger value="failed">Failed</TabsTrigger>
              </TabsList>
            </Tabs>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b dark:border-gray-700">
                    <th className="text-left py-3 px-4">Date</th>
                    <th className="text-left py-3 px-4">Reference</th>
                    <th className="text-left py-3 px-4">Method</th>
                    <th className="text-left py-3 px-4">Status</th>
                    <th className="text-right py-3 px-4">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredWithdrawals.length === 0 ? (
                    <tr>
                      <td colSpan={5} className="py-6 text-center text-gray-500 dark:text-gray-400">
                        No withdrawals found
                      </td>
                    </tr>
                  ) : (
                    filteredWithdrawals.map((withdrawal, index) => (
                      <tr key={index} className="border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="py-3 px-4">{formatDate(withdrawal.date)}</td>
                        <td className="py-3 px-4">{withdrawal.reference}</td>
                        <td className="py-3 px-4">{withdrawal.method}</td>
                        <td className="py-3 px-4">
                          <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(withdrawal.status)}`}>
                            {getStatusIcon(withdrawal.status)}
                            {withdrawal.status}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-right font-medium">{formatCurrency(withdrawal.amount)}</td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </CardContent>
          <CardFooter className="border-t px-6 py-4">
            <div className="text-sm text-gray-500 dark:text-gray-400">
              <p>Withdrawals are typically processed within 3-5 business days.</p>
              <p>For any issues with your withdrawals, please contact support.</p>
            </div>
          </CardFooter>
        </Card>
          </>
        )}
      </div>
    </RoleGate>
  );
}
