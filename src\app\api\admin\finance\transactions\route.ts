import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/admin/finance/transactions
 * Get financial transactions for admin
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50); // Max 50 per page
    const type = searchParams.get('type'); // Optional filter by transaction type

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Build where clause
    const where: any = {};
    if (type) {
      where.type = type;
    }

    // Get financial transactions
    const [transactions, totalCount] = await Promise.all([
      db.financialTransaction.findMany({
        where,
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        }
      }),
      db.financialTransaction.count({ where })
    ]);

    // Get order-related transactions
    const orderIds = await db.order.findMany({
      where: {
        status: 'Completed'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50, // Limit to recent orders
      select: {
        id: true,
        totalPrice: true,
        createdAt: true,
        userId: true,
        eventId: true
      }
    });

    // Get related events and users for orders
    const orderDetails = await Promise.all(
      orderIds.map(async (order) => {
        const [event, user] = await Promise.all([
          db.event.findUnique({
            where: { id: order.eventId },
            select: { title: true }
          }),
          db.user.findUnique({
            where: { id: order.userId },
            select: { name: true, role: true }
          })
        ]);

        return {
          ...order,
          eventTitle: event?.title || 'Unknown Event',
          userName: user?.name || 'Unknown User',
          userRole: user?.role || 'USER'
        };
      })
    );

    // Get NFC transactions
    const nfcTransactions = await db.vendorNFCTransaction.findMany({
      where: {
        status: 'COMPLETED'
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 50, // Limit to recent transactions
      include: {
        vendor: {
          select: {
            businessName: true
          }
        },
        event: {
          select: {
            title: true
          }
        },
        user: {
          select: {
            name: true
          }
        }
      }
    });

    // Format all transactions
    const formattedTransactions = [
      // Platform commission fees from orders
      ...orderDetails.map(order => ({
        id: `comm-${order.id}`,
        type: 'PLATFORM_COMMISSION',
        amount: order.totalPrice * 0.06, // 6% platform fee
        date: order.createdAt,
        user: order.userName,
        userRole: order.userRole,
        event: order.eventTitle,
        status: 'COMPLETED'
      })),

      // Processing fees from orders
      ...orderDetails.map(order => ({
        id: `proc-${order.id}`,
        type: 'PROCESSING_FEE',
        amount: order.totalPrice * 0.035, // 3.5% processing fee
        date: order.createdAt,
        user: order.userName,
        userRole: order.userRole,
        event: order.eventTitle,
        status: 'COMPLETED'
      })),

      // Processing fees from NFC transactions
      ...nfcTransactions.map(tx => ({
        id: `nfc-${tx.id}`,
        type: 'PROCESSING_FEE',
        amount: tx.amount * 0.035, // 3.5% processing fee
        date: tx.createdAt,
        user: tx.vendor.businessName || tx.user?.name || 'Unknown',
        userRole: 'VENDOR',
        event: tx.event.title,
        status: 'COMPLETED'
      })),

      // Actual financial transactions from the database
      ...transactions.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: tx.amount,
        date: tx.createdAt,
        user: tx.user.name || tx.user.email || 'Unknown',
        userRole: tx.user.role,
        description: tx.description || '',
        status: tx.status
      }))
    ]
    .sort((a, b) => b.date.getTime() - a.date.getTime())
    .slice(skip, skip + limit);

    // Calculate total count including derived transactions
    const totalTransactionsCount = orderDetails.length * 2 + nfcTransactions.length + totalCount;

    // Return the transactions with pagination
    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        page,
        limit,
        total: totalTransactionsCount,
        pages: Math.ceil(totalTransactionsCount / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching financial transactions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch financial transactions' },
      { status: 500 }
    );
  }
}
