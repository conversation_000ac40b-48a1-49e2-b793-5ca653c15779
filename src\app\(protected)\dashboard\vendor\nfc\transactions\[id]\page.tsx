'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { 
  CreditCard, 
  ArrowLeft, 
  Calendar, 
  User, 
  MapPin, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCcw, 
  Printer, 
  Download, 
  ShoppingBag 
} from 'lucide-react';
import { format } from 'date-fns';
import { LoadingScreen } from '@/components/ui/loading-screen';

interface TransactionProduct {
  id: string;
  productId: string;
  name: string;
  description: string | null;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  imagePath: string | null;
}

interface TransactionDetails {
  id: string;
  amount: number;
  currency: string;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED' | 'CANCELLED';
  reference: string | null;
  notes: string | null;
  createdAt: string;
  processedAt: string | null;
  
  event: {
    id: string;
    title: string;
    location: string;
    venue: string;
    startDate: string;
    endDate: string;
  };
  
  customer: {
    id: string;
    name: string;
    email: string | null;
  };
  
  vendor: {
    id: string;
    businessName: string;
    logo: string | null;
  };
  
  nfcCard: {
    id: string;
    uid: string;
    lastUsed: string | null;
  };
  
  products: TransactionProduct[];
}

export default function TransactionDetailsPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = use(params);
  const router = useRouter();
  const [transaction, setTransaction] = useState<TransactionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [updatingStatus, setUpdatingStatus] = useState(false);
  
  useEffect(() => {
    async function fetchTransactionDetails() {
      try {
        setLoading(true);
        const response = await fetch(`/api/vendors/nfc/transactions/${id}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            throw new Error('Transaction not found');
          }
          throw new Error('Failed to fetch transaction details');
        }
        
        const data = await response.json();
        setTransaction(data);
      } catch (err) {
        console.error('Error fetching transaction details:', err);
        setError(err instanceof Error ? err.message : 'An error occurred');
        toast({
          title: 'Error',
          description: 'Failed to load transaction details',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    }
    
    fetchTransactionDetails();
  }, [id]);

  const handleStatusUpdate = async (newStatus: 'COMPLETED' | 'CANCELLED' | 'REFUNDED') => {
    try {
      setUpdatingStatus(true);
      
      const response = await fetch(`/api/vendors/nfc/transactions/${id}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update transaction status');
      }
      
      const data = await response.json();
      
      // Update the transaction in state
      if (transaction) {
        setTransaction({
          ...transaction,
          status: newStatus,
          processedAt: data.processedAt,
        });
      }
      
      toast({
        title: 'Status Updated',
        description: `Transaction status changed to ${newStatus.toLowerCase()}`,
      });
    } catch (err) {
      console.error('Error updating transaction status:', err);
      toast({
        title: 'Update Failed',
        description: 'Failed to update transaction status',
        variant: 'destructive',
      });
    } finally {
      setUpdatingStatus(false);
    }
  };

  const handlePrintReceipt = () => {
    toast({
      title: 'Printing Receipt',
      description: 'Sending receipt to printer...',
    });
    
    // In a real implementation, this would call an API to print the receipt
    setTimeout(() => {
      toast({
        title: 'Receipt Printed',
        description: 'Receipt has been sent to the printer',
      });
    }, 2000);
  };

  const handleDownloadReceipt = () => {
    toast({
      title: 'Downloading Receipt',
      description: 'Preparing receipt for download...',
    });
    
    // In a real implementation, this would generate and download a PDF receipt
    setTimeout(() => {
      toast({
        title: 'Receipt Downloaded',
        description: 'Receipt has been downloaded',
      });
    }, 2000);
  };

  if (loading) {
    return <LoadingScreen message="Loading transaction details..." />;
  }

  if (error) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="mb-6"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Transactions
        </Button>
        
        <Card>
          <CardContent className="pt-6 flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-xl font-bold mb-2">Error Loading Transaction</h3>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={() => router.push('/dashboard/vendor/nfc/transactions')}>
              View All Transactions
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!transaction) {
    return (
      <div className="container mx-auto py-8 px-4">
        <Button 
          variant="ghost" 
          onClick={() => router.back()}
          className="mb-6"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Transactions
        </Button>
        
        <Card>
          <CardContent className="pt-6 flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-amber-500 mb-4" />
            <h3 className="text-xl font-bold mb-2">Transaction Not Found</h3>
            <p className="text-gray-600 mb-6">The requested transaction could not be found.</p>
            <Button onClick={() => router.push('/dashboard/vendor/nfc/transactions')}>
              View All Transactions
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <Button 
        variant="ghost" 
        onClick={() => router.back()}
        className="mb-6"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Back to Transactions
      </Button>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Transaction Details</CardTitle>
                <CardDescription>
                  Transaction ID: {transaction.id}
                </CardDescription>
              </div>
              <TransactionStatusBadge status={transaction.status} />
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex flex-col md:flex-row justify-between gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Date & Time</p>
                    <p className="font-medium">
                      {format(new Date(transaction.createdAt), 'MMM dd, yyyy HH:mm:ss')}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500">Amount</p>
                    <p className="font-medium text-xl">
                      {transaction.currency} {transaction.amount.toFixed(2)}
                    </p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-500">Payment Method</p>
                    <p className="font-medium">NFC Card Payment</p>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="font-medium mb-3">Event Information</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <Calendar className="h-5 w-5 text-gray-500 mt-0.5" />
                      <div>
                        <p className="font-medium">{transaction.event.title}</p>
                        <p className="text-sm text-gray-600">
                          {format(new Date(transaction.event.startDate), 'MMM dd, yyyy')} - 
                          {format(new Date(transaction.event.endDate), 'MMM dd, yyyy')}
                        </p>
                        <p className="text-sm text-gray-600 flex items-center mt-1">
                          <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                          {transaction.event.venue}, {transaction.event.location}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="font-medium mb-3">Customer Information</h3>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="flex items-start gap-3">
                      <User className="h-5 w-5 text-gray-500 mt-0.5" />
                      <div>
                        <p className="font-medium">{transaction.customer.name}</p>
                        {transaction.customer.email && (
                          <p className="text-sm text-gray-600">{transaction.customer.email}</p>
                        )}
                        <div className="flex items-center mt-2">
                          <CreditCard className="h-4 w-4 mr-2 text-gray-400" />
                          <span className="text-sm text-gray-600">NFC Card: {transaction.nfcCard.uid}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div>
                  <h3 className="font-medium mb-3">Transaction Timeline</h3>
                  <div className="space-y-4">
                    <div className="flex items-start gap-3">
                      <div className="bg-blue-100 p-1.5 rounded-full">
                        <Clock className="h-4 w-4 text-blue-600" />
                      </div>
                      <div>
                        <p className="font-medium">Transaction Created</p>
                        <p className="text-sm text-gray-600">
                          {format(new Date(transaction.createdAt), 'MMM dd, yyyy HH:mm:ss')}
                        </p>
                      </div>
                    </div>
                    
                    {transaction.processedAt && (
                      <div className="flex items-start gap-3">
                        <div className="bg-green-100 p-1.5 rounded-full">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <p className="font-medium">Transaction Processed</p>
                          <p className="text-sm text-gray-600">
                            {format(new Date(transaction.processedAt), 'MMM dd, yyyy HH:mm:ss')}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {transaction.status === 'REFUNDED' && (
                      <div className="flex items-start gap-3">
                        <div className="bg-blue-100 p-1.5 rounded-full">
                          <RefreshCcw className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <p className="font-medium">Transaction Refunded</p>
                          <p className="text-sm text-gray-600">
                            {transaction.processedAt 
                              ? format(new Date(transaction.processedAt), 'MMM dd, yyyy HH:mm:ss')
                              : 'Date not available'}
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {transaction.status === 'CANCELLED' && (
                      <div className="flex items-start gap-3">
                        <div className="bg-red-100 p-1.5 rounded-full">
                          <XCircle className="h-4 w-4 text-red-600" />
                        </div>
                        <div>
                          <p className="font-medium">Transaction Cancelled</p>
                          <p className="text-sm text-gray-600">
                            {transaction.processedAt 
                              ? format(new Date(transaction.processedAt), 'MMM dd, yyyy HH:mm:ss')
                              : 'Date not available'}
                          </p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                {transaction.notes && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="font-medium mb-2">Notes</h3>
                      <p className="text-gray-600 bg-gray-50 p-3 rounded-md">{transaction.notes}</p>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
            <CardFooter className="flex flex-wrap gap-3 justify-between">
              <div className="flex flex-wrap gap-3">
                <Button 
                  variant="outline"
                  onClick={handlePrintReceipt}
                >
                  <Printer className="mr-2 h-4 w-4" />
                  Print Receipt
                </Button>
                
                <Button 
                  variant="outline"
                  onClick={handleDownloadReceipt}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download Receipt
                </Button>
              </div>
              
              {transaction.status === 'PENDING' && (
                <div className="flex gap-3">
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" disabled={updatingStatus}>
                        <XCircle className="mr-2 h-4 w-4" />
                        Cancel Transaction
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Cancel Transaction?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to cancel this transaction? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>No, Keep It</AlertDialogCancel>
                        <AlertDialogAction 
                          onClick={() => handleStatusUpdate('CANCELLED')}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Yes, Cancel Transaction
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                  
                  <Button 
                    variant="default"
                    disabled={updatingStatus}
                    onClick={() => handleStatusUpdate('COMPLETED')}
                  >
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Complete Transaction
                  </Button>
                </div>
              )}
              
              {transaction.status === 'COMPLETED' && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="outline" className="border-amber-500 text-amber-600 hover:bg-amber-50">
                      <RefreshCcw className="mr-2 h-4 w-4" />
                      Refund Transaction
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Refund Transaction?</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to refund this transaction? This will return the funds to the customer.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={() => handleStatusUpdate('REFUNDED')}
                        className="bg-amber-600 hover:bg-amber-700"
                      >
                        Yes, Process Refund
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </CardFooter>
          </Card>
        </div>
        
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <ShoppingBag className="h-5 w-5 mr-2 text-gray-500" />
                Products ({transaction.products.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {transaction.products.map((product) => (
                  <div key={product.id} className="flex gap-3 border-b pb-4 last:border-0">
                    <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                      {product.imagePath ? (
                        <Image
                          src={product.imagePath}
                          alt={product.name}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-gray-400">
                          <ShoppingBag className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                    <div className="flex-grow">
                      <h4 className="font-medium">{product.name}</h4>
                      {product.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">{product.description}</p>
                      )}
                      <div className="flex justify-between mt-1">
                        <p className="text-sm text-gray-500">
                          {product.quantity} x {transaction.currency} {product.unitPrice.toFixed(2)}
                        </p>
                        <p className="font-medium">
                          {transaction.currency} {product.totalPrice.toFixed(2)}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 bg-gray-50 p-4 rounded-lg">
                <div className="flex justify-between mb-2">
                  <span className="text-gray-600">Subtotal</span>
                  <span>{transaction.currency} {transaction.amount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-medium text-lg">
                  <span>Total</span>
                  <span>{transaction.currency} {transaction.amount.toFixed(2)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

function TransactionStatusBadge({ status }: { status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REFUNDED' | 'CANCELLED' }) {
  switch (status) {
    case 'PENDING':
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Pending
        </Badge>
      );
    case 'COMPLETED':
      return (
        <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Completed
        </Badge>
      );
    case 'FAILED':
      return (
        <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">
          <XCircle className="h-3 w-3 mr-1" />
          Failed
        </Badge>
      );
    case 'REFUNDED':
      return (
        <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          <RefreshCcw className="h-3 w-3 mr-1" />
          Refunded
        </Badge>
      );
    case 'CANCELLED':
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-800 hover:bg-gray-100">
          <XCircle className="h-3 w-3 mr-1" />
          Cancelled
        </Badge>
      );
    default:
      return null;
  }
}
