'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';

import { useCurrentRole } from '@/hooks/use-current-role';
import { Users, Search, CheckCircle, Eye, Trash2, ArrowUpDown, Shield, Mail, Calendar, DollarSign, UserCog, AlertTriangle } from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';

// User interface
interface User {
  id: string;
  name: string;
  email: string;
  role: 'SUPERADMIN' | 'ADMIN' | 'ORGANIZER' | 'VENDOR' | 'USER';
  emailVerified: string | null;
  createdAt: string;
  isVerified?: boolean;
  verificationStatus?: 'PENDING' | 'APPROVED' | 'REJECTED' | null;
  verificationDate?: string | null;
  verificationSubmittedAt?: string | null;
  accountBalance?: number;
  lastLoginAt?: string | null;
  eventsCount?: number;
  accountsCount?: number;
}

export default function AdminUsersPage() {
  const router = useRouter();

  const role = useCurrentRole();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [verificationFilter, setVerificationFilter] = useState('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogAction, setDialogAction] = useState<'view' | 'edit' | 'delete' | 'promote' | null>(null);
  const [newRole, setNewRole] = useState<string>('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortField, setSortField] = useState('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Fetch users
  useEffect(() => {
    const fetchUsers = async () => {
      setLoading(true);
      try {
        // Build query parameters
        const params = new URLSearchParams();
        params.append('page', currentPage.toString());
        params.append('limit', '10');

        if (searchTerm) {
          params.append('search', searchTerm);
        }

        if (roleFilter !== 'all') {
          params.append('role', roleFilter);
        }

        if (verificationFilter !== 'all') {
          params.append('verification', verificationFilter);
        }

        params.append('sortField', sortField);
        params.append('sortDirection', sortDirection);

        // Fetch users from API
        const response = await fetch(`/api/admin/users?${params.toString()}`);

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();

        setUsers(data.users);
        setTotalPages(data.pagination.totalPages);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching users:', error);
        setLoading(false);
        toast({
          title: 'Error',
          description: 'Failed to fetch users. Please try again.',
          variant: 'destructive',
        });
      }
    };

    fetchUsers();
  }, [searchTerm, roleFilter, verificationFilter, sortField, sortDirection, currentPage]);

  // Handle user actions
  const handleUserAction = async (action: 'edit' | 'delete' | 'promote') => {
    if (!selectedUser) return;

    try {
      let response;
      let successMessage = '';

      if (action === 'delete') {
        // Call API to delete user
        response = await fetch('/api/admin/users', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId: selectedUser.id }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Error: ${response.status}`);
        }

        successMessage = `User "${selectedUser.name}" has been deleted`;

        // Update local state
        setUsers(users.filter(user => user.id !== selectedUser.id));
      } else if (action === 'promote' && newRole) {
        // Call API to update user role
        response = await fetch('/api/admin/users', {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ userId: selectedUser.id, role: newRole }),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || `Error: ${response.status}`);
        }

        successMessage = `User "${selectedUser.name}" has been promoted to ${newRole}`;

        // Update local state
        setUsers(users.map(user =>
          user.id === selectedUser.id ? { ...user, role: newRole as any } : user
        ));
      } else if (action === 'edit') {
        // For now, just show a message since we don't have a full edit form
        successMessage = `User "${selectedUser.name}" has been updated`;
      }

      toast({
        title: 'Success',
        description: successMessage,
      });

      // Close dialog and reset state
      setIsDialogOpen(false);
      setSelectedUser(null);
      setDialogAction(null);
      setNewRole('');
    } catch (error) {
      console.error(`Error ${action}ing user:`, error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : `Failed to ${action} user. Please try again.`,
        variant: 'destructive',
      });
    }
  };

  // Open dialog for user action
  const openActionDialog = (user: User, action: 'view' | 'edit' | 'delete' | 'promote') => {
    setSelectedUser(user);
    setDialogAction(action);
    setNewRole(user.role);
    setIsDialogOpen(true);
  };

  // Format date for display
  const formatDateDisplay = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return format(new Date(dateString), 'PPP');
  };

  // Get role badge
  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'SUPERADMIN':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{role}</Badge>;
      case 'ADMIN':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{role}</Badge>;
      case 'ORGANIZER':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">{role}</Badge>;
      case 'VENDOR':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">{role}</Badge>;
      case 'USER':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{role}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{role}</Badge>;
    }
  };

  // Get verification badge
  const getVerificationBadge = (user: User) => {
    // Only show verification status for ORGANIZER and VENDOR roles
    if (user.role !== 'ORGANIZER' && user.role !== 'VENDOR') {
      return <Badge className="bg-gray-100 text-gray-500 hover:bg-gray-200">N/A</Badge>;
    }

    // Check verification status
    if (user.verificationStatus === 'APPROVED') {
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Verified</Badge>;
    } else if (user.verificationStatus === 'PENDING') {
      return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">Pending</Badge>;
    } else if (user.verificationStatus === 'REJECTED') {
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Rejected</Badge>;
    } else {
      return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">Unverified</Badge>;
    }
  };

  // Toggle sort direction
  const toggleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">Manage Users</h1>
          <p className="text-gray-500 mt-1">
            View and manage user accounts on the platform
          </p>
        </div>
        <div className="mt-4 md:mt-0">
          <Button asChild>
            <Link href="/admin/verifications">
              <CheckCircle className="mr-2 h-4 w-4" />
              Review Verifications
            </Link>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label htmlFor="search" className="text-sm font-medium text-gray-700 mb-1 block">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  id="search"
                  placeholder="Search users..."
                  className="pl-8"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            <div>
              <label htmlFor="role" className="text-sm font-medium text-gray-700 mb-1 block">
                Role
              </label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger id="role">
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="SUPERADMIN">Super Admin</SelectItem>
                  <SelectItem value="ADMIN">Admin</SelectItem>
                  <SelectItem value="ORGANIZER">Organizer</SelectItem>
                  <SelectItem value="VENDOR">Vendor</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <label htmlFor="verification" className="text-sm font-medium text-gray-700 mb-1 block">
                Verification Status
              </label>
              <Select value={verificationFilter} onValueChange={setVerificationFilter}>
                <SelectTrigger id="verification">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="verified">Verified</SelectItem>
                  <SelectItem value="unverified">Unverified</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users Table */}
      <Card>
        <CardHeader>
          <CardTitle>Users</CardTitle>
          <CardDescription>
            {users.length} users found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">No users found</h3>
              <p className="text-gray-500 max-w-md mx-auto">
                Try adjusting your filters or check back later.
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('name')}
                      >
                        Name
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('email')}
                      >
                        Email
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Role</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Verification</th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                      <button
                        className="flex items-center focus:outline-none"
                        onClick={() => toggleSort('createdAt')}
                      >
                        Joined
                        <ArrowUpDown className="ml-1 h-4 w-4" />
                      </button>
                    </th>
                    <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id} className="border-b hover:bg-gray-50">
                      <td className="px-4 py-4 text-sm font-medium text-gray-900">{user.name}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">{user.email}</td>
                      <td className="px-4 py-4 text-sm">{getRoleBadge(user.role)}</td>
                      <td className="px-4 py-4 text-sm">{getVerificationBadge(user)}</td>
                      <td className="px-4 py-4 text-sm text-gray-500">{formatDateDisplay(user.createdAt)}</td>
                      <td className="px-4 py-4 text-sm text-right">
                        <div className="flex justify-end space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openActionDialog(user, 'view')}
                            className="h-8 w-8 p-0"
                          >
                            <span className="sr-only">View</span>
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openActionDialog(user, 'promote')}
                            className="h-8 w-8 p-0 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            disabled={user.role === 'SUPERADMIN' || (user.role === 'ADMIN' && role !== 'SUPERADMIN')}
                          >
                            <span className="sr-only">Change Role</span>
                            <Shield className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openActionDialog(user, 'delete')}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                            disabled={user.role === 'SUPERADMIN' || (user.role === 'ADMIN' && role !== 'SUPERADMIN')}
                          >
                            <span className="sr-only">Delete</span>
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {!loading && users.length > 0 && (
            <div className="flex justify-between items-center mt-6">
              <div className="text-sm text-gray-500">
                Showing {Math.min(users.length, 10)} of {users.length} users
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* User Action Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          {selectedUser && (
            <>
              <DialogHeader>
                <DialogTitle>
                  {dialogAction === 'view' && 'User Details'}
                  {dialogAction === 'edit' && 'Edit User'}
                  {dialogAction === 'delete' && 'Delete User'}
                  {dialogAction === 'promote' && 'Change User Role'}
                </DialogTitle>
                <DialogDescription>
                  {dialogAction === 'view' && 'View details for this user'}
                  {dialogAction === 'edit' && 'Edit user information'}
                  {dialogAction === 'delete' && 'Are you sure you want to delete this user?'}
                  {dialogAction === 'promote' && 'Change the role for this user'}
                </DialogDescription>
              </DialogHeader>

              {dialogAction === 'view' && (
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold">{selectedUser.name}</h3>
                    <div className="flex items-center mt-2 space-x-2">
                      {getRoleBadge(selectedUser.role)}
                      {getVerificationBadge(selectedUser)}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Email</h4>
                      <p className="text-sm">{selectedUser.email}</p>
                      <p className="text-xs text-gray-500 mt-1">
                        {selectedUser.emailVerified
                          ? `Verified on ${formatDateDisplay(selectedUser.emailVerified)}`
                          : 'Email not verified'}
                      </p>
                    </div>

                    {/* Show account balance for all users */}
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Account Balance</h4>
                      <p className="text-sm">${selectedUser.accountBalance?.toFixed(2) || '0.00'}</p>
                    </div>
                  </div>

                  {/* Show verification details for ORGANIZER and VENDOR roles */}
                  {(selectedUser.role === 'ORGANIZER' || selectedUser.role === 'VENDOR') && selectedUser.verificationStatus && (
                    <div className="border-t pt-4">
                      <h4 className="text-sm font-medium text-gray-500 mb-3">Verification Details</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h5 className="text-xs font-medium text-gray-500 mb-1">Status</h5>
                          <p className="text-sm">{selectedUser.verificationStatus}</p>
                        </div>
                        {selectedUser.verificationSubmittedAt && (
                          <div>
                            <h5 className="text-xs font-medium text-gray-500 mb-1">Submitted On</h5>
                            <p className="text-sm">{formatDateDisplay(selectedUser.verificationSubmittedAt)}</p>
                          </div>
                        )}
                        {selectedUser.verificationDate && (
                          <div>
                            <h5 className="text-xs font-medium text-gray-500 mb-1">Verified On</h5>
                            <p className="text-sm">{formatDateDisplay(selectedUser.verificationDate)}</p>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Joined</h4>
                      <p className="text-sm">{formatDateDisplay(selectedUser.createdAt)}</p>
                    </div>
                    <div>
                      <h4 className="text-sm font-medium text-gray-500 mb-1">Last Login</h4>
                      <p className="text-sm">{formatDateDisplay(selectedUser.lastLoginAt || null)}</p>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4 pt-4 border-t mt-4">
                    {/* Common actions for all users */}
                    <Button asChild variant="outline">
                      <Link href="#" onClick={(e) => {
                        e.preventDefault();
                        toast({
                          title: 'Email Sent',
                          description: `A message has been sent to ${selectedUser.email}`,
                        });
                      }}>
                        <Mail className="mr-2 h-4 w-4" />
                        Contact User
                      </Link>
                    </Button>

                    {/* Role-specific actions */}
                    {(selectedUser.role === 'ORGANIZER' || selectedUser.role === 'VENDOR') && (
                      <>
                        <Button asChild variant="outline">
                          <Link href={`/admin/users/${selectedUser.id}/events`}>
                            <Calendar className="mr-2 h-4 w-4" />
                            View Events
                          </Link>
                        </Button>
                        <Button asChild variant="outline">
                          <Link href={`/admin/users/${selectedUser.id}/transactions`}>
                            <DollarSign className="mr-2 h-4 w-4" />
                            View Transactions
                          </Link>
                        </Button>
                      </>
                    )}

                    {/* Show verification actions for ORGANIZER and VENDOR roles */}
                    {(selectedUser.role === 'ORGANIZER' || selectedUser.role === 'VENDOR') &&
                     selectedUser.verificationStatus === 'PENDING' && (
                      <>
                        <Button variant="outline" className="bg-green-50 text-green-600 hover:bg-green-100 hover:text-green-700">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Approve Verification
                        </Button>
                        <Button variant="outline" className="bg-red-50 text-red-600 hover:bg-red-100 hover:text-red-700">
                          <AlertTriangle className="mr-2 h-4 w-4" />
                          Reject Verification
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              )}

              {dialogAction === 'promote' && (
                <div className="space-y-4 py-4">
                  <div>
                    <label htmlFor="new-role" className="text-sm font-medium text-gray-700 mb-1 block">
                      Select New Role
                    </label>
                    <Select value={newRole} onValueChange={setNewRole}>
                      <SelectTrigger id="new-role">
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        {role === 'SUPERADMIN' && <SelectItem value="ADMIN">Admin</SelectItem>}
                        <SelectItem value="ORGANIZER">Organizer</SelectItem>
                        <SelectItem value="VENDOR">Vendor</SelectItem>
                        <SelectItem value="USER">User</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-gray-500 mt-2">
                      Changing a user&apos;s role will affect their permissions and access to features.
                    </p>
                  </div>
                </div>
              )}

              {dialogAction === 'delete' && (
                <div className="py-4">
                  <p className="text-sm text-gray-500">
                    Are you sure you want to delete the user &ldquo;{selectedUser.name}&rdquo;? This action cannot be undone and will remove all associated data.
                  </p>
                </div>
              )}

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Cancel
                </Button>
                {dialogAction === 'promote' && (
                  <Button
                    onClick={() => handleUserAction('promote')}
                    disabled={!newRole || newRole === selectedUser.role}
                  >
                    <Shield className="mr-2 h-4 w-4" />
                    Change Role
                  </Button>
                )}
                {dialogAction === 'delete' && (
                  <Button
                    variant="destructive"
                    onClick={() => handleUserAction('delete')}
                  >
                    <Trash2 className="mr-2 h-4 w-4" />
                    Delete User
                  </Button>
                )}
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
