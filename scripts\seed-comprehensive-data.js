#!/usr/bin/env node

/**
 * Comprehensive Transaction and Attendance Seeding Script
 *
 * Creates realistic testing data for:
 * - Ticket purchases and transactions
 * - Event attendance and check-ins
 * - Payment methods and financial records
 * - User interactions and analytics
 * - Complete event lifecycle data
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n🚀 ${message}`, colors.cyan + colors.bright);
}

// Utility functions
function randomBetween(min, max) {
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

function randomChoice(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function randomDate(start, end) {
  return new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
}

function generatePhoneNumber() {
  return `+260 ${randomBetween(90, 99)} ${randomBetween(1000000, 9999999)}`;
}

function generateTransactionId() {
  return `TXN-${Date.now()}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
}

function generateQRCode() {
  return `QR-${Math.random().toString(36).substring(2, 10).toUpperCase()}`;
}

// Payment methods and their characteristics
const paymentMethods = [
  { method: 'CARD', successRate: 0.92, processingFee: 0.029 },
  { method: 'MOBILE_MONEY', successRate: 0.88, processingFee: 0.015 },
  { method: 'BANK_TRANSFER', successRate: 0.95, processingFee: 0.01 },
  { method: 'CASH', successRate: 1.0, processingFee: 0.0 }
];

// Ticket types and pricing
const ticketTypes = [
  { type: 'REGULAR', basePrice: 50, capacity: 0.7 },
  { type: 'VIP', basePrice: 100, capacity: 0.25 },
  { type: 'VVIP', basePrice: 200, capacity: 0.05 }
];

// Event categories and their typical pricing multipliers
const categoryMultipliers = {
  'TECHNOLOGY': 1.5,
  'BUSINESS': 1.3,
  'MUSIC': 1.2,
  'SPORTS_AND_FITNESS': 1.1,
  'FOOD_AND_DRINK': 0.9,
  'CULTURAL_FESTIVALS': 0.8,
  'HEALTH_AND_WELLNESS': 1.0,
  'FASHION_SHOWS': 1.4,
  'GAMING_EVENTS': 1.2,
  'WEDDING': 2.0
};

async function getBaseData() {
  logHeader('Fetching Base Data');

  const [events, users, organizers] = await Promise.all([
    prisma.event.findMany({
      include: {
        user: true
      }
    }),
    prisma.user.findMany({
      where: {
        role: {
          in: ['USER', 'ORGANIZER', 'PARTNER']
        }
      }
    }),
    prisma.user.findMany({
      where: {
        role: 'ORGANIZER'
      }
    })
  ]);

  logInfo(`Found ${events.length} events, ${users.length} users, ${organizers.length} organizers`);

  return { events, users, organizers };
}

async function createTicketPricing(events) {
  logHeader('Creating Ticket Pricing Structure');

  const ticketPricing = [];

  for (const event of events) {
    const categoryMultiplier = categoryMultipliers[event.category] || 1.0;
    const eventCapacity = randomBetween(100, 1000);

    for (const ticketType of ticketTypes) {
      const price = Math.round(ticketType.basePrice * categoryMultiplier);
      const capacity = Math.round(eventCapacity * ticketType.capacity);

      ticketPricing.push({
        eventId: event.id,
        type: ticketType.type,
        price: price,
        capacity: capacity,
        available: capacity,
        eventCapacity: eventCapacity
      });
    }
  }

  logSuccess(`Created pricing for ${ticketPricing.length} ticket types across ${events.length} events`);
  return ticketPricing;
}

async function createTransactionsAndOrders(events, users, ticketPricing) {
  logHeader('Creating Realistic Transactions and Orders');

  const orders = [];
  const transactions = [];
  const tickets = [];
  let orderCounter = 1;
  let transactionCounter = 1;
  let ticketCounter = 1;

  for (const event of events) {
    const eventPricing = ticketPricing.filter(tp => tp.eventId === event.id);
    const isUpcoming = event.startDate > new Date();
    const isPast = event.endDate < new Date();

    // Determine number of orders based on event status and type
    let orderCount;
    if (isPast) {
      orderCount = randomBetween(20, 50); // Past events have more orders
    } else if (isUpcoming) {
      orderCount = randomBetween(10, 30); // Upcoming events have moderate orders
    } else {
      orderCount = randomBetween(5, 15); // Current events have fewer orders
    }

    logInfo(`Creating ${orderCount} orders for event: ${event.title}`);

    for (let i = 0; i < orderCount; i++) {
      const customer = randomChoice(users);
      const paymentMethod = randomChoice(paymentMethods);
      const ticketType = randomChoice(eventPricing);

      // Determine order details
      const quantity = randomBetween(1, 4);
      const unitPrice = ticketType.price;
      const subtotal = unitPrice * quantity;
      const processingFee = Math.round(subtotal * paymentMethod.processingFee * 100) / 100;
      const totalPrice = subtotal + processingFee;

      // Determine order status based on payment method success rate
      const isSuccessful = Math.random() < paymentMethod.successRate;
      const orderStatus = isSuccessful ? 'Completed' : randomChoice(['Pending', 'Cancelled']);

      // Create order (matching actual schema) - let Prisma generate the ID
      const orderData = {
        userId: customer.id,
        eventId: event.id,
        customerEmail: customer.email,
        customerName: customer.name,
        customerPhone: generatePhoneNumber(),
        totalPrice: totalPrice,
        pricePaid: isSuccessful ? totalPrice : 0,
        status: orderStatus,
        paymentMethod: paymentMethod.method,
        notes: isSuccessful ? 'Order completed successfully' : 'Payment failed',
        createdAt: randomDate(
          new Date(event.createdAt),
          isPast ? event.startDate : new Date()
        )
      };

      // Store order data for later creation
      orders.push({
        orderData,
        paymentMethod,
        isSuccessful,
        totalPrice,
        processingFee,
        quantity,
        unitPrice,
        ticketType,
        customer,
        event
      });

      // We'll create tickets after orders are created
    }
  }

  logSuccess(`Prepared ${orders.length} orders for creation`);
  return { orders };
}

async function main() {
  logHeader('Comprehensive Event Management Data Seeding');

  console.log(`
${colors.cyan}╔══════════════════════════════════════════════════════════════╗
║            🎯 COMPREHENSIVE DATA SEEDING                     ║
║                                                              ║
║  Creating realistic testing data for:                       ║
║  • Ticket purchases and transactions                        ║
║  • Event attendance and check-ins                           ║
║  • Payment methods and financial records                    ║
║  • User interactions and analytics                          ║
║  • Complete event lifecycle data                            ║
║                                                              ║
║  This will create a fully populated testing environment     ║
║  with thousands of realistic data points                    ║
╚══════════════════════════════════════════════════════════════╝${colors.reset}
  `);

  try {
    // Get base data
    const { events, users, organizers } = await getBaseData();

    if (events.length === 0) {
      logError('No events found. Please run event seeding first.');
      return;
    }

    if (users.length === 0) {
      logError('No users found. Please run user seeding first.');
      return;
    }

    // Create ticket pricing structure
    const ticketPricing = await createTicketPricing(events);

    // Create transactions and orders
    const { orders } = await createTransactionsAndOrders(events, users, ticketPricing);

    // Insert data into database
    logHeader('Inserting Data into Database');

    let createdOrders = 0;
    let createdPayments = 0;
    let createdTickets = 0;

    // Process orders one by one to handle relationships properly
    logInfo('Creating orders, payments, and tickets...');
    for (const orderInfo of orders) {
      const { orderData, paymentMethod, isSuccessful, totalPrice, processingFee, quantity, unitPrice, ticketType, customer, event } = orderInfo;

      // Create order
      const order = await prisma.order.create({ data: orderData });
      createdOrders++;

      // Create payment
      if (isSuccessful) {
        await prisma.payment.create({
          data: {
            orderId: order.id,
            method: paymentMethod.method,
            amount: totalPrice,
            currency: 'ZMW',
            status: 'Completed',
            details: {
              transactionId: generateTransactionId(),
              processingFee: processingFee,
              gatewayResponse: 'Payment processed successfully'
            }
          }
        });
        createdPayments++;

        // Create tickets
        for (let j = 0; j < quantity; j++) {
          const isPast = event.endDate < new Date();
          const isUsed = isPast ? Math.random() < 0.85 : false;

          const ticketData = {
            eventId: event.id,
            userId: customer.id,
            orderId: order.id,
            type: ticketType.type,
            price: unitPrice,
            email: customer.email,
            qrCodeData: generateQRCode(),
            isUsed: isUsed,
            isAvailable: true,
            quantity: 1,
            totalPrice: Math.round(unitPrice),
            totalSeats: 1,
            regularPrice: ticketType.type === 'REGULAR' ? unitPrice : 0,
            regularSeats: ticketType.type === 'REGULAR' ? 1 : 0,
            vipPrice: ticketType.type === 'VIP' ? unitPrice : 0,
            vipSeats: ticketType.type === 'VIP' ? 1 : 0,
            vvipPrice: ticketType.type === 'VVIP' ? unitPrice : null,
            vvipSeats: ticketType.type === 'VVIP' ? 1 : 0,
            saleStartTime: '09:00',
            saleEndTime: '23:59',
            specialGuestName: '',
            specialGuestType: '',
            description: `${ticketType.type} ticket for ${event.title}`
          };

          // Add scan data for past events
          if (isPast && isUsed) {
            ticketData.scannedAt = randomDate(
              new Date(event.startDate.getTime() - 2 * 60 * 60 * 1000), // 2 hours before
              new Date(event.startDate.getTime() + 4 * 60 * 60 * 1000)  // 4 hours after
            );
            ticketData.scannedBy = `staff-${Math.floor(Math.random() * 5) + 1}`;
          }

          await prisma.ticket.create({ data: ticketData });
          createdTickets++;
        }
      }
    }

    logSuccess(`Inserted ${createdOrders} orders`);
    logSuccess(`Inserted ${createdPayments} payments`);
    logSuccess(`Inserted ${createdTickets} tickets`);

    // Summary
    logHeader('Seeding Complete!');
    logSuccess(`🎉 Successfully created comprehensive testing data:`);
    logInfo(`   • ${createdOrders} realistic orders`);
    logInfo(`   • ${createdPayments} payment records`);
    logInfo(`   • ${createdTickets} event tickets`);
    logInfo(`   • Multiple payment methods and statuses`);
    logInfo(`   • Realistic attendance patterns`);
    logInfo(`   • Complete event lifecycle data`);

    console.log('\n');
    logSuccess('Your event management system now has comprehensive testing data!');
    logInfo('You can test:');
    logInfo('   • Organizer dashboards with real revenue data');
    logInfo('   • Attendee management and check-ins');
    logInfo('   • Financial reporting and analytics');
    logInfo('   • Payment processing workflows');
    logInfo('   • Event capacity and sold-out scenarios');

  } catch (error) {
    logError(`Seeding failed: ${error.message}`);
    console.error(error);
    process.exit(1);
  }
}

// Run the main function
main()
  .catch((error) => {
    logError(`Unexpected error: ${error.message}`);
    console.error(error);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
