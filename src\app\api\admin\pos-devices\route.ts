import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient, POSDeviceStatus } from '@prisma/client';
import { currentUser } from '@/lib/auth';
import {
  createPOSDevice,
  updatePOSDevice,
  getPOSDeviceById
} from '@/lib/pos-device-management';

// Create a new PrismaClient instance
const prisma = new PrismaClient();

/**
 * GET /api/admin/pos-devices
 * Get all POS devices
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const status = searchParams.get('status') as POSDeviceStatus | null;
    const deviceId = searchParams.get('deviceId');

    // Build query
    const query: any = {};

    if (status) {
      query.status = status;
    }

    if (deviceId) {
      query.deviceId = deviceId;
    }

    // Get POS devices
    const devices = await prisma.pOSDevice.findMany({
      where: query,
      include: {
        rentals: {
          where: {
            status: {
              in: ['ACTIVE', 'PENDING']
            }
          },
          include: {
            vendor: true,
            event: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json(devices);
  } catch (error) {
    console.error('Error getting POS devices:', error);
    return NextResponse.json(
      { error: 'Failed to get POS devices' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * POST /api/admin/pos-devices
 * Create a new POS device
 */
export async function POST(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    const {
      deviceId,
      serialNumber,
      model,
      manufacturer,
      purchaseDate,
      notes
    } = body;

    // Validate required fields
    if (!deviceId || !serialNumber || !model || !manufacturer || !purchaseDate) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Check if device ID or serial number already exists
    const existingDevice = await prisma.pOSDevice.findFirst({
      where: {
        OR: [
          { deviceId },
          { serialNumber }
        ]
      }
    });

    if (existingDevice) {
      return NextResponse.json(
        { error: 'Device ID or serial number already exists' },
        { status: 400 }
      );
    }

    // Create POS device
    const device = await createPOSDevice({
      deviceId,
      serialNumber,
      model,
      manufacturer,
      purchaseDate: new Date(purchaseDate),
      notes
    });

    return NextResponse.json(device, { status: 201 });
  } catch (error) {
    console.error('Error creating POS device:', error);
    return NextResponse.json(
      { error: 'Failed to create POS device' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * PATCH /api/admin/pos-devices?id=:id
 * Update a POS device
 */
export async function PATCH(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
    }

    // Parse request body
    const body = await request.json();
    const {
      model,
      status,
      lastMaintenance,
      nextMaintenance,
      notes
    } = body;

    // Build update data
    const updateData: any = {};

    if (model !== undefined) updateData.model = model;
    if (status !== undefined) updateData.status = status;
    if (lastMaintenance !== undefined) updateData.lastMaintenance = lastMaintenance ? new Date(lastMaintenance) : null;
    if (nextMaintenance !== undefined) updateData.nextMaintenance = nextMaintenance ? new Date(nextMaintenance) : null;
    if (notes !== undefined) updateData.notes = notes;

    // Update POS device
    const device = await updatePOSDevice(id, updateData);

    return NextResponse.json(device);
  } catch (error) {
    console.error('Error updating POS device:', error);
    return NextResponse.json(
      { error: 'Failed to update POS device' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}

/**
 * DELETE /api/admin/pos-devices?id=:id
 * Delete a POS device
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check if user is authenticated and has admin role
    const user = await currentUser();

    if (!user?.id || (user.role !== 'ADMIN' && user.role !== 'SUPERADMIN')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get ID from query parameters
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Device ID is required' }, { status: 400 });
    }

    // Check if device has active rentals
    const device = await prisma.pOSDevice.findUnique({
      where: { id },
      include: {
        rentals: {
          where: {
            status: {
              in: ['ACTIVE', 'PENDING']
            }
          }
        }
      }
    });

    if (!device) {
      return NextResponse.json(
        { error: 'POS device not found' },
        { status: 404 }
      );
    }

    if (device.rentals.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete POS device with active rentals' },
        { status: 400 }
      );
    }

    // Delete POS device
    await prisma.pOSDevice.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting POS device:', error);
    return NextResponse.json(
      { error: 'Failed to delete POS device' },
      { status: 500 }
    );
  } finally {
    // Disconnect Prisma client
    await prisma.$disconnect();
  }
}
