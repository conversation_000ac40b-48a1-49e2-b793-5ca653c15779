import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';

/**
 * GET /api/organizer/finance/revenue-breakdown
 * Get revenue breakdown by month for organizer's events
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : new Date(new Date().setFullYear(new Date().getFullYear() - 1));
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : new Date();
    
    // Get all events created by the organizer
    const events = await db.event.findMany({
      where: {
        userId: user.id!
      },
      select: {
        id: true
      }
    });

    const eventIds = events.map(event => event.id);

    // If organizer has no events, return empty data
    if (eventIds.length === 0) {
      return NextResponse.json([]);
    }

    // Get all months between start and end date
    const months = [];
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      months.push(new Date(currentDate));
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    // Get revenue data for each month
    const revenueData = await Promise.all(months.map(async (month) => {
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0, 23, 59, 59);

      // Get ticket sales for the month
      const ticketSales = await db.order.aggregate({
        where: {
          status: 'Completed',
          eventId: {
            in: eventIds
          },
          createdAt: {
            gte: monthStart,
            lte: monthEnd
          }
        },
        _sum: {
          totalPrice: true
        }
      });

      // Calculate fees
      const platformFees = (ticketSales._sum.totalPrice || 0) * 0.06;
      const processingFees = (ticketSales._sum.totalPrice || 0) * 0.035;
      const netRevenue = (ticketSales._sum.totalPrice || 0) - platformFees - processingFees;

      return {
        name: month.toLocaleString('default', { month: 'short' }) + ' ' + month.getFullYear(),
        ticketSales: ticketSales._sum.totalPrice || 0,
        platformFees,
        processingFees,
        netRevenue
      };
    }));

    return NextResponse.json(revenueData);
  } catch (error) {
    console.error('Error fetching organizer revenue breakdown data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch revenue breakdown data' },
      { status: 500 }
    );
  }
}
