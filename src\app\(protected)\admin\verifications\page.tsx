'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
// import { useCurrentUser } from '@/hooks/use-current-user';
import { useCurrentRole } from '@/hooks/use-current-role';
import { CheckCircle, XCircle, Clock, Eye, FileText, User, Building, MapPin } from 'lucide-react';
import Image from 'next/image';
import Link from 'next/link';
import { format } from 'date-fns';

// Define the verification type
interface Verification {
  id: string;
  userId: string;
  businessName: string;
  businessType: string;
  registrationNumber: string | null;
  taxPayerIdNumber: string;
  phoneNumber: string;
  alternativeEmail: string | null;
  website: string | null;
  physicalAddress: string;
  city: string;
  province: string;
  postalCode: string | null;
  idDocumentPath: string;
  idDocumentType: string;
  businessLicensePath: string | null;
  taxCertificatePath: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  createdAt: string;
  verifiedAt: string | null;
  rejectionReason: string | null;
  eventTypes: string;
  experience: string;
  previousEvents: string | null;
  user: {
    name: string;
    email: string;
  };
}

// Mock data for demonstration - replace with actual API calls
const mockVerifications: Verification[] = [
  {
    id: '1',
    userId: 'user1',
    businessName: 'Acme Events',
    businessType: 'Company',
    registrationNumber: 'REG123456',
    taxPayerIdNumber: 'TPIN987654',
    phoneNumber: '+260 97 1234567',
    alternativeEmail: '<EMAIL>',
    website: 'https://acmeevents.com',
    physicalAddress: '123 Main Street',
    city: 'Lusaka',
    province: 'Lusaka',
    postalCode: '10101',
    idDocumentPath: '/uploads/verification/id-sample.jpg',
    idDocumentType: 'national_id',
    businessLicensePath: '/uploads/verification/license-sample.jpg',
    taxCertificatePath: '/uploads/verification/tax-sample.jpg',
    status: 'PENDING',
    createdAt: '2023-06-15T10:30:00Z',
    eventTypes: 'Concerts, Corporate Events, Weddings',
    experience: '5',
    previousEvents: 'Annual Tech Conference (2022), Summer Music Festival (2021)',
    verifiedAt: null,
    rejectionReason: null,
    user: {
      name: 'John Doe',
      email: '<EMAIL>',
    }
  },
  {
    id: '2',
    userId: 'user2',
    businessName: 'Bright Day Productions',
    businessType: 'Individual',
    registrationNumber: null,
    taxPayerIdNumber: 'TPIN456789',
    phoneNumber: '+260 96 7654321',
    alternativeEmail: null,
    website: 'https://brightday.com',
    physicalAddress: '45 Independence Avenue',
    city: 'Kitwe',
    province: 'Copperbelt',
    postalCode: '20202',
    idDocumentPath: '/uploads/verification/id-sample2.jpg',
    idDocumentType: 'passport',
    businessLicensePath: null,
    taxCertificatePath: '/uploads/verification/tax-sample2.jpg',
    status: 'PENDING',
    createdAt: '2023-06-18T14:45:00Z',
    eventTypes: 'Weddings, Birthday Parties',
    experience: '3',
    previousEvents: 'Wedding Expo (2022)',
    verifiedAt: null,
    rejectionReason: null,
    user: {
      name: 'Jane Smith',
      email: '<EMAIL>',
    }
  },
  {
    id: '3',
    userId: 'user3',
    businessName: 'Global Conferences Ltd',
    businessType: 'Company',
    registrationNumber: 'REG789012',
    taxPayerIdNumber: 'TPIN345678',
    phoneNumber: '+260 95 1122334',
    alternativeEmail: '<EMAIL>',
    website: 'https://globalconferences.com',
    physicalAddress: '78 Cairo Road',
    city: 'Lusaka',
    province: 'Lusaka',
    postalCode: '10101',
    idDocumentPath: '/uploads/verification/id-sample3.jpg',
    idDocumentType: 'national_id',
    businessLicensePath: '/uploads/verification/license-sample3.jpg',
    taxCertificatePath: '/uploads/verification/tax-sample3.jpg',
    status: 'APPROVED',
    createdAt: '2023-05-20T09:15:00Z',
    verifiedAt: '2023-05-23T11:30:00Z',
    rejectionReason: null,
    eventTypes: 'Conferences, Seminars, Workshops',
    experience: '10',
    previousEvents: 'International Business Summit (2022), Tech Expo (2021), Leadership Conference (2020)',
    user: {
      name: 'Michael Johnson',
      email: '<EMAIL>',
    }
  },
  {
    id: '4',
    userId: 'user4',
    businessName: 'Party Planners',
    businessType: 'Individual',
    registrationNumber: null,
    taxPayerIdNumber: 'TPIN234567',
    phoneNumber: '+260 97 9876543',
    alternativeEmail: null,
    website: null,
    physicalAddress: '12 Freedom Way',
    city: 'Ndola',
    province: 'Copperbelt',
    postalCode: '20303',
    idDocumentPath: '/uploads/verification/id-sample4.jpg',
    idDocumentType: 'drivers_license',
    businessLicensePath: null,
    taxCertificatePath: '/uploads/verification/tax-sample4.jpg',
    status: 'REJECTED',
    createdAt: '2023-06-10T16:20:00Z',
    verifiedAt: null,
    rejectionReason: 'Tax certificate appears to be expired. Please provide a current tax certificate.',
    eventTypes: 'Birthday Parties, Anniversaries',
    experience: '2',
    previousEvents: 'Corporate End of Year Party (2022)',
    user: {
      name: 'Sarah Williams',
      email: '<EMAIL>',
    }
  }
];

export default function AdminVerificationsPage() {
  const router = useRouter();
  // We don't need the current user for this page
  const role = useCurrentRole();
  const [activeTab, setActiveTab] = useState('pending');
  // Define a more flexible type for verifications that allows null values
  type VerificationType = typeof mockVerifications[0] & {
    registrationNumber: string | null;
    alternativeEmail: string | null;
    website: string | null;
    postalCode: string | null;
    businessLicensePath: string | null;
    rejectionReason: string | null;
    verifiedAt: string | null;
    status: 'PENDING' | 'APPROVED' | 'REJECTED';
  };

  const [verifications, setVerifications] = useState<VerificationType[]>(mockVerifications as VerificationType[]);
  const [selectedVerification, setSelectedVerification] = useState<VerificationType | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  // Fetch verifications from API
  useEffect(() => {
    const fetchVerifications = async () => {
      try {
        setLoading(true);
        // Use the actual API endpoint
        const response = await fetch(`/api/admin/verifications?status=${activeTab === 'all' ? '' : activeTab.toUpperCase()}`);

        if (!response.ok) {
          throw new Error('Failed to fetch verifications');
        }

        const data = await response.json();
        // Handle the response format which includes verifications and pagination
        if (data.verifications) {
          setVerifications(data.verifications as VerificationType[]);
        } else {
          setVerifications(data as VerificationType[]);
        }
      } catch (error) {
        console.error('Error fetching verifications:', error);
        // Fallback to mock data if API fails
        const filteredVerifications = mockVerifications.filter((v: typeof mockVerifications[0]) =>
          activeTab === 'all' || v.status.toLowerCase() === activeTab
        );
        setVerifications(filteredVerifications as VerificationType[]);
      } finally {
        setLoading(false);
      }
    };

    fetchVerifications();
  }, [activeTab]);

  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  const handleApprove = async (id: string) => {
    setLoading(true);
    try {
      // Call the API to approve verification
      const response = await fetch(`/api/admin/verifications/${id}/approve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to approve verification');
      }

      // Update local state - create a new array to avoid type issues
      const updatedVerifications = verifications.map(verification => {
        if (verification.id === id) {
          return {
            ...verification,
            status: 'APPROVED' as const,
            verifiedAt: new Date().toISOString()
          };
        }
        return verification;
      });

      setVerifications(updatedVerifications);

      // Close dialog if open
      setIsDialogOpen(false);

      // Show success message
      alert('Verification approved successfully');

      // Refresh the list
      const updatedTab = activeTab === 'pending' ? 'approved' : activeTab;
      setActiveTab(updatedTab);
    } catch (error) {
      console.error('Error approving verification:', error);
      alert(error instanceof Error ? error.message : 'Failed to approve verification');
    } finally {
      setLoading(false);
    }
  };

  const handleReject = async (id: string) => {
    if (!rejectionReason.trim()) {
      alert('Please provide a reason for rejection');
      return;
    }

    setLoading(true);
    try {
      // Call API to reject verification
      const response = await fetch(`/api/admin/verifications/${id}/reject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ reason: rejectionReason })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reject verification');
      }

      // Update local state - create a new array to avoid type issues
      const updatedVerifications = verifications.map(verification => {
        if (verification.id === id) {
          return {
            ...verification,
            status: 'REJECTED' as const,
            rejectionReason: rejectionReason
          };
        }
        return verification;
      });

      setVerifications(updatedVerifications);

      // Close dialog
      setIsDialogOpen(false);
      setRejectionReason('');

      // Show success message
      alert('Verification rejected successfully');

      // Refresh the list
      const updatedTab = activeTab === 'pending' ? 'rejected' : activeTab;
      setActiveTab(updatedTab);
    } catch (error) {
      console.error('Error rejecting verification:', error);
      alert(error instanceof Error ? error.message : 'Failed to reject verification');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <Badge className="bg-green-500">Approved</Badge>;
      case 'PENDING':
        return <Badge className="bg-yellow-500">Pending</Badge>;
      case 'REJECTED':
        return <Badge className="bg-red-500">Rejected</Badge>;
      default:
        return <Badge className="bg-gray-500">Unknown</Badge>;
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Organizer Verifications</h1>
        <Button asChild variant="outline">
          <Link href="/admin/verifications/vendor">
            View Vendor Verifications
          </Link>
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-8">
          <TabsTrigger value="pending">
            <Clock className="w-4 h-4 mr-2" />
            Pending
          </TabsTrigger>
          <TabsTrigger value="approved">
            <CheckCircle className="w-4 h-4 mr-2" />
            Approved
          </TabsTrigger>
          <TabsTrigger value="rejected">
            <XCircle className="w-4 h-4 mr-2" />
            Rejected
          </TabsTrigger>
          <TabsTrigger value="all">
            <FileText className="w-4 h-4 mr-2" />
            All
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {activeTab === 'pending' && 'Pending Verifications'}
                {activeTab === 'approved' && 'Approved Verifications'}
                {activeTab === 'rejected' && 'Rejected Verifications'}
                {activeTab === 'all' && 'All Verifications'}
              </CardTitle>
              <CardDescription>
                {activeTab === 'pending' && 'Review and process organizer verification requests'}
                {activeTab === 'approved' && 'View approved organizer verifications'}
                {activeTab === 'rejected' && 'View rejected organizer verifications'}
                {activeTab === 'all' && 'View all organizer verifications'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {verifications.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No verifications found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {verifications.map((verification: VerificationType) => (
                    <div
                      key={verification.id}
                      className="border rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                      onClick={() => {
                        setSelectedVerification(verification);
                        setIsDialogOpen(true);
                      }}
                    >
                      <div className="flex justify-between items-start">
                        <div>
                          <h3 className="font-medium text-lg">{verification.businessName}</h3>
                          <p className="text-sm text-gray-500">
                            Submitted by {verification.user.name} ({verification.user.email})
                          </p>
                          <p className="text-sm text-gray-500 mt-1">
                            Submitted on {format(new Date(verification.createdAt), 'PPP')}
                          </p>
                        </div>
                        <div className="flex flex-col items-end">
                          {getStatusBadge(verification.status)}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="mt-2"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedVerification(verification);
                              setIsDialogOpen(true);
                            }}
                          >
                            <Eye className="w-4 h-4 mr-1" />
                            View Details
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Verification Details Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedVerification && (
            <>
              <DialogHeader>
                <DialogTitle className="flex items-center justify-between">
                  <span>Verification Details</span>
                  {getStatusBadge(selectedVerification.status)}
                </DialogTitle>
                <DialogDescription>
                  Submitted on {format(new Date(selectedVerification.createdAt), 'PPP')}
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                {/* Business Information */}
                <div className="space-y-4">
                  <h3 className="font-medium text-lg flex items-center">
                    <Building className="w-5 h-5 mr-2 text-blue-600" />
                    Business Information
                  </h3>

                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-500">Business Name</span>
                      <p>{selectedVerification.businessName}</p>
                    </div>

                    <div>
                      <span className="text-sm font-medium text-gray-500">Business Type</span>
                      <p>{selectedVerification.businessType}</p>
                    </div>

                    {selectedVerification.registrationNumber && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Registration Number</span>
                        <p>{selectedVerification.registrationNumber}</p>
                      </div>
                    )}

                    <div>
                      <span className="text-sm font-medium text-gray-500">Tax Payer ID (TPIN)</span>
                      <p>{selectedVerification.taxPayerIdNumber}</p>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="font-medium text-lg flex items-center">
                    <User className="w-5 h-5 mr-2 text-blue-600" />
                    Contact Information
                  </h3>

                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-500">User</span>
                      <p>{selectedVerification.user.name}</p>
                    </div>

                    <div>
                      <span className="text-sm font-medium text-gray-500">Email</span>
                      <p>{selectedVerification.user.email}</p>
                    </div>

                    <div>
                      <span className="text-sm font-medium text-gray-500">Phone</span>
                      <p>{selectedVerification.phoneNumber}</p>
                    </div>

                    {selectedVerification.alternativeEmail && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Alternative Email</span>
                        <p>{selectedVerification.alternativeEmail}</p>
                      </div>
                    )}

                    {selectedVerification.website && (
                      <div>
                        <span className="text-sm font-medium text-gray-500">Website</span>
                        <p>
                          <a
                            href={selectedVerification.website}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            {selectedVerification.website}
                          </a>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Address */}
              <div className="mt-6 space-y-4">
                <h3 className="font-medium text-lg flex items-center">
                  <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                  Address
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Physical Address</span>
                    <p>{selectedVerification.physicalAddress}</p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-gray-500">City</span>
                      <p>{selectedVerification.city}</p>
                    </div>

                    <div>
                      <span className="text-sm font-medium text-gray-500">Province</span>
                      <p>{selectedVerification.province}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Documents */}
              <div className="mt-6 space-y-4">
                <h3 className="font-medium text-lg flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-blue-600" />
                  Verification Documents
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* ID Document */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">ID Document</h4>
                    <p className="text-sm text-gray-500 mb-2">
                      Type: {selectedVerification.idDocumentType === 'national_id' ? 'National ID' :
                            selectedVerification.idDocumentType === 'passport' ? 'Passport' :
                            selectedVerification.idDocumentType === 'drivers_license' ? 'Driver&apos;s License' :
                            selectedVerification.idDocumentType}
                    </p>
                    <div className="relative h-40 bg-gray-100 rounded-lg overflow-hidden">
                      {selectedVerification.idDocumentPath ? (
                        <Image
                          src={selectedVerification.idDocumentPath}
                          alt="ID Document"
                          fill
                          className="object-contain"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          No document available
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Business License */}
                  {selectedVerification.businessLicensePath && (
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">Business License</h4>
                      <div className="relative h-40 bg-gray-100 rounded-lg overflow-hidden">
                        {selectedVerification.businessLicensePath ? (
                          <Image
                            src={selectedVerification.businessLicensePath}
                            alt="Business License"
                            fill
                            className="object-contain"
                          />
                        ) : (
                          <div className="flex items-center justify-center h-full text-gray-400">
                            No document available
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Tax Certificate */}
                  <div className="border rounded-lg p-4">
                    <h4 className="font-medium mb-2">Tax Certificate</h4>
                    <div className="relative h-40 bg-gray-100 rounded-lg overflow-hidden">
                      {selectedVerification.taxCertificatePath ? (
                        <Image
                          src={selectedVerification.taxCertificatePath}
                          alt="Tax Certificate"
                          fill
                          className="object-contain"
                        />
                      ) : (
                        <div className="flex items-center justify-center h-full text-gray-400">
                          No document available
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div className="mt-6 space-y-4">
                <h3 className="font-medium text-lg">Additional Information</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <span className="text-sm font-medium text-gray-500">Event Types</span>
                    <p>{selectedVerification.eventTypes}</p>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-gray-500">Years of Experience</span>
                    <p>{selectedVerification.experience}</p>
                  </div>
                </div>

                {selectedVerification.previousEvents && (
                  <div>
                    <span className="text-sm font-medium text-gray-500">Previous Events</span>
                    <p className="whitespace-pre-line">{selectedVerification.previousEvents}</p>
                  </div>
                )}
              </div>

              {/* Status Information */}
              {selectedVerification.status === 'APPROVED' && (
                <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-green-600 mt-0.5 mr-2" />
                    <div>
                      <h4 className="font-medium text-green-800">Approved</h4>
                      <p className="text-green-700 text-sm">
                        Approved on {format(new Date(selectedVerification.verifiedAt || new Date()), 'PPP')}
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {selectedVerification.status === 'REJECTED' && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-start">
                    <XCircle className="w-5 h-5 text-red-600 mt-0.5 mr-2" />
                    <div>
                      <h4 className="font-medium text-red-800">Rejected</h4>
                      <p className="text-red-700 mt-1">Reason: {selectedVerification.rejectionReason}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {selectedVerification.status === 'PENDING' && (
                <div className="mt-6">
                  {rejectionReason ? (
                    <div className="mb-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Rejection Reason
                      </label>
                      <Textarea
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        placeholder="Provide a reason for rejection"
                        className="w-full"
                        rows={3}
                      />
                    </div>
                  ) : null}

                  <div className="flex justify-end space-x-4">
                    {!rejectionReason && (
                      <Button
                        variant="outline"
                        onClick={() => setRejectionReason('Please provide additional information:')}
                      >
                        <XCircle className="w-4 h-4 mr-2" />
                        Reject
                      </Button>
                    )}

                    {rejectionReason ? (
                      <div className="space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => setRejectionReason('')}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => handleReject(selectedVerification.id)}
                          disabled={loading}
                        >
                          {loading ? 'Processing...' : 'Confirm Rejection'}
                        </Button>
                      </div>
                    ) : (
                      <Button
                        variant="default"
                        className="bg-green-600 hover:bg-green-700"
                        onClick={() => handleApprove(selectedVerification.id)}
                        disabled={loading}
                      >
                        <CheckCircle className="w-4 h-4 mr-2" />
                        {loading ? 'Processing...' : 'Approve Verification'}
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
