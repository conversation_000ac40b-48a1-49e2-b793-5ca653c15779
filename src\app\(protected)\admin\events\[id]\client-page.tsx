'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useCurrentRole } from '@/hooks/use-current-role';
import {
  Calendar, DollarSign, Ticket, Users, MapPin, Clock,
  Mail, Phone, User, CreditCard, Download, Eye, Edit
} from 'lucide-react';
import Link from 'next/link';
import { format } from 'date-fns';
import { toast } from '@/components/ui/use-toast';
import Image from 'next/image';

// Interfaces
interface Event {
  id: string;
  title: string;
  description: string;
  startDate: string;
  endDate: string;
  location: string;
  organizer: {
    id: string;
    name: string;
    email: string;
  };
  category: string;
  status: string;
  eventType: string;
  imagePath?: string;
  venue?: string;
  createdAt: string;
  updatedAt: string;
  tickets: Ticket[];
  attendees: Attendee[];
  sales: {
    totalSales: number;
    totalTicketsSold: number;
  };
}

interface Ticket {
  id: string;
  name: string;
  price: number;
  quantity: number;
  sold: number;
  available: number;
}

interface Attendee {
  id: string;
  name: string;
  email: string;
  phone: string;
  ticketType: string;
  ticketPrice: number;
  purchaseDate: string;
  status: string;
}

export default function AdminEventDetailClientPage({ eventId }: { eventId: string }) {
  const router = useRouter();
  const role = useCurrentRole();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedAttendee, setSelectedAttendee] = useState<Attendee | null>(null);
  const [isAttendeeDialogOpen, setIsAttendeeDialogOpen] = useState(false);
  const [isUpdateBalanceDialogOpen, setIsUpdateBalanceDialogOpen] = useState(false);
  const [balanceAmount, setBalanceAmount] = useState('');
  const [balanceAction, setBalanceAction] = useState<'add' | 'subtract'>('add');

  // Fetch event details
  useEffect(() => {
    const fetchEventDetails = async () => {
      setLoading(true);
      try {
        // Fetch real data from the API
        const response = await fetch(`/api/admin/events/${eventId}`);

        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = await response.json();

        if (data && data.event) {
          setEvent(data.event);
        } else {
          setEvent(null);
        }
        setLoading(false);
      } catch (error) {
        console.error('Error fetching event details:', error);
        setLoading(false);
      }
    };

    fetchEventDetails();
  }, [eventId]);

  // Format date for display
  const formatDateDisplay = (dateString: string) => {
    return format(new Date(dateString), 'PPP');
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Published':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">{status}</Badge>;
      case 'Draft':
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
      case 'Pending':
        return <Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">{status}</Badge>;
      case 'UnderReview':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">{status}</Badge>;
      case 'Approved':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">{status}</Badge>;
      case 'Rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{status}</Badge>;
      case 'Cancelled':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">{status}</Badge>;
      case 'Completed':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{status}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{status}</Badge>;
    }
  };

  // Get event type badge
  const getEventTypeBadge = (type: string) => {
    switch (type) {
      case 'PHYSICAL':
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-200">{type}</Badge>;
      case 'ONLINE':
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200">{type}</Badge>;
      case 'HYBRID':
        return <Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">{type}</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800 hover:bg-gray-200">{type}</Badge>;
    }
  };

  // View attendee details
  const viewAttendeeDetails = (attendee: Attendee) => {
    setSelectedAttendee(attendee);
    setIsAttendeeDialogOpen(true);
  };

  // Open update balance dialog
  const openUpdateBalanceDialog = () => {
    setBalanceAmount('');
    setBalanceAction('add');
    setIsUpdateBalanceDialogOpen(true);
  };

  // Update organizer balance
  const updateOrganizerBalance = async () => {
    if (!event || !balanceAmount || parseFloat(balanceAmount) <= 0) {
      toast({
        title: 'Error',
        description: 'Please enter a valid amount',
        variant: 'destructive',
      });
      return;
    }

    try {
      const response = await fetch(`/api/admin/organizers/${event.organizer.id}/balance`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: parseFloat(balanceAmount),
          action: balanceAction,
        }),
      });

      if (!response.ok) {
        throw new Error(`API returned status: ${response.status}`);
      }

      const data = await response.json();

      toast({
        title: 'Success',
        description: `Organizer balance has been ${balanceAction === 'add' ? 'increased' : 'decreased'} by $${balanceAmount}`,
      });

      setIsUpdateBalanceDialogOpen(false);
    } catch (error) {
      console.error('Error updating organizer balance:', error);
      toast({
        title: 'Error',
        description: 'Failed to update organizer balance',
        variant: 'destructive',
      });
    }
  };

  // Access control - only ADMIN and SUPERADMIN can access
  if (role !== 'ADMIN' && role !== 'SUPERADMIN') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-gray-600 mb-6">You need admin privileges to view this page.</p>
          <Button onClick={() => router.push('/dashboard')}>Go to Dashboard</Button>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-2">Event Not Found</h2>
          <p className="text-gray-600 mb-6">The event you&apos;re looking for doesn&apos;t exist or has been removed.</p>
          <Button asChild>
            <Link href="/admin/events">Back to Events</Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-3xl font-bold">{event.title}</h1>
            {getStatusBadge(event.status)}
          </div>
          <p className="text-gray-500 mt-1">
            Organized by {event.organizer.name}
          </p>
        </div>
        <div className="mt-4 md:mt-0 flex gap-2">
          <Button variant="outline" asChild>
            <Link href="/admin/events">
              Back to Events
            </Link>
          </Button>
          <Button variant="outline" onClick={openUpdateBalanceDialog}>
            <DollarSign className="mr-2 h-4 w-4" />
            Update Balance
          </Button>
        </div>
      </div>

      <Tabs defaultValue="details">
        <TabsList className="mb-6">
          <TabsTrigger value="details">Event Details</TabsTrigger>
          <TabsTrigger value="tickets">Tickets</TabsTrigger>
          <TabsTrigger value="attendees">Attendees</TabsTrigger>
          <TabsTrigger value="sales">Sales</TabsTrigger>
        </TabsList>

        <TabsContent value="details">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle>Event Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {event.imagePath && (
                  <div className="relative w-full h-64 rounded-lg overflow-hidden">
                    <Image
                      src={event.imagePath}
                      alt={event.title}
                      fill
                      style={{ objectFit: 'cover' }}
                    />
                  </div>
                )}

                <div>
                  <h3 className="text-lg font-semibold mb-2">Description</h3>
                  <p className="text-gray-700">{event.description}</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-start">
                    <Calendar className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Date</h4>
                      <p>{formatDateDisplay(event.startDate)}</p>
                      {event.startDate !== event.endDate && (
                        <p>to {formatDateDisplay(event.endDate)}</p>
                      )}
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Clock className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Time</h4>
                      <p>{format(new Date(event.startDate), 'h:mm a')}</p>
                      <p>to {format(new Date(event.endDate), 'h:mm a')}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Location</h4>
                      <p>{event.location}</p>
                      {event.venue && <p className="text-gray-500">{event.venue}</p>}
                    </div>
                  </div>

                  <div className="flex items-start">
                    <Ticket className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-gray-500">Event Type</h4>
                      <div>{getEventTypeBadge(event.eventType)}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Organizer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-start">
                  <User className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Name</h4>
                    <p>{event.organizer.name}</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Email</h4>
                    <p>{event.organizer.email}</p>
                  </div>
                </div>

                <Separator />

                <div className="pt-2">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Event Created</h4>
                  <p>{formatDateDisplay(event.createdAt)}</p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Last Updated</h4>
                  <p>{formatDateDisplay(event.updatedAt)}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="tickets">
          <Card>
            <CardHeader>
              <CardTitle>Ticket Information</CardTitle>
              <CardDescription>
                Overview of all ticket types for this event
              </CardDescription>
            </CardHeader>
            <CardContent>
              {event.tickets && event.tickets.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Ticket Type</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Price</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Quantity</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Sold</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Available</th>
                      </tr>
                    </thead>
                    <tbody>
                      {event.tickets.map((ticket) => (
                        <tr key={ticket.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{ticket.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">${ticket.price.toFixed(2)}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{ticket.quantity}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{ticket.sold}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{ticket.available}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Ticket className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">No Tickets Found</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    This event doesn&apos;t have any ticket types defined yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="attendees">
          <Card>
            <CardHeader>
              <CardTitle>Attendees</CardTitle>
              <CardDescription>
                People who have registered for this event
              </CardDescription>
            </CardHeader>
            <CardContent>
              {event.attendees && event.attendees.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Name</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Email</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Phone</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Ticket Type</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Purchase Date</th>
                        <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">Status</th>
                        <th className="px-4 py-3 text-right text-sm font-medium text-gray-500">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {event.attendees.map((attendee) => (
                        <tr key={attendee.id} className="border-b hover:bg-gray-50">
                          <td className="px-4 py-4 text-sm font-medium text-gray-900">{attendee.name}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{attendee.email}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{attendee.phone}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{attendee.ticketType}</td>
                          <td className="px-4 py-4 text-sm text-gray-500">{formatDateDisplay(attendee.purchaseDate)}</td>
                          <td className="px-4 py-4 text-sm">
                            <Badge className={
                              attendee.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                                attendee.status === 'Pending' ? 'bg-amber-100 text-amber-800' :
                                  'bg-red-100 text-red-800'
                            }>
                              {attendee.status}
                            </Badge>
                          </td>
                          <td className="px-4 py-4 text-sm text-right">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => viewAttendeeDetails(attendee)}
                              className="h-8 w-8 p-0"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <Users className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">No Attendees Yet</h3>
                  <p className="text-gray-500 max-w-md mx-auto">
                    This event doesn&apos;t have any registered attendees yet.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sales">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Sales Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-4 bg-blue-50 rounded-lg">
                    <div>
                      <h3 className="text-sm font-medium text-blue-800">Total Sales</h3>
                      <p className="text-2xl font-bold text-blue-900">${event.sales?.totalSales.toFixed(2) || '0.00'}</p>
                    </div>
                    <CreditCard className="h-10 w-10 text-blue-500" />
                  </div>

                  <div className="flex justify-between items-center p-4 bg-green-50 rounded-lg">
                    <div>
                      <h3 className="text-sm font-medium text-green-800">Tickets Sold</h3>
                      <p className="text-2xl font-bold text-green-900">{event.sales?.totalTicketsSold || 0}</p>
                    </div>
                    <Ticket className="h-10 w-10 text-green-500" />
                  </div>

                  <div className="flex justify-between items-center p-4 bg-purple-50 rounded-lg">
                    <div>
                      <h3 className="text-sm font-medium text-purple-800">Ticket Types</h3>
                      <p className="text-2xl font-bold text-purple-900">{event.tickets?.length || 0}</p>
                    </div>
                    <Ticket className="h-10 w-10 text-purple-500" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full" asChild>
                  <Link href={`/admin/events/${event.id}/sales`}>
                    <CreditCard className="mr-2 h-4 w-4" />
                    View Detailed Sales Report
                  </Link>
                </Button>

                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/admin/events/${event.id}/export`}>
                    <Download className="mr-2 h-4 w-4" />
                    Export Attendee List
                  </Link>
                </Button>

                <Button variant="outline" className="w-full" asChild>
                  <Link href={`/admin/events/${event.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Event
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Attendee Details Dialog */}
      <Dialog open={isAttendeeDialogOpen} onOpenChange={setIsAttendeeDialogOpen}>
        <DialogContent className="max-w-md">
          {selectedAttendee && (
            <>
              <DialogHeader>
                <DialogTitle>Attendee Details</DialogTitle>
                <DialogDescription>
                  Information about this attendee
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Name</h3>
                  <p>{selectedAttendee.name}</p>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Contact Information</h3>
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-500 mr-2" />
                    <p>{selectedAttendee.email}</p>
                  </div>
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-500 mr-2" />
                    <p>{selectedAttendee.phone}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-500">Ticket Information</h3>
                  <p><span className="font-medium">Type:</span> {selectedAttendee.ticketType}</p>
                  <p><span className="font-medium">Price:</span> ${selectedAttendee.ticketPrice.toFixed(2)}</p>
                  <p><span className="font-medium">Purchase Date:</span> {formatDateDisplay(selectedAttendee.purchaseDate)}</p>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">Status:</span>
                    <Badge className={
                      selectedAttendee.status === 'Confirmed' ? 'bg-green-100 text-green-800' :
                        selectedAttendee.status === 'Pending' ? 'bg-amber-100 text-amber-800' :
                          'bg-red-100 text-red-800'
                    }>
                      {selectedAttendee.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAttendeeDialogOpen(false)}>
                  Close
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>

      {/* Update Balance Dialog */}
      <Dialog open={isUpdateBalanceDialogOpen} onOpenChange={setIsUpdateBalanceDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Update Organizer Balance</DialogTitle>
            <DialogDescription>
              Add or subtract from the organizer&apos;s account balance
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="action" className="text-sm font-medium">Action</label>
              <div className="flex space-x-4">
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="add"
                    name="action"
                    className="mr-2"
                    checked={balanceAction === 'add'}
                    onChange={() => setBalanceAction('add')}
                  />
                  <label htmlFor="add">Add Funds</label>
                </div>
                <div className="flex items-center">
                  <input
                    type="radio"
                    id="subtract"
                    name="action"
                    className="mr-2"
                    checked={balanceAction === 'subtract'}
                    onChange={() => setBalanceAction('subtract')}
                  />
                  <label htmlFor="subtract">Subtract Funds</label>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label htmlFor="amount" className="text-sm font-medium">Amount</label>
              <div className="relative">
                <DollarSign className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
                <Input
                  id="amount"
                  type="number"
                  min="0.01"
                  step="0.01"
                  placeholder="0.00"
                  className="pl-8"
                  value={balanceAmount}
                  onChange={(e) => setBalanceAmount(e.target.value)}
                />
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsUpdateBalanceDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateOrganizerBalance}>
              Update Balance
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
