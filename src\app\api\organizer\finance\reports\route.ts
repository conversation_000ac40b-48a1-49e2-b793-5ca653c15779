import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';
import { currentUser } from '@/lib/auth';
import { Prisma } from '@prisma/client';

/**
 * GET /api/organizer/finance/reports
 * Get revenue summary data for organizer's events
 */
export async function GET(request: NextRequest) {
  try {
    // Check if user is authenticated and has organizer role
    const user = await currentUser();

    if (!user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate') ? new Date(searchParams.get('startDate')!) : undefined;
    const endDate = searchParams.get('endDate') ? new Date(searchParams.get('endDate')!) : undefined;
    
    // Build date filter
    const dateFilter: Prisma.OrderWhereInput = {};
    if (startDate || endDate) {
      dateFilter.createdAt = {};
      if (startDate) {
        dateFilter.createdAt.gte = startDate;
      }
      if (endDate) {
        dateFilter.createdAt.lte = endDate;
      }
    }

    // Get all events created by the organizer
    const events = await db.event.findMany({
      where: {
        userId: user.id!
      },
      select: {
        id: true
      }
    });

    const eventIds = events.map(event => event.id);

    // If organizer has no events, return empty data
    if (eventIds.length === 0) {
      return NextResponse.json({
        totalRevenue: 0,
        ticketSales: 0,
        platformFees: 0,
        processingFees: 0,
        netRevenue: 0,
        ticketCount: 0
      });
    }

    // Get ticket sales data for organizer's events
    const ticketSalesData = await db.order.aggregate({
      where: {
        status: 'Completed',
        eventId: {
          in: eventIds
        },
        ...dateFilter
      },
      _sum: {
        totalPrice: true
      },
      _count: {
        id: true
      }
    });

    // Calculate platform fees (6% of ticket sales)
    const platformFees = (ticketSalesData._sum.totalPrice || 0) * 0.06;
    
    // Calculate processing fees (3.5% of ticket sales)
    const processingFees = (ticketSalesData._sum.totalPrice || 0) * 0.035;

    // Calculate net revenue (total - fees)
    const netRevenue = (ticketSalesData._sum.totalPrice || 0) - platformFees - processingFees;

    // Return the revenue summary
    return NextResponse.json({
      totalRevenue: ticketSalesData._sum.totalPrice || 0,
      ticketSales: ticketSalesData._sum.totalPrice || 0,
      platformFees,
      processingFees,
      netRevenue,
      ticketCount: ticketSalesData._count.id || 0
    });
  } catch (error) {
    console.error('Error generating organizer financial reports:', error);
    return NextResponse.json(
      { error: 'Failed to generate financial reports' },
      { status: 500 }
    );
  }
}
